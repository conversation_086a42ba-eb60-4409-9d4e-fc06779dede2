r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Assistants
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, Optional
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class KnowledgeStatusInstance(InstanceResource):
    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Knowledge resource.
    :ivar status: The status of processing the knowledge source ('QUEUED', 'PROCESSING', 'COMPLETED', 'FAILED')
    :ivar last_status: The last status of processing the knowledge source ('QUEUED', 'PROCESSING', 'COMPLETED', 'FAILED')
    :ivar date_updated: The date and time in GMT when the Knowledge was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    """

    def __init__(self, version: Version, payload: Dict[str, Any], id: str):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.status: Optional[str] = payload.get("status")
        self.last_status: Optional[str] = payload.get("last_status")
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )

        self._solution = {
            "id": id,
        }
        self._context: Optional[KnowledgeStatusContext] = None

    @property
    def _proxy(self) -> "KnowledgeStatusContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: KnowledgeStatusContext for this KnowledgeStatusInstance
        """
        if self._context is None:
            self._context = KnowledgeStatusContext(
                self._version,
                id=self._solution["id"],
            )
        return self._context

    def fetch(self) -> "KnowledgeStatusInstance":
        """
        Fetch the KnowledgeStatusInstance


        :returns: The fetched KnowledgeStatusInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "KnowledgeStatusInstance":
        """
        Asynchronous coroutine to fetch the KnowledgeStatusInstance


        :returns: The fetched KnowledgeStatusInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Assistants.V1.KnowledgeStatusInstance {}>".format(context)


class KnowledgeStatusContext(InstanceContext):

    def __init__(self, version: Version, id: str):
        """
        Initialize the KnowledgeStatusContext

        :param version: Version that contains the resource
        :param id: the Knowledge ID.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "id": id,
        }
        self._uri = "/Knowledge/{id}/Status".format(**self._solution)

    def fetch(self) -> KnowledgeStatusInstance:
        """
        Fetch the KnowledgeStatusInstance


        :returns: The fetched KnowledgeStatusInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return KnowledgeStatusInstance(
            self._version,
            payload,
            id=self._solution["id"],
        )

    async def fetch_async(self) -> KnowledgeStatusInstance:
        """
        Asynchronous coroutine to fetch the KnowledgeStatusInstance


        :returns: The fetched KnowledgeStatusInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return KnowledgeStatusInstance(
            self._version,
            payload,
            id=self._solution["id"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Assistants.V1.KnowledgeStatusContext {}>".format(context)


class KnowledgeStatusList(ListResource):

    def __init__(self, version: Version, id: str):
        """
        Initialize the KnowledgeStatusList

        :param version: Version that contains the resource
        :param id: the Knowledge ID.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "id": id,
        }

    def get(self) -> KnowledgeStatusContext:
        """
        Constructs a KnowledgeStatusContext

        """
        return KnowledgeStatusContext(self._version, id=self._solution["id"])

    def __call__(self) -> KnowledgeStatusContext:
        """
        Constructs a KnowledgeStatusContext

        """
        return KnowledgeStatusContext(self._version, id=self._solution["id"])

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Assistants.V1.KnowledgeStatusList>"
