from datetime import datetime
from app.models.report import ReportCategory

def generate_response(report_data, nearby_reports=None, community_groups=None):
    """
    Generate a response message for a WhatsApp report

    Args:
        report_data: The parsed report data
        nearby_reports: List of nearby reports (optional)
        community_groups: List of community groups in the area (optional)

    Returns:
        A formatted response message
    """
    # Basic confirmation message
    short_id = report_data.get("short_id", "UNKNOWN")
    category = report_data.get("category", "")

    response = [
        f"Thank you for your report. Your report ID is: #{short_id}",
        "",
        f"Category: {category.value.upper()}",
        f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
    ]

    # Add location confirmation with Google Maps link
    lat = report_data.get("latitude")
    lng = report_data.get("longitude")
    if lat and lng:
        response.append(f"Location: {lat:.4f}, {lng:.4f}")
        response.append(f"View on map: https://maps.google.com/?q={lat},{lng}")

    # Add nearby incidents if available
    if nearby_reports and len(nearby_reports) > 0:
        response.append("")
        response.append("NEARBY INCIDENTS:")
        for i, report in enumerate(nearby_reports[:3], 1):
            time_diff = datetime.now() - report.timestamp
            hours_ago = int(time_diff.total_seconds() / 3600)
            response.append(f"{i}. {report.category.value.upper()} - {hours_ago}h ago - #{report.short_id}")

    # Add community groups if available
    if community_groups and len(community_groups) > 0:
        response.append("")
        response.append("COMMUNITY GROUPS IN YOUR AREA:")
        for i, group in enumerate(community_groups[:2], 1):
            response.append(f"{i}. {group.name}")

    # Add safety tips based on category
    response.append("")
    response.append("SAFETY TIPS:")

    if category == ReportCategory.CRIME:
        response.extend([
            "• Stay in a safe location and call emergency services if needed",
            "• Do not approach suspicious individuals",
            "• Lock doors and windows if at home"
        ])
    elif category == ReportCategory.EMS:
        response.extend([
            "• If medical emergency, call ambulance: 10177",
            "• For minor injuries, keep the area clean and elevated",
            "• Stay with the injured person until help arrives"
        ])
    elif category == ReportCategory.INFRASTRUCTURE:
        response.extend([
            "• Stay clear of damaged infrastructure",
            "• Report dangerous situations to municipal services",
            "• Avoid contact with exposed wires or pipes"
        ])

    # Add footer
    response.extend([
        "",
        "Your safety is our priority. Thank you for helping keep our community safe.",
        "To follow up on your report, reply with: #status " + short_id
    ])

    return "\n".join(response)
