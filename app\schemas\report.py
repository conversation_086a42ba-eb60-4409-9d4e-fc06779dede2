from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from uuid import UUID
from app.models.report import ReportCategory

class LocationSchema(BaseModel):
    latitude: float
    longitude: float

class ReportBase(BaseModel):
    category: ReportCategory
    description: str
    location: LocationSchema

class ReportCreate(ReportBase):
    image_url: Optional[str] = None
    user_hash: str

class ReportResponse(ReportBase):
    id: UUID
    image_url: Optional[str] = None
    timestamp: datetime
    verified: bool

    class Config:
        from_attributes = True

class WhatsAppMessage(BaseModel):
    From: str
    Body: str
    NumMedia: int = 0
    MediaUrl0: Optional[str] = None
    Latitude: Optional[float] = None
    Longitude: Optional[float] = None

class WhatsAppResponse(BaseModel):
    message: str
