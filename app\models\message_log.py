from sqlalchemy import Column, String, Text, Boolean, DateTime, Float, Enum
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, timezone
import enum

from app.models.database import Base

class MessageStatus(str, enum.Enum):
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"

class MessageType(str, enum.Enum):
    WHATSAPP = "whatsapp"
    SMS = "sms"
    EMAIL = "email"

class MessageLog(Base):
    __tablename__ = "message_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Message details
    message_type = Column(Enum(MessageType), nullable=False)
    recipient = Column(String, nullable=False)  # Phone number or email
    message_content = Column(Text, nullable=False)
    
    # Tracking
    status = Column(Enum(MessageStatus), default=MessageStatus.PENDING)
    sent_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    delivered_at = Column(DateTime, nullable=True)
    failed_at = Column(DateTime, nullable=True)
    
    # Cost tracking
    cost = Column(Float, nullable=False, default=0.0)  # Cost in Rand
    provider = Column(String, nullable=True)  # e.g., "twilio", "clickatell"
    
    # Context
    report_id = Column(UUID(as_uuid=True), nullable=True)  # Related report
    group_id = Column(UUID(as_uuid=True), nullable=True)   # Related group
    
    # Provider response
    provider_message_id = Column(String, nullable=True)  # Provider's message ID
    provider_response = Column(Text, nullable=True)       # Full provider response
    error_message = Column(Text, nullable=True)           # Error details if failed
    
    def __repr__(self):
        return f"<MessageLog(id={self.id}, type={self.message_type}, recipient={self.recipient}, status={self.status})>"
