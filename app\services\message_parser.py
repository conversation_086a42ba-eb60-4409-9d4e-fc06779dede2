import re
import uuid
from app.models.report import ReportCategory, Report
from app.schemas.report import WhatsAppMessage

def parse_whatsapp_message(message: WhatsAppMessage):
    """
    Parse a WhatsApp message to extract report details

    Expected format:
    #category
    Description of the issue
    Location: (shared via WhatsApp or mentioned in text)
    [Optional image]
    """
    # Extract category from the message body
    category_match = re.search(r'#(\w+)', message.Body.lower())
    if not category_match:
        raise ValueError("No category found in message. Please include #crime, #ems, or #infrastructure.")

    category_text = category_match.group(1)

    # Map the category text to our enum
    category_mapping = {
        "crime": ReportCategory.CRIME,
        "ems": ReportCategory.EMS,
        "infrastructure": ReportCategory.INFRASTRUCTURE
    }

    if category_text not in category_mapping:
        raise ValueError(f"Invalid category: {category_text}. Please use #crime, #ems, or #infrastructure.")

    category = category_mapping[category_text]

    # Extract description (everything after the category tag until "Location:" if present)
    description_match = re.search(r'#\w+\s+(.*?)(?:Location:|$)', message.Body, re.DOTALL)
    if not description_match or not description_match.group(1).strip():
        raise ValueError("No description found in message.")

    description = description_match.group(1).strip()

    # Get location from shared location or from text
    latitude = message.Latitude
    longitude = message.Longitude

    if not latitude or not longitude:
        # Try to extract location from text
        location_match = re.search(r'Location:\s*(-?\d+\.\d+),\s*(-?\d+\.\d+)', message.Body)
        if location_match:
            latitude = float(location_match.group(1))
            longitude = float(location_match.group(2))
        else:
            # Try to extract a plain text location
            location_match = re.search(r'Location:\s*(.+?)(?:\n|$)', message.Body)
            if location_match:
                # Store the text location in the description for now
                # In a real app, you would use a geocoding service here
                text_location = location_match.group(1).strip()
                description += f"\n\nLocation mentioned: {text_location}"
                # Use default coordinates for South Africa if no specific coordinates
                latitude = -30.5595
                longitude = 22.9375
            else:
                raise ValueError("No location found. Please share your location or include coordinates.")

    # Get image URL if available
    image_url = message.MediaUrl0 if message.NumMedia > 0 else None

    # Generate a short ID for the report
    short_id = Report.generate_short_id()

    # Hash the phone number for anonymity
    user_hash = Report.hash_phone_number(message.From)

    return {
        "short_id": short_id,
        "category": category,
        "description": description,
        "latitude": latitude,
        "longitude": longitude,
        "image_url": image_url,
        "user_hash": user_hash
    }
