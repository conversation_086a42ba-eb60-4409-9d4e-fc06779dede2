r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Microvisor
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, Optional
from twilio.base import values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class AppManifestInstance(InstanceResource):
    """
    :ivar app_sid: A 34-character string that uniquely identifies this App.
    :ivar hash: App manifest hash represented as `hash_algorithm:hash_value`.
    :ivar encoded_bytes: The base-64 encoded manifest
    :ivar url: The absolute URL of this Manifest.
    """

    def __init__(self, version: Version, payload: Dict[str, Any], app_sid: str):
        super().__init__(version)

        self.app_sid: Optional[str] = payload.get("app_sid")
        self.hash: Optional[str] = payload.get("hash")
        self.encoded_bytes: Optional[str] = payload.get("encoded_bytes")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "app_sid": app_sid,
        }
        self._context: Optional[AppManifestContext] = None

    @property
    def _proxy(self) -> "AppManifestContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: AppManifestContext for this AppManifestInstance
        """
        if self._context is None:
            self._context = AppManifestContext(
                self._version,
                app_sid=self._solution["app_sid"],
            )
        return self._context

    def fetch(self) -> "AppManifestInstance":
        """
        Fetch the AppManifestInstance


        :returns: The fetched AppManifestInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "AppManifestInstance":
        """
        Asynchronous coroutine to fetch the AppManifestInstance


        :returns: The fetched AppManifestInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Microvisor.V1.AppManifestInstance {}>".format(context)


class AppManifestContext(InstanceContext):

    def __init__(self, version: Version, app_sid: str):
        """
        Initialize the AppManifestContext

        :param version: Version that contains the resource
        :param app_sid: A 34-character string that uniquely identifies this App.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "app_sid": app_sid,
        }
        self._uri = "/Apps/{app_sid}/Manifest".format(**self._solution)

    def fetch(self) -> AppManifestInstance:
        """
        Fetch the AppManifestInstance


        :returns: The fetched AppManifestInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return AppManifestInstance(
            self._version,
            payload,
            app_sid=self._solution["app_sid"],
        )

    async def fetch_async(self) -> AppManifestInstance:
        """
        Asynchronous coroutine to fetch the AppManifestInstance


        :returns: The fetched AppManifestInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return AppManifestInstance(
            self._version,
            payload,
            app_sid=self._solution["app_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Microvisor.V1.AppManifestContext {}>".format(context)


class AppManifestList(ListResource):

    def __init__(self, version: Version, app_sid: str):
        """
        Initialize the AppManifestList

        :param version: Version that contains the resource
        :param app_sid: A 34-character string that uniquely identifies this App.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "app_sid": app_sid,
        }

    def get(self) -> AppManifestContext:
        """
        Constructs a AppManifestContext

        """
        return AppManifestContext(self._version, app_sid=self._solution["app_sid"])

    def __call__(self) -> AppManifestContext:
        """
        Constructs a AppManifestContext

        """
        return AppManifestContext(self._version, app_sid=self._solution["app_sid"])

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Microvisor.V1.AppManifestList>"
