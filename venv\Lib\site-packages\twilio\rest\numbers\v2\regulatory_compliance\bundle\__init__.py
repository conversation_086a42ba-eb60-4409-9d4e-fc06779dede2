r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Numbers
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.numbers.v2.regulatory_compliance.bundle.bundle_copy import (
    BundleCopyList,
)
from twilio.rest.numbers.v2.regulatory_compliance.bundle.evaluation import (
    EvaluationList,
)
from twilio.rest.numbers.v2.regulatory_compliance.bundle.item_assignment import (
    ItemAssignmentList,
)
from twilio.rest.numbers.v2.regulatory_compliance.bundle.replace_items import (
    ReplaceItemsList,
)


class BundleInstance(InstanceResource):

    class EndUserType(object):
        INDIVIDUAL = "individual"
        BUSINESS = "business"

    class SortBy(object):
        VALID_UNTIL = "valid-until"
        DATE_UPDATED = "date-updated"

    class SortDirection(object):
        ASC = "ASC"
        DESC = "DESC"

    class Status(object):
        DRAFT = "draft"
        PENDING_REVIEW = "pending-review"
        IN_REVIEW = "in-review"
        TWILIO_REJECTED = "twilio-rejected"
        TWILIO_APPROVED = "twilio-approved"
        PROVISIONALLY_APPROVED = "provisionally-approved"

    """
    :ivar sid: The unique string that we created to identify the Bundle resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Bundle resource.
    :ivar regulation_sid: The unique string of a regulation that is associated to the Bundle resource.
    :ivar friendly_name: The string that you assigned to describe the resource.
    :ivar status: 
    :ivar valid_until: The date and time in GMT in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format when the resource will be valid until.
    :ivar email: The email address that will receive updates when the Bundle resource changes status.
    :ivar status_callback: The URL we call to inform your application of status changes.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The absolute URL of the Bundle resource.
    :ivar links: The URLs of the Assigned Items of the Bundle resource.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.regulation_sid: Optional[str] = payload.get("regulation_sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.status: Optional["BundleInstance.Status"] = payload.get("status")
        self.valid_until: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("valid_until")
        )
        self.email: Optional[str] = payload.get("email")
        self.status_callback: Optional[str] = payload.get("status_callback")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[BundleContext] = None

    @property
    def _proxy(self) -> "BundleContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: BundleContext for this BundleInstance
        """
        if self._context is None:
            self._context = BundleContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the BundleInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the BundleInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "BundleInstance":
        """
        Fetch the BundleInstance


        :returns: The fetched BundleInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "BundleInstance":
        """
        Asynchronous coroutine to fetch the BundleInstance


        :returns: The fetched BundleInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        status: Union["BundleInstance.Status", object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        email: Union[str, object] = values.unset,
    ) -> "BundleInstance":
        """
        Update the BundleInstance

        :param status:
        :param status_callback: The URL we call to inform your application of status changes.
        :param friendly_name: The string that you assigned to describe the resource.
        :param email: The email address that will receive updates when the Bundle resource changes status.

        :returns: The updated BundleInstance
        """
        return self._proxy.update(
            status=status,
            status_callback=status_callback,
            friendly_name=friendly_name,
            email=email,
        )

    async def update_async(
        self,
        status: Union["BundleInstance.Status", object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        email: Union[str, object] = values.unset,
    ) -> "BundleInstance":
        """
        Asynchronous coroutine to update the BundleInstance

        :param status:
        :param status_callback: The URL we call to inform your application of status changes.
        :param friendly_name: The string that you assigned to describe the resource.
        :param email: The email address that will receive updates when the Bundle resource changes status.

        :returns: The updated BundleInstance
        """
        return await self._proxy.update_async(
            status=status,
            status_callback=status_callback,
            friendly_name=friendly_name,
            email=email,
        )

    @property
    def bundle_copies(self) -> BundleCopyList:
        """
        Access the bundle_copies
        """
        return self._proxy.bundle_copies

    @property
    def evaluations(self) -> EvaluationList:
        """
        Access the evaluations
        """
        return self._proxy.evaluations

    @property
    def item_assignments(self) -> ItemAssignmentList:
        """
        Access the item_assignments
        """
        return self._proxy.item_assignments

    @property
    def replace_items(self) -> ReplaceItemsList:
        """
        Access the replace_items
        """
        return self._proxy.replace_items

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Numbers.V2.BundleInstance {}>".format(context)


class BundleContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the BundleContext

        :param version: Version that contains the resource
        :param sid: The unique string that we created to identify the Bundle resource.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/RegulatoryCompliance/Bundles/{sid}".format(**self._solution)

        self._bundle_copies: Optional[BundleCopyList] = None
        self._evaluations: Optional[EvaluationList] = None
        self._item_assignments: Optional[ItemAssignmentList] = None
        self._replace_items: Optional[ReplaceItemsList] = None

    def delete(self) -> bool:
        """
        Deletes the BundleInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the BundleInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> BundleInstance:
        """
        Fetch the BundleInstance


        :returns: The fetched BundleInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return BundleInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> BundleInstance:
        """
        Asynchronous coroutine to fetch the BundleInstance


        :returns: The fetched BundleInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return BundleInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def update(
        self,
        status: Union["BundleInstance.Status", object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        email: Union[str, object] = values.unset,
    ) -> BundleInstance:
        """
        Update the BundleInstance

        :param status:
        :param status_callback: The URL we call to inform your application of status changes.
        :param friendly_name: The string that you assigned to describe the resource.
        :param email: The email address that will receive updates when the Bundle resource changes status.

        :returns: The updated BundleInstance
        """

        data = values.of(
            {
                "Status": status,
                "StatusCallback": status_callback,
                "FriendlyName": friendly_name,
                "Email": email,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return BundleInstance(self._version, payload, sid=self._solution["sid"])

    async def update_async(
        self,
        status: Union["BundleInstance.Status", object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        email: Union[str, object] = values.unset,
    ) -> BundleInstance:
        """
        Asynchronous coroutine to update the BundleInstance

        :param status:
        :param status_callback: The URL we call to inform your application of status changes.
        :param friendly_name: The string that you assigned to describe the resource.
        :param email: The email address that will receive updates when the Bundle resource changes status.

        :returns: The updated BundleInstance
        """

        data = values.of(
            {
                "Status": status,
                "StatusCallback": status_callback,
                "FriendlyName": friendly_name,
                "Email": email,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return BundleInstance(self._version, payload, sid=self._solution["sid"])

    @property
    def bundle_copies(self) -> BundleCopyList:
        """
        Access the bundle_copies
        """
        if self._bundle_copies is None:
            self._bundle_copies = BundleCopyList(
                self._version,
                self._solution["sid"],
            )
        return self._bundle_copies

    @property
    def evaluations(self) -> EvaluationList:
        """
        Access the evaluations
        """
        if self._evaluations is None:
            self._evaluations = EvaluationList(
                self._version,
                self._solution["sid"],
            )
        return self._evaluations

    @property
    def item_assignments(self) -> ItemAssignmentList:
        """
        Access the item_assignments
        """
        if self._item_assignments is None:
            self._item_assignments = ItemAssignmentList(
                self._version,
                self._solution["sid"],
            )
        return self._item_assignments

    @property
    def replace_items(self) -> ReplaceItemsList:
        """
        Access the replace_items
        """
        if self._replace_items is None:
            self._replace_items = ReplaceItemsList(
                self._version,
                self._solution["sid"],
            )
        return self._replace_items

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Numbers.V2.BundleContext {}>".format(context)


class BundlePage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> BundleInstance:
        """
        Build an instance of BundleInstance

        :param payload: Payload response from the API
        """
        return BundleInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Numbers.V2.BundlePage>"


class BundleList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the BundleList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/RegulatoryCompliance/Bundles"

    def create(
        self,
        friendly_name: str,
        email: str,
        status_callback: Union[str, object] = values.unset,
        regulation_sid: Union[str, object] = values.unset,
        iso_country: Union[str, object] = values.unset,
        end_user_type: Union["BundleInstance.EndUserType", object] = values.unset,
        number_type: Union[str, object] = values.unset,
        is_test: Union[bool, object] = values.unset,
    ) -> BundleInstance:
        """
        Create the BundleInstance

        :param friendly_name: The string that you assigned to describe the resource.
        :param email: The email address that will receive updates when the Bundle resource changes status.
        :param status_callback: The URL we call to inform your application of status changes.
        :param regulation_sid: The unique string of a regulation that is associated to the Bundle resource.
        :param iso_country: The [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the Bundle's phone number country ownership request.
        :param end_user_type:
        :param number_type: The type of phone number of the Bundle's ownership request. Can be `local`, `mobile`, `national`, or `toll-free`.
        :param is_test: Indicates that Bundle is a Test Bundle and will be Auto-Rejected

        :returns: The created BundleInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "Email": email,
                "StatusCallback": status_callback,
                "RegulationSid": regulation_sid,
                "IsoCountry": iso_country,
                "EndUserType": end_user_type,
                "NumberType": number_type,
                "IsTest": serialize.boolean_to_string(is_test),
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return BundleInstance(self._version, payload)

    async def create_async(
        self,
        friendly_name: str,
        email: str,
        status_callback: Union[str, object] = values.unset,
        regulation_sid: Union[str, object] = values.unset,
        iso_country: Union[str, object] = values.unset,
        end_user_type: Union["BundleInstance.EndUserType", object] = values.unset,
        number_type: Union[str, object] = values.unset,
        is_test: Union[bool, object] = values.unset,
    ) -> BundleInstance:
        """
        Asynchronously create the BundleInstance

        :param friendly_name: The string that you assigned to describe the resource.
        :param email: The email address that will receive updates when the Bundle resource changes status.
        :param status_callback: The URL we call to inform your application of status changes.
        :param regulation_sid: The unique string of a regulation that is associated to the Bundle resource.
        :param iso_country: The [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the Bundle's phone number country ownership request.
        :param end_user_type:
        :param number_type: The type of phone number of the Bundle's ownership request. Can be `local`, `mobile`, `national`, or `toll-free`.
        :param is_test: Indicates that Bundle is a Test Bundle and will be Auto-Rejected

        :returns: The created BundleInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "Email": email,
                "StatusCallback": status_callback,
                "RegulationSid": regulation_sid,
                "IsoCountry": iso_country,
                "EndUserType": end_user_type,
                "NumberType": number_type,
                "IsTest": serialize.boolean_to_string(is_test),
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return BundleInstance(self._version, payload)

    def stream(
        self,
        status: Union["BundleInstance.Status", object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        regulation_sid: Union[str, object] = values.unset,
        iso_country: Union[str, object] = values.unset,
        number_type: Union[str, object] = values.unset,
        has_valid_until_date: Union[bool, object] = values.unset,
        sort_by: Union["BundleInstance.SortBy", object] = values.unset,
        sort_direction: Union["BundleInstance.SortDirection", object] = values.unset,
        valid_until_date: Union[datetime, object] = values.unset,
        valid_until_date_before: Union[datetime, object] = values.unset,
        valid_until_date_after: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[BundleInstance]:
        """
        Streams BundleInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param &quot;BundleInstance.Status&quot; status: The verification status of the Bundle resource. Please refer to [Bundle Statuses](https://www.twilio.com/docs/phone-numbers/regulatory/api/bundles#bundle-statuses) for more details.
        :param str friendly_name: The string that you assigned to describe the resource. The column can contain 255 variable characters.
        :param str regulation_sid: The unique string of a [Regulation resource](https://www.twilio.com/docs/phone-numbers/regulatory/api/regulations) that is associated to the Bundle resource.
        :param str iso_country: The 2-digit [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the Bundle's phone number country ownership request.
        :param str number_type: The type of phone number of the Bundle's ownership request. Can be `local`, `mobile`, `national`, or `toll-free`.
        :param bool has_valid_until_date: Indicates that the Bundle is a valid Bundle until a specified expiration date.
        :param &quot;BundleInstance.SortBy&quot; sort_by: Can be `valid-until` or `date-updated`. Defaults to `date-created`.
        :param &quot;BundleInstance.SortDirection&quot; sort_direction: Default is `DESC`. Can be `ASC` or `DESC`.
        :param datetime valid_until_date: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param datetime valid_until_date_before: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param datetime valid_until_date_after: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            status=status,
            friendly_name=friendly_name,
            regulation_sid=regulation_sid,
            iso_country=iso_country,
            number_type=number_type,
            has_valid_until_date=has_valid_until_date,
            sort_by=sort_by,
            sort_direction=sort_direction,
            valid_until_date=valid_until_date,
            valid_until_date_before=valid_until_date_before,
            valid_until_date_after=valid_until_date_after,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        status: Union["BundleInstance.Status", object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        regulation_sid: Union[str, object] = values.unset,
        iso_country: Union[str, object] = values.unset,
        number_type: Union[str, object] = values.unset,
        has_valid_until_date: Union[bool, object] = values.unset,
        sort_by: Union["BundleInstance.SortBy", object] = values.unset,
        sort_direction: Union["BundleInstance.SortDirection", object] = values.unset,
        valid_until_date: Union[datetime, object] = values.unset,
        valid_until_date_before: Union[datetime, object] = values.unset,
        valid_until_date_after: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[BundleInstance]:
        """
        Asynchronously streams BundleInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param &quot;BundleInstance.Status&quot; status: The verification status of the Bundle resource. Please refer to [Bundle Statuses](https://www.twilio.com/docs/phone-numbers/regulatory/api/bundles#bundle-statuses) for more details.
        :param str friendly_name: The string that you assigned to describe the resource. The column can contain 255 variable characters.
        :param str regulation_sid: The unique string of a [Regulation resource](https://www.twilio.com/docs/phone-numbers/regulatory/api/regulations) that is associated to the Bundle resource.
        :param str iso_country: The 2-digit [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the Bundle's phone number country ownership request.
        :param str number_type: The type of phone number of the Bundle's ownership request. Can be `local`, `mobile`, `national`, or `toll-free`.
        :param bool has_valid_until_date: Indicates that the Bundle is a valid Bundle until a specified expiration date.
        :param &quot;BundleInstance.SortBy&quot; sort_by: Can be `valid-until` or `date-updated`. Defaults to `date-created`.
        :param &quot;BundleInstance.SortDirection&quot; sort_direction: Default is `DESC`. Can be `ASC` or `DESC`.
        :param datetime valid_until_date: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param datetime valid_until_date_before: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param datetime valid_until_date_after: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            status=status,
            friendly_name=friendly_name,
            regulation_sid=regulation_sid,
            iso_country=iso_country,
            number_type=number_type,
            has_valid_until_date=has_valid_until_date,
            sort_by=sort_by,
            sort_direction=sort_direction,
            valid_until_date=valid_until_date,
            valid_until_date_before=valid_until_date_before,
            valid_until_date_after=valid_until_date_after,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        status: Union["BundleInstance.Status", object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        regulation_sid: Union[str, object] = values.unset,
        iso_country: Union[str, object] = values.unset,
        number_type: Union[str, object] = values.unset,
        has_valid_until_date: Union[bool, object] = values.unset,
        sort_by: Union["BundleInstance.SortBy", object] = values.unset,
        sort_direction: Union["BundleInstance.SortDirection", object] = values.unset,
        valid_until_date: Union[datetime, object] = values.unset,
        valid_until_date_before: Union[datetime, object] = values.unset,
        valid_until_date_after: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[BundleInstance]:
        """
        Lists BundleInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param &quot;BundleInstance.Status&quot; status: The verification status of the Bundle resource. Please refer to [Bundle Statuses](https://www.twilio.com/docs/phone-numbers/regulatory/api/bundles#bundle-statuses) for more details.
        :param str friendly_name: The string that you assigned to describe the resource. The column can contain 255 variable characters.
        :param str regulation_sid: The unique string of a [Regulation resource](https://www.twilio.com/docs/phone-numbers/regulatory/api/regulations) that is associated to the Bundle resource.
        :param str iso_country: The 2-digit [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the Bundle's phone number country ownership request.
        :param str number_type: The type of phone number of the Bundle's ownership request. Can be `local`, `mobile`, `national`, or `toll-free`.
        :param bool has_valid_until_date: Indicates that the Bundle is a valid Bundle until a specified expiration date.
        :param &quot;BundleInstance.SortBy&quot; sort_by: Can be `valid-until` or `date-updated`. Defaults to `date-created`.
        :param &quot;BundleInstance.SortDirection&quot; sort_direction: Default is `DESC`. Can be `ASC` or `DESC`.
        :param datetime valid_until_date: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param datetime valid_until_date_before: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param datetime valid_until_date_after: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                status=status,
                friendly_name=friendly_name,
                regulation_sid=regulation_sid,
                iso_country=iso_country,
                number_type=number_type,
                has_valid_until_date=has_valid_until_date,
                sort_by=sort_by,
                sort_direction=sort_direction,
                valid_until_date=valid_until_date,
                valid_until_date_before=valid_until_date_before,
                valid_until_date_after=valid_until_date_after,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        status: Union["BundleInstance.Status", object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        regulation_sid: Union[str, object] = values.unset,
        iso_country: Union[str, object] = values.unset,
        number_type: Union[str, object] = values.unset,
        has_valid_until_date: Union[bool, object] = values.unset,
        sort_by: Union["BundleInstance.SortBy", object] = values.unset,
        sort_direction: Union["BundleInstance.SortDirection", object] = values.unset,
        valid_until_date: Union[datetime, object] = values.unset,
        valid_until_date_before: Union[datetime, object] = values.unset,
        valid_until_date_after: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[BundleInstance]:
        """
        Asynchronously lists BundleInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param &quot;BundleInstance.Status&quot; status: The verification status of the Bundle resource. Please refer to [Bundle Statuses](https://www.twilio.com/docs/phone-numbers/regulatory/api/bundles#bundle-statuses) for more details.
        :param str friendly_name: The string that you assigned to describe the resource. The column can contain 255 variable characters.
        :param str regulation_sid: The unique string of a [Regulation resource](https://www.twilio.com/docs/phone-numbers/regulatory/api/regulations) that is associated to the Bundle resource.
        :param str iso_country: The 2-digit [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the Bundle's phone number country ownership request.
        :param str number_type: The type of phone number of the Bundle's ownership request. Can be `local`, `mobile`, `national`, or `toll-free`.
        :param bool has_valid_until_date: Indicates that the Bundle is a valid Bundle until a specified expiration date.
        :param &quot;BundleInstance.SortBy&quot; sort_by: Can be `valid-until` or `date-updated`. Defaults to `date-created`.
        :param &quot;BundleInstance.SortDirection&quot; sort_direction: Default is `DESC`. Can be `ASC` or `DESC`.
        :param datetime valid_until_date: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param datetime valid_until_date_before: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param datetime valid_until_date_after: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                status=status,
                friendly_name=friendly_name,
                regulation_sid=regulation_sid,
                iso_country=iso_country,
                number_type=number_type,
                has_valid_until_date=has_valid_until_date,
                sort_by=sort_by,
                sort_direction=sort_direction,
                valid_until_date=valid_until_date,
                valid_until_date_before=valid_until_date_before,
                valid_until_date_after=valid_until_date_after,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        status: Union["BundleInstance.Status", object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        regulation_sid: Union[str, object] = values.unset,
        iso_country: Union[str, object] = values.unset,
        number_type: Union[str, object] = values.unset,
        has_valid_until_date: Union[bool, object] = values.unset,
        sort_by: Union["BundleInstance.SortBy", object] = values.unset,
        sort_direction: Union["BundleInstance.SortDirection", object] = values.unset,
        valid_until_date: Union[datetime, object] = values.unset,
        valid_until_date_before: Union[datetime, object] = values.unset,
        valid_until_date_after: Union[datetime, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> BundlePage:
        """
        Retrieve a single page of BundleInstance records from the API.
        Request is executed immediately

        :param status: The verification status of the Bundle resource. Please refer to [Bundle Statuses](https://www.twilio.com/docs/phone-numbers/regulatory/api/bundles#bundle-statuses) for more details.
        :param friendly_name: The string that you assigned to describe the resource. The column can contain 255 variable characters.
        :param regulation_sid: The unique string of a [Regulation resource](https://www.twilio.com/docs/phone-numbers/regulatory/api/regulations) that is associated to the Bundle resource.
        :param iso_country: The 2-digit [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the Bundle's phone number country ownership request.
        :param number_type: The type of phone number of the Bundle's ownership request. Can be `local`, `mobile`, `national`, or `toll-free`.
        :param has_valid_until_date: Indicates that the Bundle is a valid Bundle until a specified expiration date.
        :param sort_by: Can be `valid-until` or `date-updated`. Defaults to `date-created`.
        :param sort_direction: Default is `DESC`. Can be `ASC` or `DESC`.
        :param valid_until_date: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param valid_until_date_before: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param valid_until_date_after: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of BundleInstance
        """
        data = values.of(
            {
                "Status": status,
                "FriendlyName": friendly_name,
                "RegulationSid": regulation_sid,
                "IsoCountry": iso_country,
                "NumberType": number_type,
                "HasValidUntilDate": serialize.boolean_to_string(has_valid_until_date),
                "SortBy": sort_by,
                "SortDirection": sort_direction,
                "ValidUntilDate": serialize.iso8601_datetime(valid_until_date),
                "ValidUntilDate<": serialize.iso8601_datetime(valid_until_date_before),
                "ValidUntilDate>": serialize.iso8601_datetime(valid_until_date_after),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return BundlePage(self._version, response)

    async def page_async(
        self,
        status: Union["BundleInstance.Status", object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        regulation_sid: Union[str, object] = values.unset,
        iso_country: Union[str, object] = values.unset,
        number_type: Union[str, object] = values.unset,
        has_valid_until_date: Union[bool, object] = values.unset,
        sort_by: Union["BundleInstance.SortBy", object] = values.unset,
        sort_direction: Union["BundleInstance.SortDirection", object] = values.unset,
        valid_until_date: Union[datetime, object] = values.unset,
        valid_until_date_before: Union[datetime, object] = values.unset,
        valid_until_date_after: Union[datetime, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> BundlePage:
        """
        Asynchronously retrieve a single page of BundleInstance records from the API.
        Request is executed immediately

        :param status: The verification status of the Bundle resource. Please refer to [Bundle Statuses](https://www.twilio.com/docs/phone-numbers/regulatory/api/bundles#bundle-statuses) for more details.
        :param friendly_name: The string that you assigned to describe the resource. The column can contain 255 variable characters.
        :param regulation_sid: The unique string of a [Regulation resource](https://www.twilio.com/docs/phone-numbers/regulatory/api/regulations) that is associated to the Bundle resource.
        :param iso_country: The 2-digit [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the Bundle's phone number country ownership request.
        :param number_type: The type of phone number of the Bundle's ownership request. Can be `local`, `mobile`, `national`, or `toll-free`.
        :param has_valid_until_date: Indicates that the Bundle is a valid Bundle until a specified expiration date.
        :param sort_by: Can be `valid-until` or `date-updated`. Defaults to `date-created`.
        :param sort_direction: Default is `DESC`. Can be `ASC` or `DESC`.
        :param valid_until_date: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param valid_until_date_before: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param valid_until_date_after: Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well. [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) is the acceptable date format.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of BundleInstance
        """
        data = values.of(
            {
                "Status": status,
                "FriendlyName": friendly_name,
                "RegulationSid": regulation_sid,
                "IsoCountry": iso_country,
                "NumberType": number_type,
                "HasValidUntilDate": serialize.boolean_to_string(has_valid_until_date),
                "SortBy": sort_by,
                "SortDirection": sort_direction,
                "ValidUntilDate": serialize.iso8601_datetime(valid_until_date),
                "ValidUntilDate<": serialize.iso8601_datetime(valid_until_date_before),
                "ValidUntilDate>": serialize.iso8601_datetime(valid_until_date_after),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return BundlePage(self._version, response)

    def get_page(self, target_url: str) -> BundlePage:
        """
        Retrieve a specific page of BundleInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of BundleInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return BundlePage(self._version, response)

    async def get_page_async(self, target_url: str) -> BundlePage:
        """
        Asynchronously retrieve a specific page of BundleInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of BundleInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return BundlePage(self._version, response)

    def get(self, sid: str) -> BundleContext:
        """
        Constructs a BundleContext

        :param sid: The unique string that we created to identify the Bundle resource.
        """
        return BundleContext(self._version, sid=sid)

    def __call__(self, sid: str) -> BundleContext:
        """
        Constructs a BundleContext

        :param sid: The unique string that we created to identify the Bundle resource.
        """
        return BundleContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Numbers.V2.BundleList>"
