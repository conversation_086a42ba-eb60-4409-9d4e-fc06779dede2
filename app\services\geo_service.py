from sqlalchemy.orm import Session
import math
from app.models.report import Report
from app.models.group import CommunityGroup

def haversine_distance(lat1, lon1, lat2, lon2):
    """
    Calculate the great circle distance between two points
    on the earth (specified in decimal degrees)
    """
    # Convert decimal degrees to radians
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])

    # Haversine formula
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    r = 6371000  # Radius of earth in meters
    return c * r

def find_nearby_reports(db: Session, latitude: float, longitude: float, distance_meters: int = 1000, limit: int = 5):
    """
    Find reports near a given location using Python-based distance calculation

    Args:
        db: Database session
        latitude: Latitude of the point
        longitude: Longitude of the point
        distance_meters: Search radius in meters
        limit: Maximum number of reports to return

    Returns:
        List of nearby reports
    """
    # Get all reports ordered by timestamp
    all_reports = db.query(Report).order_by(Report.timestamp.desc()).all()

    # Filter reports by distance
    nearby_reports = []
    for report in all_reports:
        distance = haversine_distance(
            latitude, longitude,
            report.latitude, report.longitude
        )

        if distance <= distance_meters:
            nearby_reports.append(report)

            # Stop once we have enough reports
            if len(nearby_reports) >= limit:
                break

    return nearby_reports

def point_in_polygon(point, polygon):
    """
    Check if a point is inside a polygon using the ray casting algorithm

    Args:
        point: [longitude, latitude]
        polygon: List of [longitude, latitude] points forming a polygon

    Returns:
        Boolean indicating whether the point is inside the polygon
    """
    x, y = point
    n = len(polygon)
    inside = False

    p1x, p1y = polygon[0]
    for i in range(1, n + 1):
        p2x, p2y = polygon[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= xinters:
                        inside = not inside
        p1x, p1y = p2x, p2y

    return inside

def find_community_groups_for_location(db: Session, latitude: float, longitude: float):
    """
    Find community groups that cover a given location using Python-based point-in-polygon check

    Args:
        db: Database session
        latitude: Latitude of the point
        longitude: Longitude of the point

    Returns:
        List of community groups
    """
    # Get all active groups
    all_groups = db.query(CommunityGroup).filter(CommunityGroup.active == True).all()

    # Filter groups by whether they contain the point
    matching_groups = []
    point = [longitude, latitude]

    for group in all_groups:
        polygon = group.area_coordinates
        if polygon and point_in_polygon(point, polygon):
            matching_groups.append(group)

    return matching_groups

def should_notify_group(group, report):
    """
    Determine if a group should be notified about a report

    Args:
        group: The community group
        report: The report

    Returns:
        Boolean indicating whether to notify
    """
    print('geo service - should_notify_group')
    # Check if the group is interested in this category
    if not group.categories or report.category.value in group.categories:
        return True

    return False
