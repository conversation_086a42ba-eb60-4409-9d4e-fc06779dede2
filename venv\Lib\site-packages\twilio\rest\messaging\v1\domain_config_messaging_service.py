r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Messaging
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, Optional
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class DomainConfigMessagingServiceInstance(InstanceResource):
    """
    :ivar domain_sid: The unique string that we created to identify the Domain resource.
    :ivar config_sid: The unique string that we created to identify the Domain config (prefix ZK).
    :ivar messaging_service_sid: The unique string that identifies the messaging service
    :ivar fallback_url: Any requests we receive to this domain that do not match an existing shortened message will be redirected to the fallback url. These will likely be either expired messages, random misdirected traffic, or intentional scraping.
    :ivar callback_url: URL to receive click events to your webhook whenever the recipients click on the shortened links.
    :ivar continue_on_failure: Boolean field to set customer delivery preference when there is a failure in linkShortening service
    :ivar date_created: Date this Domain Config was created.
    :ivar date_updated: Date that this Domain Config was last updated.
    :ivar url:
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        messaging_service_sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.domain_sid: Optional[str] = payload.get("domain_sid")
        self.config_sid: Optional[str] = payload.get("config_sid")
        self.messaging_service_sid: Optional[str] = payload.get("messaging_service_sid")
        self.fallback_url: Optional[str] = payload.get("fallback_url")
        self.callback_url: Optional[str] = payload.get("callback_url")
        self.continue_on_failure: Optional[bool] = payload.get("continue_on_failure")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "messaging_service_sid": messaging_service_sid
            or self.messaging_service_sid,
        }
        self._context: Optional[DomainConfigMessagingServiceContext] = None

    @property
    def _proxy(self) -> "DomainConfigMessagingServiceContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: DomainConfigMessagingServiceContext for this DomainConfigMessagingServiceInstance
        """
        if self._context is None:
            self._context = DomainConfigMessagingServiceContext(
                self._version,
                messaging_service_sid=self._solution["messaging_service_sid"],
            )
        return self._context

    def fetch(self) -> "DomainConfigMessagingServiceInstance":
        """
        Fetch the DomainConfigMessagingServiceInstance


        :returns: The fetched DomainConfigMessagingServiceInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "DomainConfigMessagingServiceInstance":
        """
        Asynchronous coroutine to fetch the DomainConfigMessagingServiceInstance


        :returns: The fetched DomainConfigMessagingServiceInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Messaging.V1.DomainConfigMessagingServiceInstance {}>".format(
            context
        )


class DomainConfigMessagingServiceContext(InstanceContext):

    def __init__(self, version: Version, messaging_service_sid: str):
        """
        Initialize the DomainConfigMessagingServiceContext

        :param version: Version that contains the resource
        :param messaging_service_sid: Unique string used to identify the Messaging service that this domain should be associated with.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "messaging_service_sid": messaging_service_sid,
        }
        self._uri = "/LinkShortening/MessagingService/{messaging_service_sid}/DomainConfig".format(
            **self._solution
        )

    def fetch(self) -> DomainConfigMessagingServiceInstance:
        """
        Fetch the DomainConfigMessagingServiceInstance


        :returns: The fetched DomainConfigMessagingServiceInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return DomainConfigMessagingServiceInstance(
            self._version,
            payload,
            messaging_service_sid=self._solution["messaging_service_sid"],
        )

    async def fetch_async(self) -> DomainConfigMessagingServiceInstance:
        """
        Asynchronous coroutine to fetch the DomainConfigMessagingServiceInstance


        :returns: The fetched DomainConfigMessagingServiceInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return DomainConfigMessagingServiceInstance(
            self._version,
            payload,
            messaging_service_sid=self._solution["messaging_service_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Messaging.V1.DomainConfigMessagingServiceContext {}>".format(
            context
        )


class DomainConfigMessagingServiceList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the DomainConfigMessagingServiceList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self, messaging_service_sid: str) -> DomainConfigMessagingServiceContext:
        """
        Constructs a DomainConfigMessagingServiceContext

        :param messaging_service_sid: Unique string used to identify the Messaging service that this domain should be associated with.
        """
        return DomainConfigMessagingServiceContext(
            self._version, messaging_service_sid=messaging_service_sid
        )

    def __call__(
        self, messaging_service_sid: str
    ) -> DomainConfigMessagingServiceContext:
        """
        Constructs a DomainConfigMessagingServiceContext

        :param messaging_service_sid: Unique string used to identify the Messaging service that this domain should be associated with.
        """
        return DomainConfigMessagingServiceContext(
            self._version, messaging_service_sid=messaging_service_sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Messaging.V1.DomainConfigMessagingServiceList>"
