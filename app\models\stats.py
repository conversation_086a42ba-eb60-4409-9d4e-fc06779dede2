from sqlalchemy import Column, String, Integer, Float, DateTime
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, timezone

from app.models.database import Base

class PlatformStats(Base):
    __tablename__ = "platform_stats"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Stat identification
    stat_name = Column(String, nullable=False, unique=True)  # e.g., "total_messages_sent"
    
    # Values
    int_value = Column(Integer, default=0)      # For counts
    float_value = Column(Float, default=0.0)    # For costs
    string_value = Column(String, nullable=True) # For text values
    
    # Tracking
    last_updated = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    
    def __repr__(self):
        return f"<PlatformStats(stat_name={self.stat_name}, int_value={self.int_value}, float_value={self.float_value})>"
