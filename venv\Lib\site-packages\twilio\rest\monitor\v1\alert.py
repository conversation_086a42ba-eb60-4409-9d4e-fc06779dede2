r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Monitor
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class AlertInstance(InstanceResource):
    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Alert resource.
    :ivar alert_text: The text of the alert.
    :ivar api_version: The API version used when the alert was generated.  Can be empty for events that don't have a specific API version.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_generated: The date and time in GMT when the alert was generated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#UTC) format.  Due to buffering, this can be different than `date_created`.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar error_code: The error code for the condition that generated the alert. See the [Error Dictionary](https://www.twilio.com/docs/api/errors) for possible causes and solutions to the error.
    :ivar log_level: The log level.  Can be: `error`, `warning`, `notice`, or `debug`.
    :ivar more_info: The URL of the page in our [Error Dictionary](https://www.twilio.com/docs/api/errors) with more information about the error condition.
    :ivar request_method: The method used by the request that generated the alert. If the alert was generated by a request we made to your server, this is the method we used. If the alert was generated by a request from your application to our API, this is the method your application used.
    :ivar request_url: The URL of the request that generated the alert. If the alert was generated by a request we made to your server, this is the URL on your server that generated the alert. If the alert was generated by a request from your application to our API, this is the URL of the resource requested.
    :ivar request_variables: The variables passed in the request that generated the alert. This value is only returned when a single Alert resource is fetched.
    :ivar resource_sid: The SID of the resource for which the alert was generated.  For instance, if your server failed to respond to an HTTP request during the flow of a particular call, this value would be the SID of the server.  This value is empty if the alert was not generated for a particular resource.
    :ivar response_body: The response body of the request that generated the alert. This value is only returned when a single Alert resource is fetched.
    :ivar response_headers: The response headers of the request that generated the alert. This value is only returned when a single Alert resource is fetched.
    :ivar sid: The unique string that we created to identify the Alert resource.
    :ivar url: The absolute URL of the Alert resource.
    :ivar request_headers: The request headers of the request that generated the alert. This value is only returned when a single Alert resource is fetched.
    :ivar service_sid: The SID of the service or resource that generated the alert. Can be `null`.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.alert_text: Optional[str] = payload.get("alert_text")
        self.api_version: Optional[str] = payload.get("api_version")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_generated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_generated")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.error_code: Optional[str] = payload.get("error_code")
        self.log_level: Optional[str] = payload.get("log_level")
        self.more_info: Optional[str] = payload.get("more_info")
        self.request_method: Optional[str] = payload.get("request_method")
        self.request_url: Optional[str] = payload.get("request_url")
        self.request_variables: Optional[str] = payload.get("request_variables")
        self.resource_sid: Optional[str] = payload.get("resource_sid")
        self.response_body: Optional[str] = payload.get("response_body")
        self.response_headers: Optional[str] = payload.get("response_headers")
        self.sid: Optional[str] = payload.get("sid")
        self.url: Optional[str] = payload.get("url")
        self.request_headers: Optional[str] = payload.get("request_headers")
        self.service_sid: Optional[str] = payload.get("service_sid")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[AlertContext] = None

    @property
    def _proxy(self) -> "AlertContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: AlertContext for this AlertInstance
        """
        if self._context is None:
            self._context = AlertContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def fetch(self) -> "AlertInstance":
        """
        Fetch the AlertInstance


        :returns: The fetched AlertInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "AlertInstance":
        """
        Asynchronous coroutine to fetch the AlertInstance


        :returns: The fetched AlertInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Monitor.V1.AlertInstance {}>".format(context)


class AlertContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the AlertContext

        :param version: Version that contains the resource
        :param sid: The SID of the Alert resource to fetch.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/Alerts/{sid}".format(**self._solution)

    def fetch(self) -> AlertInstance:
        """
        Fetch the AlertInstance


        :returns: The fetched AlertInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return AlertInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> AlertInstance:
        """
        Asynchronous coroutine to fetch the AlertInstance


        :returns: The fetched AlertInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return AlertInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Monitor.V1.AlertContext {}>".format(context)


class AlertPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> AlertInstance:
        """
        Build an instance of AlertInstance

        :param payload: Payload response from the API
        """
        return AlertInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Monitor.V1.AlertPage>"


class AlertList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the AlertList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Alerts"

    def stream(
        self,
        log_level: Union[str, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[AlertInstance]:
        """
        Streams AlertInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str log_level: Only show alerts for this log-level.  Can be: `error`, `warning`, `notice`, or `debug`.
        :param datetime start_date: Only include alerts that occurred on or after this date and time. Specify the date and time in GMT and format as `YYYY-MM-DD` or `YYYY-MM-DDThh:mm:ssZ`. Queries for alerts older than 30 days are not supported.
        :param datetime end_date: Only include alerts that occurred on or before this date and time. Specify the date and time in GMT and format as `YYYY-MM-DD` or `YYYY-MM-DDThh:mm:ssZ`. Queries for alerts older than 30 days are not supported.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            log_level=log_level,
            start_date=start_date,
            end_date=end_date,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        log_level: Union[str, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[AlertInstance]:
        """
        Asynchronously streams AlertInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str log_level: Only show alerts for this log-level.  Can be: `error`, `warning`, `notice`, or `debug`.
        :param datetime start_date: Only include alerts that occurred on or after this date and time. Specify the date and time in GMT and format as `YYYY-MM-DD` or `YYYY-MM-DDThh:mm:ssZ`. Queries for alerts older than 30 days are not supported.
        :param datetime end_date: Only include alerts that occurred on or before this date and time. Specify the date and time in GMT and format as `YYYY-MM-DD` or `YYYY-MM-DDThh:mm:ssZ`. Queries for alerts older than 30 days are not supported.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            log_level=log_level,
            start_date=start_date,
            end_date=end_date,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        log_level: Union[str, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[AlertInstance]:
        """
        Lists AlertInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str log_level: Only show alerts for this log-level.  Can be: `error`, `warning`, `notice`, or `debug`.
        :param datetime start_date: Only include alerts that occurred on or after this date and time. Specify the date and time in GMT and format as `YYYY-MM-DD` or `YYYY-MM-DDThh:mm:ssZ`. Queries for alerts older than 30 days are not supported.
        :param datetime end_date: Only include alerts that occurred on or before this date and time. Specify the date and time in GMT and format as `YYYY-MM-DD` or `YYYY-MM-DDThh:mm:ssZ`. Queries for alerts older than 30 days are not supported.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                log_level=log_level,
                start_date=start_date,
                end_date=end_date,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        log_level: Union[str, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[AlertInstance]:
        """
        Asynchronously lists AlertInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str log_level: Only show alerts for this log-level.  Can be: `error`, `warning`, `notice`, or `debug`.
        :param datetime start_date: Only include alerts that occurred on or after this date and time. Specify the date and time in GMT and format as `YYYY-MM-DD` or `YYYY-MM-DDThh:mm:ssZ`. Queries for alerts older than 30 days are not supported.
        :param datetime end_date: Only include alerts that occurred on or before this date and time. Specify the date and time in GMT and format as `YYYY-MM-DD` or `YYYY-MM-DDThh:mm:ssZ`. Queries for alerts older than 30 days are not supported.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                log_level=log_level,
                start_date=start_date,
                end_date=end_date,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        log_level: Union[str, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> AlertPage:
        """
        Retrieve a single page of AlertInstance records from the API.
        Request is executed immediately

        :param log_level: Only show alerts for this log-level.  Can be: `error`, `warning`, `notice`, or `debug`.
        :param start_date: Only include alerts that occurred on or after this date and time. Specify the date and time in GMT and format as `YYYY-MM-DD` or `YYYY-MM-DDThh:mm:ssZ`. Queries for alerts older than 30 days are not supported.
        :param end_date: Only include alerts that occurred on or before this date and time. Specify the date and time in GMT and format as `YYYY-MM-DD` or `YYYY-MM-DDThh:mm:ssZ`. Queries for alerts older than 30 days are not supported.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of AlertInstance
        """
        data = values.of(
            {
                "LogLevel": log_level,
                "StartDate": serialize.iso8601_datetime(start_date),
                "EndDate": serialize.iso8601_datetime(end_date),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return AlertPage(self._version, response)

    async def page_async(
        self,
        log_level: Union[str, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> AlertPage:
        """
        Asynchronously retrieve a single page of AlertInstance records from the API.
        Request is executed immediately

        :param log_level: Only show alerts for this log-level.  Can be: `error`, `warning`, `notice`, or `debug`.
        :param start_date: Only include alerts that occurred on or after this date and time. Specify the date and time in GMT and format as `YYYY-MM-DD` or `YYYY-MM-DDThh:mm:ssZ`. Queries for alerts older than 30 days are not supported.
        :param end_date: Only include alerts that occurred on or before this date and time. Specify the date and time in GMT and format as `YYYY-MM-DD` or `YYYY-MM-DDThh:mm:ssZ`. Queries for alerts older than 30 days are not supported.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of AlertInstance
        """
        data = values.of(
            {
                "LogLevel": log_level,
                "StartDate": serialize.iso8601_datetime(start_date),
                "EndDate": serialize.iso8601_datetime(end_date),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return AlertPage(self._version, response)

    def get_page(self, target_url: str) -> AlertPage:
        """
        Retrieve a specific page of AlertInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of AlertInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return AlertPage(self._version, response)

    async def get_page_async(self, target_url: str) -> AlertPage:
        """
        Asynchronously retrieve a specific page of AlertInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of AlertInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return AlertPage(self._version, response)

    def get(self, sid: str) -> AlertContext:
        """
        Constructs a AlertContext

        :param sid: The SID of the Alert resource to fetch.
        """
        return AlertContext(self._version, sid=sid)

    def __call__(self, sid: str) -> AlertContext:
        """
        Constructs a AlertContext

        :param sid: The SID of the Alert resource to fetch.
        """
        return AlertContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Monitor.V1.AlertList>"
