from sqlalchemy import Column, String, Text, DateTime
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.models.database import Base

class Feedback(Base):
    __tablename__ = "feedback"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    email = Column(String, nullable=False)
    title = Column(String, nullable=False)
    comment = Column(Text, nullable=False)
    submitted_at = Column(DateTime, nullable=False)
