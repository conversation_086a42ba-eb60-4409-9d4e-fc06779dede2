from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta, timezone

from app.models.database import get_db
from app.models.report import Report, ReportCategory
from app.schemas.report import ReportCreate, ReportResponse
from app.services.notification_service import get_notification_service

router = APIRouter()

@router.post("/", response_model=ReportResponse)
def create_report(report: ReportCreate, db: Session = Depends(get_db)):
    """Create a new report"""
    # Store latitude and longitude directly
    db_report = Report(
        category=report.category,
        description=report.description,
        image_url=report.image_url,
        latitude=report.location.latitude,
        longitude=report.location.longitude,
        user_hash=report.user_hash
    )

    db.add(db_report)
    db.commit()
    db.refresh(db_report)

    # Process notifications for community groups
    notification_service = get_notification_service(db)
    notification_service.process_new_report(db_report)

    # Create response with proper location format
    response = ReportResponse(
        id=db_report.id,
        category=db_report.category,
        description=db_report.description,
        image_url=db_report.image_url,
        location={
            "longitude": db_report.longitude,
            "latitude": db_report.latitude
        },
        timestamp=db_report.timestamp,
        verified=db_report.verified
    )

    return response

@router.get("/", response_model=List[ReportResponse])
def get_reports(
    category: Optional[ReportCategory] = Query(None),
    start_date: Optional[str] = Query(None, description="Filter reports on or after this date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="Filter reports on or before this date (YYYY-MM-DD)"),
    verified: Optional[str] = Query(None, description="'true' or 'false' to filter verified status"),
    db: Session = Depends(get_db)
):
    """Get reports with optional filtering by category, date range, and verified status"""
    query = db.query(Report)

    # Filter by category
    if category:
        query = query.filter(Report.category == category)

    # Filter by start_date
    if start_date:
        try:
            start_dt = datetime.fromisoformat(start_date)
            if start_dt.tzinfo is None:
                start_dt = start_dt.replace(tzinfo=timezone.utc)
            query = query.filter(Report.timestamp >= start_dt)
        except ValueError:
            pass

    # Filter by end_date
    if end_date:
        try:
            end_dt = datetime.fromisoformat(end_date) + timedelta(days=1)
            if end_dt.tzinfo is None:
                end_dt = end_dt.replace(tzinfo=timezone.utc)
            query = query.filter(Report.timestamp < end_dt)
        except ValueError:
            pass

    # Filter by verified status
    if verified is not None:
        if verified.lower() == 'true':
            query = query.filter(Report.verified == True)
        elif verified.lower() == 'false':
            query = query.filter(Report.verified == False)

    # Filter for the last 24 hours if no start_date is provided
    if not start_date:
        last_24_hours = datetime.now(timezone.utc) - timedelta(hours=24)
        query = query.filter(Report.timestamp >= last_24_hours)

    # Order by timestamp (newest first)
    query = query.order_by(Report.timestamp.desc())

    # Execute query
    results = query.all()

    # Format response
    response = []
    for result in results:
        report = ReportResponse(
            id=result.id,
            category=result.category,
            description=result.description,
            image_url=result.image_url,
            location={
                "longitude": result.longitude,
                "latitude": result.latitude
            },
            timestamp=result.timestamp,
            verified=result.verified
        )
        response.append(report)

    # Log the filtering parameters
    print(f"Filtering reports with start_date: {start_date}, end_date: {end_date}, verified: {verified}")

    return response

@router.get("/{report_id}", response_model=ReportResponse)
def get_report(report_id: str, db: Session = Depends(get_db)):
    """Get a specific report by ID"""
    result = db.query(Report).filter(Report.id == report_id).first()

    if not result:
        raise HTTPException(status_code=404, detail="Report not found")

    # Format response
    response = ReportResponse(
        id=result.id,
        category=result.category,
        description=result.description,
        image_url=result.image_url,
        location={
            "longitude": result.longitude,
            "latitude": result.latitude
        },
        timestamp=result.timestamp,
        verified=result.verified
    )

    return response
