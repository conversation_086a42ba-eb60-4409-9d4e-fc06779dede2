from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, timezone
import math

from app.models.database import get_db
from app.models.report import Report, ReportCategory
from app.schemas.report import ReportCreate, ReportResponse
from app.services.notification_service import get_notification_service

router = APIRouter()

@router.post("/", response_model=ReportResponse)
def create_report(report: ReportCreate, db: Session = Depends(get_db)):
    """Create a new report"""
    # Store latitude and longitude directly
    db_report = Report(
        category=report.category,
        description=report.description,
        image_url=report.image_url,
        latitude=report.location.latitude,
        longitude=report.location.longitude,
        user_hash=report.user_hash
    )

    db.add(db_report)
    db.commit()
    db.refresh(db_report)

    # Process notifications for community groups
    notification_service = get_notification_service(db)
    notification_service.process_new_report(db_report)

    # Create response with proper location format
    response = ReportResponse(
        id=db_report.id,
        category=db_report.category,
        description=db_report.description,
        image_url=db_report.image_url,
        location={
            "longitude": db_report.longitude,
            "latitude": db_report.latitude
        },
        timestamp=db_report.timestamp,
        verified=db_report.verified
    )

    return response

@router.get("/", response_model=List[ReportResponse])
def get_reports(
    category: Optional[ReportCategory] = Query(None),
    start_date: Optional[str] = Query(None, description="Filter reports on or after this date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="Filter reports on or before this date (YYYY-MM-DD)"),
    verified: Optional[str] = Query(None, description="'true' or 'false' to filter verified status"),
    db: Session = Depends(get_db)
):
    """Get reports with optional filtering by category, date range, and verified status"""
    query = db.query(Report)

    # Filter by category
    if category:
        query = query.filter(Report.category == category)

    # Filter by start_date
    if start_date:
        try:
            start_dt = datetime.fromisoformat(start_date)
            if start_dt.tzinfo is None:
                start_dt = start_dt.replace(tzinfo=timezone.utc)
            query = query.filter(Report.timestamp >= start_dt)
        except ValueError:
            pass

    # Filter by end_date
    if end_date:
        try:
            end_dt = datetime.fromisoformat(end_date) + timedelta(days=1)
            if end_dt.tzinfo is None:
                end_dt = end_dt.replace(tzinfo=timezone.utc)
            query = query.filter(Report.timestamp < end_dt)
        except ValueError:
            pass

    # Filter by verified status
    if verified is not None:
        if verified.lower() == 'true':
            query = query.filter(Report.verified == True)
        elif verified.lower() == 'false':
            query = query.filter(Report.verified == False)

    # Filter for the last 24 hours if no start_date is provided
    if not start_date:
        last_24_hours = datetime.now(timezone.utc) - timedelta(hours=24)
        query = query.filter(Report.timestamp >= last_24_hours)

    # Order by timestamp (newest first)
    query = query.order_by(Report.timestamp.desc())

    # Execute query
    results = query.all()

    # Format response
    response = []
    for result in results:
        report = ReportResponse(
            id=result.id,
            category=result.category,
            description=result.description,
            image_url=result.image_url,
            location={
                "longitude": result.longitude,
                "latitude": result.latitude
            },
            timestamp=result.timestamp,
            verified=result.verified
        )
        response.append(report)

    # Log the filtering parameters
    print(f"Filtering reports with start_date: {start_date}, end_date: {end_date}, verified: {verified}")

    return response

@router.get("/{report_id}", response_model=ReportResponse)
def get_report(report_id: str, db: Session = Depends(get_db)):
    """Get a specific report by ID"""
    result = db.query(Report).filter(Report.id == report_id).first()

    if not result:
        raise HTTPException(status_code=404, detail="Report not found")

    # Format response
    response = ReportResponse(
        id=result.id,
        category=result.category,
        description=result.description,
        image_url=result.image_url,
        location={
            "longitude": result.longitude,
            "latitude": result.latitude
        },
        timestamp=result.timestamp,
        verified=result.verified
    )

    return response

@router.get("/heatmap", response_model=Dict[str, Any])
def get_heatmap_data(
    hours: Optional[int] = Query(24, description="Number of hours to look back for reports"),
    grid_size: Optional[float] = Query(0.1, description="Grid size in degrees for aggregation"),
    db: Session = Depends(get_db)
):
    """
    Get heatmap data for South Africa with danger level classification

    Returns aggregated report data in a grid format with danger levels:
    - Red (danger): High crime activity or multiple incidents
    - Yellow (warning): Moderate activity or infrastructure issues
    - Green (safe): Low activity or EMS-only reports
    """

    # Calculate time threshold
    time_threshold = datetime.now(timezone.utc) - timedelta(hours=hours)

    # Get all reports within the time window
    reports = db.query(Report).filter(
        Report.timestamp >= time_threshold,
        Report.latitude.isnot(None),
        Report.longitude.isnot(None)
    ).all()

    # South Africa bounds (approximate)
    SA_BOUNDS = {
        'min_lat': -35.0,
        'max_lat': -22.0,
        'min_lng': 16.0,
        'max_lng': 33.0
    }

    # Create grid
    grid_data = {}

    # Process each report
    for report in reports:
        # Skip reports outside SA bounds
        if not (SA_BOUNDS['min_lat'] <= report.latitude <= SA_BOUNDS['max_lat'] and
                SA_BOUNDS['min_lng'] <= report.longitude <= SA_BOUNDS['max_lng']):
            continue

        # Calculate grid cell
        grid_lat = math.floor(report.latitude / grid_size) * grid_size
        grid_lng = math.floor(report.longitude / grid_size) * grid_size
        grid_key = f"{grid_lat},{grid_lng}"

        # Initialize grid cell if not exists
        if grid_key not in grid_data:
            grid_data[grid_key] = {
                'lat': grid_lat + grid_size/2,  # Center of grid cell
                'lng': grid_lng + grid_size/2,
                'crime_count': 0,
                'ems_count': 0,
                'infrastructure_count': 0,
                'total_count': 0,
                'reports': []
            }

        # Add report to grid cell
        grid_data[grid_key]['total_count'] += 1
        grid_data[grid_key]['reports'].append({
            'id': report.id,
            'category': report.category.value,
            'description': report.description,
            'timestamp': report.timestamp.isoformat()
        })

        # Count by category
        if report.category == ReportCategory.crime:
            grid_data[grid_key]['crime_count'] += 1
        elif report.category == ReportCategory.ems:
            grid_data[grid_key]['ems_count'] += 1
        elif report.category == ReportCategory.infrastructure:
            grid_data[grid_key]['infrastructure_count'] += 1

    # Calculate danger levels for each grid cell
    heatmap_zones = []

    for grid_key, data in grid_data.items():
        # Determine danger level based on report types and counts
        danger_level = calculate_danger_level(
            data['crime_count'],
            data['ems_count'],
            data['infrastructure_count'],
            data['total_count']
        )

        zone = {
            'lat': data['lat'],
            'lng': data['lng'],
            'danger_level': danger_level,
            'color': get_danger_color(danger_level),
            'total_reports': data['total_count'],
            'crime_reports': data['crime_count'],
            'ems_reports': data['ems_count'],
            'infrastructure_reports': data['infrastructure_count'],
            'recent_reports': data['reports'][-5:] if len(data['reports']) > 5 else data['reports']  # Last 5 reports
        }

        heatmap_zones.append(zone)

    # Calculate overall statistics
    total_reports = len(reports)
    crime_reports = len([r for r in reports if r.category == ReportCategory.crime])
    ems_reports = len([r for r in reports if r.category == ReportCategory.ems])
    infrastructure_reports = len([r for r in reports if r.category == ReportCategory.infrastructure])

    # Count zones by danger level
    danger_zones = len([z for z in heatmap_zones if z['danger_level'] == 'danger'])
    warning_zones = len([z for z in heatmap_zones if z['danger_level'] == 'warning'])
    safe_zones = len([z for z in heatmap_zones if z['danger_level'] == 'safe'])

    return {
        'zones': heatmap_zones,
        'statistics': {
            'total_reports': total_reports,
            'crime_reports': crime_reports,
            'ems_reports': ems_reports,
            'infrastructure_reports': infrastructure_reports,
            'time_window_hours': hours,
            'grid_size': grid_size,
            'danger_zones': danger_zones,
            'warning_zones': warning_zones,
            'safe_zones': safe_zones
        },
        'bounds': SA_BOUNDS
    }

def calculate_danger_level(crime_count: int, ems_count: int, infrastructure_count: int, total_count: int) -> str:
    """
    Calculate danger level based on report counts and types

    Logic:
    - Danger (Red): 3+ crime reports OR 5+ total reports with crime
    - Warning (Yellow): 1-2 crime reports OR 3+ infrastructure reports OR 4+ total reports
    - Safe (Green): Only EMS reports OR low activity
    """

    # High crime activity = danger
    if crime_count >= 3:
        return 'danger'

    # High total activity with some crime = danger
    if total_count >= 5 and crime_count > 0:
        return 'danger'

    # Some crime activity = warning
    if crime_count >= 1:
        return 'warning'

    # High infrastructure issues = warning
    if infrastructure_count >= 3:
        return 'warning'

    # Moderate total activity = warning
    if total_count >= 4:
        return 'warning'

    # Low activity or EMS only = safe
    return 'safe'

def get_danger_color(danger_level: str) -> str:
    """Get color code for danger level"""
    colors = {
        'danger': '#dc3545',    # Red
        'warning': '#ffc107',   # Yellow
        'safe': '#28a745'       # Green
    }
    return colors.get(danger_level, '#6c757d')  # Default gray