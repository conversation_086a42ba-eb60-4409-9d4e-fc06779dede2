r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Assistants
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional
from twilio.base.version import Version
from twilio.base.domain import Domain
from twilio.rest.assistants.v1.assistant import AssistantList
from twilio.rest.assistants.v1.knowledge import KnowledgeList
from twilio.rest.assistants.v1.policy import PolicyList
from twilio.rest.assistants.v1.session import SessionList
from twilio.rest.assistants.v1.tool import ToolList


class V1(Version):

    def __init__(self, domain: Domain):
        """
        Initialize the V1 version of Assistants

        :param domain: The Twilio.assistants domain
        """
        super().__init__(domain, "v1")
        self._assistants: Optional[AssistantList] = None
        self._knowledge: Optional[KnowledgeList] = None
        self._policies: Optional[PolicyList] = None
        self._sessions: Optional[SessionList] = None
        self._tools: Optional[ToolList] = None

    @property
    def assistants(self) -> AssistantList:
        if self._assistants is None:
            self._assistants = AssistantList(self)
        return self._assistants

    @property
    def knowledge(self) -> KnowledgeList:
        if self._knowledge is None:
            self._knowledge = KnowledgeList(self)
        return self._knowledge

    @property
    def policies(self) -> PolicyList:
        if self._policies is None:
            self._policies = PolicyList(self)
        return self._policies

    @property
    def sessions(self) -> SessionList:
        if self._sessions is None:
            self._sessions = SessionList(self)
        return self._sessions

    @property
    def tools(self) -> ToolList:
        if self._tools is None:
            self._tools = ToolList(self)
        return self._tools

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.Assistants.V1>"
