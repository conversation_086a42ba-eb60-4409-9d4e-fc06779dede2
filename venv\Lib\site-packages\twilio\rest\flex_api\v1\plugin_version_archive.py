r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Flex
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, Optional, Union
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class PluginVersionArchiveInstance(InstanceResource):
    """
    :ivar sid: The unique string that we created to identify the Flex Plugin Version resource.
    :ivar plugin_sid: The SID of the Flex Plugin resource this Flex Plugin Version belongs to.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Flex Plugin Version resource and owns this resource.
    :ivar version: The unique version of this Flex Plugin Version.
    :ivar plugin_url: The URL of where the Flex Plugin Version JavaScript bundle is hosted on.
    :ivar changelog: A changelog that describes the changes this Flex Plugin Version brings.
    :ivar private: Whether to inject credentials while accessing this Plugin Version. The default value is false.
    :ivar archived: Whether the Flex Plugin Version is archived. The default value is false.
    :ivar date_created: The date and time in GMT when the Flex Plugin Version was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The absolute URL of the Flex Plugin Version resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        plugin_sid: Optional[str] = None,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.plugin_sid: Optional[str] = payload.get("plugin_sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.version: Optional[str] = payload.get("version")
        self.plugin_url: Optional[str] = payload.get("plugin_url")
        self.changelog: Optional[str] = payload.get("changelog")
        self.private: Optional[bool] = payload.get("private")
        self.archived: Optional[bool] = payload.get("archived")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "plugin_sid": plugin_sid or self.plugin_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[PluginVersionArchiveContext] = None

    @property
    def _proxy(self) -> "PluginVersionArchiveContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: PluginVersionArchiveContext for this PluginVersionArchiveInstance
        """
        if self._context is None:
            self._context = PluginVersionArchiveContext(
                self._version,
                plugin_sid=self._solution["plugin_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def update(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> "PluginVersionArchiveInstance":
        """
        Update the PluginVersionArchiveInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The updated PluginVersionArchiveInstance
        """
        return self._proxy.update(
            flex_metadata=flex_metadata,
        )

    async def update_async(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> "PluginVersionArchiveInstance":
        """
        Asynchronous coroutine to update the PluginVersionArchiveInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The updated PluginVersionArchiveInstance
        """
        return await self._proxy.update_async(
            flex_metadata=flex_metadata,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.PluginVersionArchiveInstance {}>".format(context)


class PluginVersionArchiveContext(InstanceContext):

    def __init__(self, version: Version, plugin_sid: str, sid: str):
        """
        Initialize the PluginVersionArchiveContext

        :param version: Version that contains the resource
        :param plugin_sid: The SID of the Flex Plugin the resource to belongs to.
        :param sid: The SID of the Flex Plugin Version resource to archive.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "plugin_sid": plugin_sid,
            "sid": sid,
        }
        self._uri = "/PluginService/Plugins/{plugin_sid}/Versions/{sid}/Archive".format(
            **self._solution
        )

    def update(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> PluginVersionArchiveInstance:
        """
        Update the PluginVersionArchiveInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The updated PluginVersionArchiveInstance
        """

        data = values.of({})
        headers = values.of({})

        if not (
            flex_metadata is values.unset
            or (isinstance(flex_metadata, str) and not flex_metadata)
        ):
            headers["Flex-Metadata"] = flex_metadata

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return PluginVersionArchiveInstance(
            self._version,
            payload,
            plugin_sid=self._solution["plugin_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> PluginVersionArchiveInstance:
        """
        Asynchronous coroutine to update the PluginVersionArchiveInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The updated PluginVersionArchiveInstance
        """

        data = values.of({})
        headers = values.of({})

        if not (
            flex_metadata is values.unset
            or (isinstance(flex_metadata, str) and not flex_metadata)
        ):
            headers["Flex-Metadata"] = flex_metadata

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return PluginVersionArchiveInstance(
            self._version,
            payload,
            plugin_sid=self._solution["plugin_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.PluginVersionArchiveContext {}>".format(context)


class PluginVersionArchiveList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the PluginVersionArchiveList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self, plugin_sid: str, sid: str) -> PluginVersionArchiveContext:
        """
        Constructs a PluginVersionArchiveContext

        :param plugin_sid: The SID of the Flex Plugin the resource to belongs to.
        :param sid: The SID of the Flex Plugin Version resource to archive.
        """
        return PluginVersionArchiveContext(
            self._version, plugin_sid=plugin_sid, sid=sid
        )

    def __call__(self, plugin_sid: str, sid: str) -> PluginVersionArchiveContext:
        """
        Constructs a PluginVersionArchiveContext

        :param plugin_sid: The SID of the Flex Plugin the resource to belongs to.
        :param sid: The SID of the Flex Plugin Version resource to archive.
        """
        return PluginVersionArchiveContext(
            self._version, plugin_sid=plugin_sid, sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V1.PluginVersionArchiveList>"
