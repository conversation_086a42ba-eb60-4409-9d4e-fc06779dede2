r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Numbers
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import date, datetime
from typing import Any, Dict, List, Optional, Union
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class PortingPortInInstance(InstanceResource):
    """
    :ivar port_in_request_sid: The SID of the Port In request. This is a unique identifier of the port in request.
    :ivar url: The URL of this Port In request
    :ivar account_sid: Account Sid or subaccount where the phone number(s) will be Ported
    :ivar notification_emails: Additional emails to send a copy of the signed LOA to.
    :ivar target_port_in_date: Target date to port the number. We cannot guarantee that this date will be honored by the other carriers, please work with Ops to get a confirmation of the firm order commitment (FOC) date. Expected format is ISO Local Date, example: ‘2011-12-03`. This date must be at least 7 days in the future for US ports and 10 days in the future for Japanese ports. (This value is only available for custom porting customers.)
    :ivar target_port_in_time_range_start: The earliest time that the port should occur on the target port in date. Expected format is ISO Offset Time, example: ‘10:15:00-08:00'. (This value is only available for custom porting customers.)
    :ivar target_port_in_time_range_end: The latest time that the port should occur on the target port in date. Expected format is ISO Offset Time, example: ‘10:15:00-08:00'.  (This value is only available for custom porting customers.)
    :ivar port_in_request_status: The status of the port in request. The possible values are: In progress, Completed, Expired, In review, Waiting for Signature, Action Required, and Canceled.
    :ivar losing_carrier_information: Details regarding the customer’s information with the losing carrier. These values will be used to generate the letter of authorization and should match the losing carrier’s data as closely as possible to ensure the port is accepted.
    :ivar phone_numbers:
    :ivar documents: List of document SIDs for all phone numbers included in the port in request. At least one document SID referring to a document of the type Utility Bill is required.
    :ivar date_created:
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        port_in_request_sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.port_in_request_sid: Optional[str] = payload.get("port_in_request_sid")
        self.url: Optional[str] = payload.get("url")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.notification_emails: Optional[List[str]] = payload.get(
            "notification_emails"
        )
        self.target_port_in_date: Optional[date] = deserialize.iso8601_date(
            payload.get("target_port_in_date")
        )
        self.target_port_in_time_range_start: Optional[str] = payload.get(
            "target_port_in_time_range_start"
        )
        self.target_port_in_time_range_end: Optional[str] = payload.get(
            "target_port_in_time_range_end"
        )
        self.port_in_request_status: Optional[str] = payload.get(
            "port_in_request_status"
        )
        self.losing_carrier_information: Optional[Dict[str, object]] = payload.get(
            "losing_carrier_information"
        )
        self.phone_numbers: Optional[List[Dict[str, object]]] = payload.get(
            "phone_numbers"
        )
        self.documents: Optional[List[str]] = payload.get("documents")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )

        self._solution = {
            "port_in_request_sid": port_in_request_sid or self.port_in_request_sid,
        }
        self._context: Optional[PortingPortInContext] = None

    @property
    def _proxy(self) -> "PortingPortInContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: PortingPortInContext for this PortingPortInInstance
        """
        if self._context is None:
            self._context = PortingPortInContext(
                self._version,
                port_in_request_sid=self._solution["port_in_request_sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the PortingPortInInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the PortingPortInInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "PortingPortInInstance":
        """
        Fetch the PortingPortInInstance


        :returns: The fetched PortingPortInInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "PortingPortInInstance":
        """
        Asynchronous coroutine to fetch the PortingPortInInstance


        :returns: The fetched PortingPortInInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Numbers.V1.PortingPortInInstance {}>".format(context)


class PortingPortInContext(InstanceContext):

    def __init__(self, version: Version, port_in_request_sid: str):
        """
        Initialize the PortingPortInContext

        :param version: Version that contains the resource
        :param port_in_request_sid: The SID of the Port In request. This is a unique identifier of the port in request.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "port_in_request_sid": port_in_request_sid,
        }
        self._uri = "/Porting/PortIn/{port_in_request_sid}".format(**self._solution)

    def delete(self) -> bool:
        """
        Deletes the PortingPortInInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the PortingPortInInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> PortingPortInInstance:
        """
        Fetch the PortingPortInInstance


        :returns: The fetched PortingPortInInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return PortingPortInInstance(
            self._version,
            payload,
            port_in_request_sid=self._solution["port_in_request_sid"],
        )

    async def fetch_async(self) -> PortingPortInInstance:
        """
        Asynchronous coroutine to fetch the PortingPortInInstance


        :returns: The fetched PortingPortInInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return PortingPortInInstance(
            self._version,
            payload,
            port_in_request_sid=self._solution["port_in_request_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Numbers.V1.PortingPortInContext {}>".format(context)


class PortingPortInList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the PortingPortInList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Porting/PortIn"

    def create(
        self, body: Union[object, object] = values.unset
    ) -> PortingPortInInstance:
        """
        Create the PortingPortInInstance

        :param body:

        :returns: The created PortingPortInInstance
        """
        data = body.to_dict()

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return PortingPortInInstance(self._version, payload)

    async def create_async(
        self, body: Union[object, object] = values.unset
    ) -> PortingPortInInstance:
        """
        Asynchronously create the PortingPortInInstance

        :param body:

        :returns: The created PortingPortInInstance
        """
        data = body.to_dict()

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return PortingPortInInstance(self._version, payload)

    def get(self, port_in_request_sid: str) -> PortingPortInContext:
        """
        Constructs a PortingPortInContext

        :param port_in_request_sid: The SID of the Port In request. This is a unique identifier of the port in request.
        """
        return PortingPortInContext(
            self._version, port_in_request_sid=port_in_request_sid
        )

    def __call__(self, port_in_request_sid: str) -> PortingPortInContext:
        """
        Constructs a PortingPortInContext

        :param port_in_request_sid: The SID of the Port In request. This is a unique identifier of the port in request.
        """
        return PortingPortInContext(
            self._version, port_in_request_sid=port_in_request_sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Numbers.V1.PortingPortInList>"
