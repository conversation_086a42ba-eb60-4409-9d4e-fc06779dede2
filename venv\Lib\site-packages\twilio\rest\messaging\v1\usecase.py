r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Messaging
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, List, Optional
from twilio.base import values

from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class UsecaseInstance(InstanceResource):
    """
    :ivar usecases: Human readable use case details (usecase, description and purpose) of Messaging Service Use Cases.
    """

    def __init__(self, version: Version, payload: Dict[str, Any]):
        super().__init__(version)

        self.usecases: Optional[List[Dict[str, object]]] = payload.get("usecases")

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Messaging.V1.UsecaseInstance>"


class UsecaseList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the UsecaseList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Services/Usecases"

    def fetch(self) -> UsecaseInstance:
        """
        Asynchronously fetch the UsecaseInstance


        :returns: The fetched UsecaseInstance
        """
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return UsecaseInstance(self._version, payload)

    async def fetch_async(self) -> UsecaseInstance:
        """
        Asynchronously fetch the UsecaseInstance


        :returns: The fetched UsecaseInstance
        """
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return UsecaseInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Messaging.V1.UsecaseList>"
