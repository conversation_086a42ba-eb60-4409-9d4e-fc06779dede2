{% extends "base.html" %}

{% block content %}

{% block extra_css %}
<!-- Include jsPDF for PDF generation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

<!-- Include Flatpickr for better date picking -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

<style>
  .report-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .report-controls {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
  }

  .control-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 15px;
  }

  .control-group {
    flex: 1;
    min-width: 200px;
  }

  .control-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .control-group select,
  .control-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  .map-selection-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
  }

  #selection-map {
    height: 400px;
    width: 100%;
    border-radius: 5px;
  }

  .action-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }

  .generate-btn {
    background-color: #ee4d4d;
    border: none;
    border-radius: 5px;
    color: white;
    cursor: pointer;
    font-family: "Dosis", sans-serif;
    font-size: 16px;
    padding: 10px 20px;
    text-transform: uppercase;
    transition: background-color 0.3s ease;
  }

  .generate-btn:hover {
    background-color: #d32f2f;
  }

  .reset-btn {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 5px;
    color: #333;
    cursor: pointer;
    font-family: "Dosis", sans-serif;
    font-size: 16px;
    padding: 10px 20px;
    text-transform: uppercase;
    transition: background-color 0.3s ease;
  }

  .reset-btn:hover {
    background-color: #e0e0e0;
  }

  #report-preview {
    display: none;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-top: 20px;
  }

  .report-header {
    text-align: center;
    margin-bottom: 20px;
  }

  .report-header h2 {
    color: #ee4d4d;
    margin-bottom: 5px;
  }

  .report-header p {
    color: #666;
    margin: 5px 0;
  }

  #report-map {
    height: 400px;
    width: 100%;
    border-radius: 5px;
    margin-bottom: 20px;
  }

  .report-incidents {
    margin-top: 20px;
  }

  .report-incidents h3 {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }

  .incident-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  .incident-card {
    background-color: #f9f9f9;
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .incident-card h4 {
    color: #ee4d4d;
    margin-top: 0;
    margin-bottom: 10px;
  }

  .incident-meta {
    color: #888;
    font-size: 0.9em;
    margin-bottom: 10px;
  }

  .download-btn {
    background-color: #4CAF50;
    border: none;
    border-radius: 5px;
    color: white;
    cursor: pointer;
    font-family: "Dosis", sans-serif;
    font-size: 16px;
    padding: 10px 20px;
    text-transform: uppercase;
    transition: background-color 0.3s ease;
    margin-top: 20px;
  }

  .download-btn:hover {
    background-color: #388E3C;
  }

  .category-badge {
    border-radius: 20px;
    display: inline-block;
    font-size: 0.8em;
    margin-left: 10px;
    padding: 3px 10px;
    text-transform: uppercase;
  }

  .category-crime {
    background-color: #ffebee;
    color: #c62828;
  }

  .category-ems {
    background-color: #e3f2fd;
    color: #1565c0;
  }

  .category-infrastructure {
    background-color: #fff8e1;
    color: #ff8f00;
  }

  /* Flatpickr customization */
  .flatpickr-calendar {
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
  }

  .flatpickr-day.selected,
  .flatpickr-day.startRange,
  .flatpickr-day.endRange,
  .flatpickr-day.selected.inRange,
  .flatpickr-day.startRange.inRange,
  .flatpickr-day.endRange.inRange,
  .flatpickr-day.selected:focus,
  .flatpickr-day.startRange:focus,
  .flatpickr-day.endRange:focus,
  .flatpickr-day.selected:hover,
  .flatpickr-day.startRange:hover,
  .flatpickr-day.endRange:hover,
  .flatpickr-day.selected.prevMonthDay,
  .flatpickr-day.startRange.prevMonthDay,
  .flatpickr-day.endRange.prevMonthDay,
  .flatpickr-day.selected.nextMonthDay,
  .flatpickr-day.startRange.nextMonthDay,
  .flatpickr-day.endRange.nextMonthDay {
    background: #ee4d4d;
    border-color: #ee4d4d;
  }

  .flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),
  .flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),
  .flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {
    box-shadow: -10px 0 0 #ee4d4d;
  }

  .flatpickr-months .flatpickr-month {
    background: #ee4d4d;
  }

  .flatpickr-months .flatpickr-prev-month,
  .flatpickr-months .flatpickr-next-month {
    color: rgba(255, 255, 255, 0.9);
    fill: rgba(255, 255, 255, 0.9);
  }

  .flatpickr-current-month .flatpickr-monthDropdown-months {
    background: #ee4d4d;
    color: white;
  }

  .flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
    background-color: #ee4d4d;
  }

  .flatpickr-current-month .numInputWrapper span.arrowUp:after {
    border-bottom-color: rgba(255, 255, 255, 0.9);
  }

  .flatpickr-current-month .numInputWrapper span.arrowDown:after {
    border-top-color: rgba(255, 255, 255, 0.9);
  }

  .flatpickr-current-month input.cur-year {
    color: white;
  }

  /* Print styles */
  @media print {
    body * {
      visibility: hidden;
    }
    #report-preview, #report-preview * {
      visibility: visible;
    }
    #report-preview {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
    }
    .download-btn {
      display: none;
    }
  }
</style>
{% endblock %}


<div class="report-container pt-5">
  <div class="report-controls">
    <h2>Contact Us</h2>
    <form method="post" action="/api/contact-us">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" class="form-control" id="email" name="email" required>
        </div>
        <div class="form-group">
            <label for="title">Title:</label>
            <input type="text" class="form-control" id="title" name="title" required>
        </div>
        <div class="form-group">
            <label for="comment">Comment:</label>
            <textarea class="form-control" id="comment" name="comment" rows="5" required></textarea>
        </div>
        <button type="submit" class="btn btn-primary">Submit</button>
    </form>
  </div>
</div>

<div class="container">

</div>

<script>
    document.querySelector('form').addEventListener('submit', async function(event) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);

        const response = await fetch(form.action, {
            method: form.method,
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            alert(result.message);
            form.reset();
        } else {
            alert('Failed to submit feedback. Please try again.');
        }
    });
</script>

{% endblock %}
