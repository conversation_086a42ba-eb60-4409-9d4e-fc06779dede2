r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Flex
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional
from twilio.base.version import Version
from twilio.base.domain import Domain
from twilio.rest.flex_api.v1.assessments import AssessmentsList
from twilio.rest.flex_api.v1.channel import ChannelList
from twilio.rest.flex_api.v1.configuration import ConfigurationList
from twilio.rest.flex_api.v1.flex_flow import FlexFlowList
from twilio.rest.flex_api.v1.insights_assessments_comment import (
    InsightsAssessmentsCommentList,
)
from twilio.rest.flex_api.v1.insights_conversations import InsightsConversationsList
from twilio.rest.flex_api.v1.insights_questionnaires import InsightsQuestionnairesList
from twilio.rest.flex_api.v1.insights_questionnaires_category import (
    InsightsQuestionnairesCategoryList,
)
from twilio.rest.flex_api.v1.insights_questionnaires_question import (
    InsightsQuestionnairesQuestionList,
)
from twilio.rest.flex_api.v1.insights_segments import InsightsSegmentsList
from twilio.rest.flex_api.v1.insights_session import InsightsSessionList
from twilio.rest.flex_api.v1.insights_settings_answer_sets import (
    InsightsSettingsAnswerSetsList,
)
from twilio.rest.flex_api.v1.insights_settings_comment import (
    InsightsSettingsCommentList,
)
from twilio.rest.flex_api.v1.insights_user_roles import InsightsUserRolesList
from twilio.rest.flex_api.v1.interaction import InteractionList
from twilio.rest.flex_api.v1.plugin import PluginList
from twilio.rest.flex_api.v1.plugin_archive import PluginArchiveList
from twilio.rest.flex_api.v1.plugin_configuration import PluginConfigurationList
from twilio.rest.flex_api.v1.plugin_configuration_archive import (
    PluginConfigurationArchiveList,
)
from twilio.rest.flex_api.v1.plugin_release import PluginReleaseList
from twilio.rest.flex_api.v1.plugin_version_archive import PluginVersionArchiveList
from twilio.rest.flex_api.v1.provisioning_status import ProvisioningStatusList
from twilio.rest.flex_api.v1.web_channel import WebChannelList


class V1(Version):

    def __init__(self, domain: Domain):
        """
        Initialize the V1 version of FlexApi

        :param domain: The Twilio.flex_api domain
        """
        super().__init__(domain, "v1")
        self._assessments: Optional[AssessmentsList] = None
        self._channel: Optional[ChannelList] = None
        self._configuration: Optional[ConfigurationList] = None
        self._flex_flow: Optional[FlexFlowList] = None
        self._insights_assessments_comment: Optional[InsightsAssessmentsCommentList] = (
            None
        )
        self._insights_conversations: Optional[InsightsConversationsList] = None
        self._insights_questionnaires: Optional[InsightsQuestionnairesList] = None
        self._insights_questionnaires_category: Optional[
            InsightsQuestionnairesCategoryList
        ] = None
        self._insights_questionnaires_question: Optional[
            InsightsQuestionnairesQuestionList
        ] = None
        self._insights_segments: Optional[InsightsSegmentsList] = None
        self._insights_session: Optional[InsightsSessionList] = None
        self._insights_settings_answer_sets: Optional[
            InsightsSettingsAnswerSetsList
        ] = None
        self._insights_settings_comment: Optional[InsightsSettingsCommentList] = None
        self._insights_user_roles: Optional[InsightsUserRolesList] = None
        self._interaction: Optional[InteractionList] = None
        self._plugins: Optional[PluginList] = None
        self._plugin_archive: Optional[PluginArchiveList] = None
        self._plugin_configurations: Optional[PluginConfigurationList] = None
        self._plugin_configuration_archive: Optional[PluginConfigurationArchiveList] = (
            None
        )
        self._plugin_releases: Optional[PluginReleaseList] = None
        self._plugin_version_archive: Optional[PluginVersionArchiveList] = None
        self._provisioning_status: Optional[ProvisioningStatusList] = None
        self._web_channel: Optional[WebChannelList] = None

    @property
    def assessments(self) -> AssessmentsList:
        if self._assessments is None:
            self._assessments = AssessmentsList(self)
        return self._assessments

    @property
    def channel(self) -> ChannelList:
        if self._channel is None:
            self._channel = ChannelList(self)
        return self._channel

    @property
    def configuration(self) -> ConfigurationList:
        if self._configuration is None:
            self._configuration = ConfigurationList(self)
        return self._configuration

    @property
    def flex_flow(self) -> FlexFlowList:
        if self._flex_flow is None:
            self._flex_flow = FlexFlowList(self)
        return self._flex_flow

    @property
    def insights_assessments_comment(self) -> InsightsAssessmentsCommentList:
        if self._insights_assessments_comment is None:
            self._insights_assessments_comment = InsightsAssessmentsCommentList(self)
        return self._insights_assessments_comment

    @property
    def insights_conversations(self) -> InsightsConversationsList:
        if self._insights_conversations is None:
            self._insights_conversations = InsightsConversationsList(self)
        return self._insights_conversations

    @property
    def insights_questionnaires(self) -> InsightsQuestionnairesList:
        if self._insights_questionnaires is None:
            self._insights_questionnaires = InsightsQuestionnairesList(self)
        return self._insights_questionnaires

    @property
    def insights_questionnaires_category(self) -> InsightsQuestionnairesCategoryList:
        if self._insights_questionnaires_category is None:
            self._insights_questionnaires_category = InsightsQuestionnairesCategoryList(
                self
            )
        return self._insights_questionnaires_category

    @property
    def insights_questionnaires_question(self) -> InsightsQuestionnairesQuestionList:
        if self._insights_questionnaires_question is None:
            self._insights_questionnaires_question = InsightsQuestionnairesQuestionList(
                self
            )
        return self._insights_questionnaires_question

    @property
    def insights_segments(self) -> InsightsSegmentsList:
        if self._insights_segments is None:
            self._insights_segments = InsightsSegmentsList(self)
        return self._insights_segments

    @property
    def insights_session(self) -> InsightsSessionList:
        if self._insights_session is None:
            self._insights_session = InsightsSessionList(self)
        return self._insights_session

    @property
    def insights_settings_answer_sets(self) -> InsightsSettingsAnswerSetsList:
        if self._insights_settings_answer_sets is None:
            self._insights_settings_answer_sets = InsightsSettingsAnswerSetsList(self)
        return self._insights_settings_answer_sets

    @property
    def insights_settings_comment(self) -> InsightsSettingsCommentList:
        if self._insights_settings_comment is None:
            self._insights_settings_comment = InsightsSettingsCommentList(self)
        return self._insights_settings_comment

    @property
    def insights_user_roles(self) -> InsightsUserRolesList:
        if self._insights_user_roles is None:
            self._insights_user_roles = InsightsUserRolesList(self)
        return self._insights_user_roles

    @property
    def interaction(self) -> InteractionList:
        if self._interaction is None:
            self._interaction = InteractionList(self)
        return self._interaction

    @property
    def plugins(self) -> PluginList:
        if self._plugins is None:
            self._plugins = PluginList(self)
        return self._plugins

    @property
    def plugin_archive(self) -> PluginArchiveList:
        if self._plugin_archive is None:
            self._plugin_archive = PluginArchiveList(self)
        return self._plugin_archive

    @property
    def plugin_configurations(self) -> PluginConfigurationList:
        if self._plugin_configurations is None:
            self._plugin_configurations = PluginConfigurationList(self)
        return self._plugin_configurations

    @property
    def plugin_configuration_archive(self) -> PluginConfigurationArchiveList:
        if self._plugin_configuration_archive is None:
            self._plugin_configuration_archive = PluginConfigurationArchiveList(self)
        return self._plugin_configuration_archive

    @property
    def plugin_releases(self) -> PluginReleaseList:
        if self._plugin_releases is None:
            self._plugin_releases = PluginReleaseList(self)
        return self._plugin_releases

    @property
    def plugin_version_archive(self) -> PluginVersionArchiveList:
        if self._plugin_version_archive is None:
            self._plugin_version_archive = PluginVersionArchiveList(self)
        return self._plugin_version_archive

    @property
    def provisioning_status(self) -> ProvisioningStatusList:
        if self._provisioning_status is None:
            self._provisioning_status = ProvisioningStatusList(self)
        return self._provisioning_status

    @property
    def web_channel(self) -> WebChannelList:
        if self._web_channel is None:
            self._web_channel = WebChannelList(self)
        return self._web_channel

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V1>"
