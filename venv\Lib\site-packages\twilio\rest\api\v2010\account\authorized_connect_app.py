r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Api
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class AuthorizedConnectAppInstance(InstanceResource):

    class Permission(object):
        GET_ALL = "get-all"
        POST_ALL = "post-all"

    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the AuthorizedConnectApp resource.
    :ivar connect_app_company_name: The company name set for the Connect App.
    :ivar connect_app_description: A detailed description of the Connect App.
    :ivar connect_app_friendly_name: The name of the Connect App.
    :ivar connect_app_homepage_url: The public URL for the Connect App.
    :ivar connect_app_sid: The SID that we assigned to the Connect App.
    :ivar permissions: The set of permissions that you authorized for the Connect App.  Can be: `get-all` or `post-all`.
    :ivar uri: The URI of the resource, relative to `https://api.twilio.com`.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        account_sid: str,
        connect_app_sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.connect_app_company_name: Optional[str] = payload.get(
            "connect_app_company_name"
        )
        self.connect_app_description: Optional[str] = payload.get(
            "connect_app_description"
        )
        self.connect_app_friendly_name: Optional[str] = payload.get(
            "connect_app_friendly_name"
        )
        self.connect_app_homepage_url: Optional[str] = payload.get(
            "connect_app_homepage_url"
        )
        self.connect_app_sid: Optional[str] = payload.get("connect_app_sid")
        self.permissions: Optional[List["AuthorizedConnectAppInstance.Permission"]] = (
            payload.get("permissions")
        )
        self.uri: Optional[str] = payload.get("uri")

        self._solution = {
            "account_sid": account_sid,
            "connect_app_sid": connect_app_sid or self.connect_app_sid,
        }
        self._context: Optional[AuthorizedConnectAppContext] = None

    @property
    def _proxy(self) -> "AuthorizedConnectAppContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: AuthorizedConnectAppContext for this AuthorizedConnectAppInstance
        """
        if self._context is None:
            self._context = AuthorizedConnectAppContext(
                self._version,
                account_sid=self._solution["account_sid"],
                connect_app_sid=self._solution["connect_app_sid"],
            )
        return self._context

    def fetch(self) -> "AuthorizedConnectAppInstance":
        """
        Fetch the AuthorizedConnectAppInstance


        :returns: The fetched AuthorizedConnectAppInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "AuthorizedConnectAppInstance":
        """
        Asynchronous coroutine to fetch the AuthorizedConnectAppInstance


        :returns: The fetched AuthorizedConnectAppInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.AuthorizedConnectAppInstance {}>".format(context)


class AuthorizedConnectAppContext(InstanceContext):

    def __init__(self, version: Version, account_sid: str, connect_app_sid: str):
        """
        Initialize the AuthorizedConnectAppContext

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the AuthorizedConnectApp resource to fetch.
        :param connect_app_sid: The SID of the Connect App to fetch.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "connect_app_sid": connect_app_sid,
        }
        self._uri = "/Accounts/{account_sid}/AuthorizedConnectApps/{connect_app_sid}.json".format(
            **self._solution
        )

    def fetch(self) -> AuthorizedConnectAppInstance:
        """
        Fetch the AuthorizedConnectAppInstance


        :returns: The fetched AuthorizedConnectAppInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return AuthorizedConnectAppInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            connect_app_sid=self._solution["connect_app_sid"],
        )

    async def fetch_async(self) -> AuthorizedConnectAppInstance:
        """
        Asynchronous coroutine to fetch the AuthorizedConnectAppInstance


        :returns: The fetched AuthorizedConnectAppInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return AuthorizedConnectAppInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            connect_app_sid=self._solution["connect_app_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.AuthorizedConnectAppContext {}>".format(context)


class AuthorizedConnectAppPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> AuthorizedConnectAppInstance:
        """
        Build an instance of AuthorizedConnectAppInstance

        :param payload: Payload response from the API
        """
        return AuthorizedConnectAppInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.AuthorizedConnectAppPage>"


class AuthorizedConnectAppList(ListResource):

    def __init__(self, version: Version, account_sid: str):
        """
        Initialize the AuthorizedConnectAppList

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the AuthorizedConnectApp resources to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
        }
        self._uri = "/Accounts/{account_sid}/AuthorizedConnectApps.json".format(
            **self._solution
        )

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[AuthorizedConnectAppInstance]:
        """
        Streams AuthorizedConnectAppInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[AuthorizedConnectAppInstance]:
        """
        Asynchronously streams AuthorizedConnectAppInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[AuthorizedConnectAppInstance]:
        """
        Lists AuthorizedConnectAppInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[AuthorizedConnectAppInstance]:
        """
        Asynchronously lists AuthorizedConnectAppInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> AuthorizedConnectAppPage:
        """
        Retrieve a single page of AuthorizedConnectAppInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of AuthorizedConnectAppInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return AuthorizedConnectAppPage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> AuthorizedConnectAppPage:
        """
        Asynchronously retrieve a single page of AuthorizedConnectAppInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of AuthorizedConnectAppInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return AuthorizedConnectAppPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> AuthorizedConnectAppPage:
        """
        Retrieve a specific page of AuthorizedConnectAppInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of AuthorizedConnectAppInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return AuthorizedConnectAppPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> AuthorizedConnectAppPage:
        """
        Asynchronously retrieve a specific page of AuthorizedConnectAppInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of AuthorizedConnectAppInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return AuthorizedConnectAppPage(self._version, response, self._solution)

    def get(self, connect_app_sid: str) -> AuthorizedConnectAppContext:
        """
        Constructs a AuthorizedConnectAppContext

        :param connect_app_sid: The SID of the Connect App to fetch.
        """
        return AuthorizedConnectAppContext(
            self._version,
            account_sid=self._solution["account_sid"],
            connect_app_sid=connect_app_sid,
        )

    def __call__(self, connect_app_sid: str) -> AuthorizedConnectAppContext:
        """
        Constructs a AuthorizedConnectAppContext

        :param connect_app_sid: The SID of the Connect App to fetch.
        """
        return AuthorizedConnectAppContext(
            self._version,
            account_sid=self._solution["account_sid"],
            connect_app_sid=connect_app_sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.AuthorizedConnectAppList>"
