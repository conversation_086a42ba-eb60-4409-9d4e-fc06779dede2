r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Flex
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class PluginVersionsInstance(InstanceResource):
    """
    :ivar sid: The unique string that we created to identify the Flex Plugin Version resource.
    :ivar plugin_sid: The SID of the Flex Plugin resource this Flex Plugin Version belongs to.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Flex Plugin Version resource and owns this resource.
    :ivar version: The unique version of this Flex Plugin Version.
    :ivar plugin_url: The URL of where the Flex Plugin Version JavaScript bundle is hosted on.
    :ivar changelog: A changelog that describes the changes this Flex Plugin Version brings.
    :ivar private: Whether the Flex Plugin Version is validated. The default value is false.
    :ivar archived: Whether the Flex Plugin Version is archived. The default value is false.
    :ivar validated:
    :ivar date_created: The date and time in GMT when the Flex Plugin Version was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The absolute URL of the Flex Plugin Version resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        plugin_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.plugin_sid: Optional[str] = payload.get("plugin_sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.version: Optional[str] = payload.get("version")
        self.plugin_url: Optional[str] = payload.get("plugin_url")
        self.changelog: Optional[str] = payload.get("changelog")
        self.private: Optional[bool] = payload.get("private")
        self.archived: Optional[bool] = payload.get("archived")
        self.validated: Optional[bool] = payload.get("validated")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "plugin_sid": plugin_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[PluginVersionsContext] = None

    @property
    def _proxy(self) -> "PluginVersionsContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: PluginVersionsContext for this PluginVersionsInstance
        """
        if self._context is None:
            self._context = PluginVersionsContext(
                self._version,
                plugin_sid=self._solution["plugin_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def fetch(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> "PluginVersionsInstance":
        """
        Fetch the PluginVersionsInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The fetched PluginVersionsInstance
        """
        return self._proxy.fetch(
            flex_metadata=flex_metadata,
        )

    async def fetch_async(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> "PluginVersionsInstance":
        """
        Asynchronous coroutine to fetch the PluginVersionsInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The fetched PluginVersionsInstance
        """
        return await self._proxy.fetch_async(
            flex_metadata=flex_metadata,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.PluginVersionsInstance {}>".format(context)


class PluginVersionsContext(InstanceContext):

    def __init__(self, version: Version, plugin_sid: str, sid: str):
        """
        Initialize the PluginVersionsContext

        :param version: Version that contains the resource
        :param plugin_sid: The SID of the Flex Plugin the resource to belongs to.
        :param sid: The SID of the Flex Plugin Version resource to fetch.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "plugin_sid": plugin_sid,
            "sid": sid,
        }
        self._uri = "/PluginService/Plugins/{plugin_sid}/Versions/{sid}".format(
            **self._solution
        )

    def fetch(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> PluginVersionsInstance:
        """
        Fetch the PluginVersionsInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The fetched PluginVersionsInstance
        """

        data = values.of(
            {
                "Flex-Metadata": flex_metadata,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return PluginVersionsInstance(
            self._version,
            payload,
            plugin_sid=self._solution["plugin_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> PluginVersionsInstance:
        """
        Asynchronous coroutine to fetch the PluginVersionsInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The fetched PluginVersionsInstance
        """

        data = values.of(
            {
                "Flex-Metadata": flex_metadata,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return PluginVersionsInstance(
            self._version,
            payload,
            plugin_sid=self._solution["plugin_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.PluginVersionsContext {}>".format(context)


class PluginVersionsPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> PluginVersionsInstance:
        """
        Build an instance of PluginVersionsInstance

        :param payload: Payload response from the API
        """
        return PluginVersionsInstance(
            self._version, payload, plugin_sid=self._solution["plugin_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V1.PluginVersionsPage>"


class PluginVersionsList(ListResource):

    def __init__(self, version: Version, plugin_sid: str):
        """
        Initialize the PluginVersionsList

        :param version: Version that contains the resource
        :param plugin_sid: The SID of the Flex Plugin the resource to belongs to.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "plugin_sid": plugin_sid,
        }
        self._uri = "/PluginService/Plugins/{plugin_sid}/Versions".format(
            **self._solution
        )

    def create(
        self,
        version: str,
        plugin_url: str,
        flex_metadata: Union[str, object] = values.unset,
        changelog: Union[str, object] = values.unset,
        private: Union[bool, object] = values.unset,
        cli_version: Union[str, object] = values.unset,
        validate_status: Union[str, object] = values.unset,
    ) -> PluginVersionsInstance:
        """
        Create the PluginVersionsInstance

        :param version: The Flex Plugin Version's version.
        :param plugin_url: The URL of the Flex Plugin Version bundle
        :param flex_metadata: The Flex-Metadata HTTP request header
        :param changelog: The changelog of the Flex Plugin Version.
        :param private: Whether this Flex Plugin Version requires authorization.
        :param cli_version: The version of Flex Plugins CLI used to create this plugin
        :param validate_status: The validation status of the plugin, indicating whether it has been validated

        :returns: The created PluginVersionsInstance
        """

        data = values.of(
            {
                "Version": version,
                "PluginUrl": plugin_url,
                "Changelog": changelog,
                "Private": serialize.boolean_to_string(private),
                "CliVersion": cli_version,
                "ValidateStatus": validate_status,
            }
        )
        headers = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return PluginVersionsInstance(
            self._version, payload, plugin_sid=self._solution["plugin_sid"]
        )

    async def create_async(
        self,
        version: str,
        plugin_url: str,
        flex_metadata: Union[str, object] = values.unset,
        changelog: Union[str, object] = values.unset,
        private: Union[bool, object] = values.unset,
        cli_version: Union[str, object] = values.unset,
        validate_status: Union[str, object] = values.unset,
    ) -> PluginVersionsInstance:
        """
        Asynchronously create the PluginVersionsInstance

        :param version: The Flex Plugin Version's version.
        :param plugin_url: The URL of the Flex Plugin Version bundle
        :param flex_metadata: The Flex-Metadata HTTP request header
        :param changelog: The changelog of the Flex Plugin Version.
        :param private: Whether this Flex Plugin Version requires authorization.
        :param cli_version: The version of Flex Plugins CLI used to create this plugin
        :param validate_status: The validation status of the plugin, indicating whether it has been validated

        :returns: The created PluginVersionsInstance
        """

        data = values.of(
            {
                "Version": version,
                "PluginUrl": plugin_url,
                "Changelog": changelog,
                "Private": serialize.boolean_to_string(private),
                "CliVersion": cli_version,
                "ValidateStatus": validate_status,
            }
        )
        headers = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return PluginVersionsInstance(
            self._version, payload, plugin_sid=self._solution["plugin_sid"]
        )

    def stream(
        self,
        flex_metadata: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[PluginVersionsInstance]:
        """
        Streams PluginVersionsInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str flex_metadata: The Flex-Metadata HTTP request header
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(flex_metadata=flex_metadata, page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        flex_metadata: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[PluginVersionsInstance]:
        """
        Asynchronously streams PluginVersionsInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str flex_metadata: The Flex-Metadata HTTP request header
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            flex_metadata=flex_metadata, page_size=limits["page_size"]
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        flex_metadata: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[PluginVersionsInstance]:
        """
        Lists PluginVersionsInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str flex_metadata: The Flex-Metadata HTTP request header
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                flex_metadata=flex_metadata,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        flex_metadata: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[PluginVersionsInstance]:
        """
        Asynchronously lists PluginVersionsInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str flex_metadata: The Flex-Metadata HTTP request header
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                flex_metadata=flex_metadata,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        flex_metadata: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> PluginVersionsPage:
        """
        Retrieve a single page of PluginVersionsInstance records from the API.
        Request is executed immediately

        :param flex_metadata: The Flex-Metadata HTTP request header
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of PluginVersionsInstance
        """
        data = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return PluginVersionsPage(self._version, response, self._solution)

    async def page_async(
        self,
        flex_metadata: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> PluginVersionsPage:
        """
        Asynchronously retrieve a single page of PluginVersionsInstance records from the API.
        Request is executed immediately

        :param flex_metadata: The Flex-Metadata HTTP request header
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of PluginVersionsInstance
        """
        data = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return PluginVersionsPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> PluginVersionsPage:
        """
        Retrieve a specific page of PluginVersionsInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of PluginVersionsInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return PluginVersionsPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> PluginVersionsPage:
        """
        Asynchronously retrieve a specific page of PluginVersionsInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of PluginVersionsInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return PluginVersionsPage(self._version, response, self._solution)

    def get(self, sid: str) -> PluginVersionsContext:
        """
        Constructs a PluginVersionsContext

        :param sid: The SID of the Flex Plugin Version resource to fetch.
        """
        return PluginVersionsContext(
            self._version, plugin_sid=self._solution["plugin_sid"], sid=sid
        )

    def __call__(self, sid: str) -> PluginVersionsContext:
        """
        Constructs a PluginVersionsContext

        :param sid: The SID of the Flex Plugin Version resource to fetch.
        """
        return PluginVersionsContext(
            self._version, plugin_sid=self._solution["plugin_sid"], sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V1.PluginVersionsList>"
