r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Numbers
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, Optional
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class PortingPortInPhoneNumberInstance(InstanceResource):
    """
    :ivar port_in_request_sid: The unique identifier for the port in request that this phone number is associated with.
    :ivar phone_number_sid: The unique identifier for this phone number associated with this port in request.
    :ivar url: URL reference for this resource.
    :ivar account_sid: Account Sid or subaccount where the phone number(s) will be Ported.
    :ivar phone_number_type: The number type of the phone number. This can be: toll-free, local, mobile or unknown. This field may be null if the number is not portable or if the portability for a number has not yet been evaluated.
    :ivar date_created: The timestamp for when this port in phone number was created.
    :ivar country: The ISO country code that this number is associated with. This field may be null if the number is not portable or if the portability for a number has not yet been evaluated.
    :ivar missing_required_fields: Indicates if the phone number is missing required fields such as a PIN or account number. This field may be null if the number is not portable or if the portability for a number has not yet been evaluated.
    :ivar last_updated: Timestamp indicating when the Port In Phone Number resource was last modified.
    :ivar phone_number: Phone number to be ported. This will be in the E164 Format.
    :ivar portable: If the number is portable by Twilio or not. This field may be null if the number portability has not yet been evaluated. If a number is not portable reference the `not_portability_reason_code` and `not_portability_reason` fields for more details
    :ivar not_portability_reason: The not portability reason code description. This field may be null if the number is portable or if the portability for a number has not yet been evaluated.
    :ivar not_portability_reason_code: The not portability reason code. This field may be null if the number is portable or if the portability for a number has not yet been evaluated.
    :ivar port_in_phone_number_status: The status of the port in phone number.
    :ivar port_out_pin: The pin required by the losing carrier to do the port out.
    :ivar rejection_reason: The description of the rejection reason provided by the losing carrier. This field may be null if the number has not been rejected by the losing carrier.
    :ivar rejection_reason_code: The code for the rejection reason provided by the losing carrier. This field may be null if the number has not been rejected by the losing carrier.
    :ivar port_date: The timestamp the phone number will be ported. This will only be set once a port date has been confirmed. Not all carriers can guarantee a specific time on the port date. Twilio will try its best to get the port completed by this time on the port date. Please subscribe to webhooks for confirmation on when a port has actually been completed.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        port_in_request_sid: Optional[str] = None,
        phone_number_sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.port_in_request_sid: Optional[str] = payload.get("port_in_request_sid")
        self.phone_number_sid: Optional[str] = payload.get("phone_number_sid")
        self.url: Optional[str] = payload.get("url")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.phone_number_type: Optional[str] = payload.get("phone_number_type")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.country: Optional[str] = payload.get("country")
        self.missing_required_fields: Optional[bool] = payload.get(
            "missing_required_fields"
        )
        self.last_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("last_updated")
        )
        self.phone_number: Optional[str] = payload.get("phone_number")
        self.portable: Optional[bool] = payload.get("portable")
        self.not_portability_reason: Optional[str] = payload.get(
            "not_portability_reason"
        )
        self.not_portability_reason_code: Optional[int] = deserialize.integer(
            payload.get("not_portability_reason_code")
        )
        self.port_in_phone_number_status: Optional[str] = payload.get(
            "port_in_phone_number_status"
        )
        self.port_out_pin: Optional[int] = deserialize.integer(
            payload.get("port_out_pin")
        )
        self.rejection_reason: Optional[str] = payload.get("rejection_reason")
        self.rejection_reason_code: Optional[int] = deserialize.integer(
            payload.get("rejection_reason_code")
        )
        self.port_date: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("port_date")
        )

        self._solution = {
            "port_in_request_sid": port_in_request_sid or self.port_in_request_sid,
            "phone_number_sid": phone_number_sid or self.phone_number_sid,
        }
        self._context: Optional[PortingPortInPhoneNumberContext] = None

    @property
    def _proxy(self) -> "PortingPortInPhoneNumberContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: PortingPortInPhoneNumberContext for this PortingPortInPhoneNumberInstance
        """
        if self._context is None:
            self._context = PortingPortInPhoneNumberContext(
                self._version,
                port_in_request_sid=self._solution["port_in_request_sid"],
                phone_number_sid=self._solution["phone_number_sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the PortingPortInPhoneNumberInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the PortingPortInPhoneNumberInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "PortingPortInPhoneNumberInstance":
        """
        Fetch the PortingPortInPhoneNumberInstance


        :returns: The fetched PortingPortInPhoneNumberInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "PortingPortInPhoneNumberInstance":
        """
        Asynchronous coroutine to fetch the PortingPortInPhoneNumberInstance


        :returns: The fetched PortingPortInPhoneNumberInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Numbers.V1.PortingPortInPhoneNumberInstance {}>".format(context)


class PortingPortInPhoneNumberContext(InstanceContext):

    def __init__(
        self, version: Version, port_in_request_sid: str, phone_number_sid: str
    ):
        """
        Initialize the PortingPortInPhoneNumberContext

        :param version: Version that contains the resource
        :param port_in_request_sid: The SID of the Port In request. This is a unique identifier of the port in request.
        :param phone_number_sid: The SID of the Phone number. This is a unique identifier of the phone number.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "port_in_request_sid": port_in_request_sid,
            "phone_number_sid": phone_number_sid,
        }
        self._uri = "/Porting/PortIn/{port_in_request_sid}/PhoneNumber/{phone_number_sid}".format(
            **self._solution
        )

    def delete(self) -> bool:
        """
        Deletes the PortingPortInPhoneNumberInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the PortingPortInPhoneNumberInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> PortingPortInPhoneNumberInstance:
        """
        Fetch the PortingPortInPhoneNumberInstance


        :returns: The fetched PortingPortInPhoneNumberInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return PortingPortInPhoneNumberInstance(
            self._version,
            payload,
            port_in_request_sid=self._solution["port_in_request_sid"],
            phone_number_sid=self._solution["phone_number_sid"],
        )

    async def fetch_async(self) -> PortingPortInPhoneNumberInstance:
        """
        Asynchronous coroutine to fetch the PortingPortInPhoneNumberInstance


        :returns: The fetched PortingPortInPhoneNumberInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return PortingPortInPhoneNumberInstance(
            self._version,
            payload,
            port_in_request_sid=self._solution["port_in_request_sid"],
            phone_number_sid=self._solution["phone_number_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Numbers.V1.PortingPortInPhoneNumberContext {}>".format(context)


class PortingPortInPhoneNumberList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the PortingPortInPhoneNumberList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(
        self, port_in_request_sid: str, phone_number_sid: str
    ) -> PortingPortInPhoneNumberContext:
        """
        Constructs a PortingPortInPhoneNumberContext

        :param port_in_request_sid: The SID of the Port In request. This is a unique identifier of the port in request.
        :param phone_number_sid: The SID of the Phone number. This is a unique identifier of the phone number.
        """
        return PortingPortInPhoneNumberContext(
            self._version,
            port_in_request_sid=port_in_request_sid,
            phone_number_sid=phone_number_sid,
        )

    def __call__(
        self, port_in_request_sid: str, phone_number_sid: str
    ) -> PortingPortInPhoneNumberContext:
        """
        Constructs a PortingPortInPhoneNumberContext

        :param port_in_request_sid: The SID of the Port In request. This is a unique identifier of the port in request.
        :param phone_number_sid: The SID of the Phone number. This is a unique identifier of the phone number.
        """
        return PortingPortInPhoneNumberContext(
            self._version,
            port_in_request_sid=port_in_request_sid,
            phone_number_sid=phone_number_sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Numbers.V1.PortingPortInPhoneNumberList>"
