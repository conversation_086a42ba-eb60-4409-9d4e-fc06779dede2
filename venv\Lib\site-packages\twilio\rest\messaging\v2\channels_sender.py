r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Messaging
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class ChannelsSenderInstance(InstanceResource):

    class MessagingV2ChannelsSenderConfiguration(object):
        """
        :ivar waba_id: The ID of the WhatsApp Business Account to use for this sender.
        :ivar verification_method: The method to use for verification. Either \"sms\" or \"voice\".
        :ivar verification_code: The verification code to use for this sender.
        :ivar voice_application_sid: The SID of the Twilio Voice application to use for this sender.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.waba_id: Optional[str] = payload.get("waba_id")
            self.verification_method: Optional["ChannelsSenderInstance.str"] = (
                payload.get("verification_method")
            )
            self.verification_code: Optional[str] = payload.get("verification_code")
            self.voice_application_sid: Optional[str] = payload.get(
                "voice_application_sid"
            )

        def to_dict(self):
            return {
                "waba_id": self.waba_id,
                "verification_method": self.verification_method,
                "verification_code": self.verification_code,
                "voice_application_sid": self.voice_application_sid,
            }

    class MessagingV2ChannelsSenderProfile(object):
        """
        :ivar name: The name of the sender.
        :ivar about: The about text of the sender.
        :ivar address: The address of the sender.
        :ivar description: The description of the sender.
        :ivar emails: The emails of the sender.
        :ivar logo_url: The logo URL of the sender.
        :ivar vertical: The vertical of the sender. Allowed values are: - \"Automotive\" - \"Beauty, Spa and Salon\" - \"Clothing and Apparel\" - \"Education\" - \"Entertainment\" - \"Event Planning and Service\" - \"Finance and Banking\" - \"Food and Grocery\" - \"Public Service\" - \"Hotel and Lodging\" - \"Medical and Health\" - \"Non-profit\" - \"Professional Services\" - \"Shopping and Retail\" - \"Travel and Transportation\" - \"Restaurant\" - \"Other\"
        :ivar websites: The websites of the sender.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.name: Optional[str] = payload.get("name")
            self.about: Optional[str] = payload.get("about")
            self.address: Optional[str] = payload.get("address")
            self.description: Optional[str] = payload.get("description")
            self.emails: Optional[
                List[ChannelsSenderList.MessagingV2ChannelsSenderProfileEmails]
            ] = payload.get("emails")
            self.logo_url: Optional[str] = payload.get("logo_url")
            self.vertical: Optional[str] = payload.get("vertical")
            self.websites: Optional[
                List[ChannelsSenderList.MessagingV2ChannelsSenderProfileWebsites]
            ] = payload.get("websites")

        def to_dict(self):
            return {
                "name": self.name,
                "about": self.about,
                "address": self.address,
                "description": self.description,
                "emails": (
                    [emails.to_dict() for emails in self.emails]
                    if self.emails is not None
                    else None
                ),
                "logo_url": self.logo_url,
                "vertical": self.vertical,
                "websites": (
                    [websites.to_dict() for websites in self.websites]
                    if self.websites is not None
                    else None
                ),
            }

    class MessagingV2ChannelsSenderProfileEmails(object):
        """
        :ivar email: The email of the sender.
        :ivar label: The label of the sender.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.email: Optional[str] = payload.get("email")
            self.label: Optional[str] = payload.get("label")

        def to_dict(self):
            return {
                "email": self.email,
                "label": self.label,
            }

    class MessagingV2ChannelsSenderProfileWebsites(object):
        """
        :ivar label: The label of the sender.
        :ivar website: The website of the sender.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.label: Optional[str] = payload.get("label")
            self.website: Optional[str] = payload.get("website")

        def to_dict(self):
            return {
                "label": self.label,
                "website": self.website,
            }

    class MessagingV2ChannelsSenderRequestsCreate(object):
        """
        :ivar sender_id: The ID of this Sender prefixed with the channel, e.g., `whatsapp:E.164`
        :ivar configuration:
        :ivar webhook:
        :ivar profile:
        """

        def __init__(self, payload: Dict[str, Any]):

            self.sender_id: Optional[str] = payload.get("sender_id")
            self.configuration: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderConfiguration
            ] = payload.get("configuration")
            self.webhook: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderWebhook
            ] = payload.get("webhook")
            self.profile: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderProfile
            ] = payload.get("profile")

        def to_dict(self):
            return {
                "sender_id": self.sender_id,
                "configuration": (
                    self.configuration.to_dict()
                    if self.configuration is not None
                    else None
                ),
                "webhook": self.webhook.to_dict() if self.webhook is not None else None,
                "profile": self.profile.to_dict() if self.profile is not None else None,
            }

    class MessagingV2ChannelsSenderRequestsUpdate(object):
        """
        :ivar configuration:
        :ivar webhook:
        :ivar profile:
        """

        def __init__(self, payload: Dict[str, Any]):

            self.configuration: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderConfiguration
            ] = payload.get("configuration")
            self.webhook: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderWebhook
            ] = payload.get("webhook")
            self.profile: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderProfile
            ] = payload.get("profile")

        def to_dict(self):
            return {
                "configuration": (
                    self.configuration.to_dict()
                    if self.configuration is not None
                    else None
                ),
                "webhook": self.webhook.to_dict() if self.webhook is not None else None,
                "profile": self.profile.to_dict() if self.profile is not None else None,
            }

    class MessagingV2ChannelsSenderWebhook(object):
        """
        :ivar callback_url: The URL to send the webhook to.
        :ivar callback_method: The HTTP method to use for the webhook. Either \"POST\" or \"PUT\".
        :ivar fallback_url: The URL to send the fallback webhook to.
        :ivar fallback_method: The HTTP method to use for the fallback webhook. Either \"POST\" or \"PUT\".
        :ivar status_callback_url: The URL to send the status callback to.
        :ivar status_callback_method: The HTTP method to use for the status callback.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.callback_url: Optional[str] = payload.get("callback_url")
            self.callback_method: Optional["ChannelsSenderInstance.str"] = payload.get(
                "callback_method"
            )
            self.fallback_url: Optional[str] = payload.get("fallback_url")
            self.fallback_method: Optional["ChannelsSenderInstance.str"] = payload.get(
                "fallback_method"
            )
            self.status_callback_url: Optional[str] = payload.get("status_callback_url")
            self.status_callback_method: Optional[str] = payload.get(
                "status_callback_method"
            )

        def to_dict(self):
            return {
                "callback_url": self.callback_url,
                "callback_method": self.callback_method,
                "fallback_url": self.fallback_url,
                "fallback_method": self.fallback_method,
                "status_callback_url": self.status_callback_url,
                "status_callback_method": self.status_callback_method,
            }

    class Status(object):
        CREATING = "CREATING"
        ONLINE = "ONLINE"
        OFFLINE = "OFFLINE"
        PENDING_VERIFICATION = "PENDING_VERIFICATION"
        VERIFYING = "VERIFYING"
        ONLINE_UPDATING = "ONLINE:UPDATING"
        STUBBED = "STUBBED"

    """
    :ivar sid: A 34 character string that uniquely identifies this Sender.
    :ivar status: 
    :ivar sender_id: The ID of this Sender prefixed with the channel, e.g., `whatsapp:E.164`
    :ivar configuration: 
    :ivar webhook: 
    :ivar profile: 
    :ivar properties: 
    :ivar offline_reasons: Reasons why the sender is offline., e.g., [{\"code\": \"********\", \"message\": \"Whatsapp business account is banned by provider {provider_name} | Credit line is assigned to another BSP\", \"more_info\": \"https://www.twilio.com/docs/errors/********\"}]
    :ivar url: The URL of this resource, relative to `https://messaging.twilio.com`.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.status: Optional["ChannelsSenderInstance.Status"] = payload.get("status")
        self.sender_id: Optional[str] = payload.get("sender_id")
        self.configuration: Optional[str] = payload.get("configuration")
        self.webhook: Optional[str] = payload.get("webhook")
        self.profile: Optional[str] = payload.get("profile")
        self.properties: Optional[str] = payload.get("properties")
        self.offline_reasons: Optional[List[str]] = payload.get("offline_reasons")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[ChannelsSenderContext] = None

    @property
    def _proxy(self) -> "ChannelsSenderContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ChannelsSenderContext for this ChannelsSenderInstance
        """
        if self._context is None:
            self._context = ChannelsSenderContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the ChannelsSenderInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ChannelsSenderInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "ChannelsSenderInstance":
        """
        Fetch the ChannelsSenderInstance


        :returns: The fetched ChannelsSenderInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ChannelsSenderInstance":
        """
        Asynchronous coroutine to fetch the ChannelsSenderInstance


        :returns: The fetched ChannelsSenderInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        messaging_v2_channels_sender_requests_update: Union[
            MessagingV2ChannelsSenderRequestsUpdate, object
        ] = values.unset,
    ) -> "ChannelsSenderInstance":
        """
        Update the ChannelsSenderInstance

        :param messaging_v2_channels_sender_requests_update:

        :returns: The updated ChannelsSenderInstance
        """
        return self._proxy.update(
            messaging_v2_channels_sender_requests_update=messaging_v2_channels_sender_requests_update,
        )

    async def update_async(
        self,
        messaging_v2_channels_sender_requests_update: Union[
            MessagingV2ChannelsSenderRequestsUpdate, object
        ] = values.unset,
    ) -> "ChannelsSenderInstance":
        """
        Asynchronous coroutine to update the ChannelsSenderInstance

        :param messaging_v2_channels_sender_requests_update:

        :returns: The updated ChannelsSenderInstance
        """
        return await self._proxy.update_async(
            messaging_v2_channels_sender_requests_update=messaging_v2_channels_sender_requests_update,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Messaging.V2.ChannelsSenderInstance {}>".format(context)


class ChannelsSenderContext(InstanceContext):

    class MessagingV2ChannelsSenderConfiguration(object):
        """
        :ivar waba_id: The ID of the WhatsApp Business Account to use for this sender.
        :ivar verification_method: The method to use for verification. Either \"sms\" or \"voice\".
        :ivar verification_code: The verification code to use for this sender.
        :ivar voice_application_sid: The SID of the Twilio Voice application to use for this sender.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.waba_id: Optional[str] = payload.get("waba_id")
            self.verification_method: Optional["ChannelsSenderInstance.str"] = (
                payload.get("verification_method")
            )
            self.verification_code: Optional[str] = payload.get("verification_code")
            self.voice_application_sid: Optional[str] = payload.get(
                "voice_application_sid"
            )

        def to_dict(self):
            return {
                "waba_id": self.waba_id,
                "verification_method": self.verification_method,
                "verification_code": self.verification_code,
                "voice_application_sid": self.voice_application_sid,
            }

    class MessagingV2ChannelsSenderProfile(object):
        """
        :ivar name: The name of the sender.
        :ivar about: The about text of the sender.
        :ivar address: The address of the sender.
        :ivar description: The description of the sender.
        :ivar emails: The emails of the sender.
        :ivar logo_url: The logo URL of the sender.
        :ivar vertical: The vertical of the sender. Allowed values are: - \"Automotive\" - \"Beauty, Spa and Salon\" - \"Clothing and Apparel\" - \"Education\" - \"Entertainment\" - \"Event Planning and Service\" - \"Finance and Banking\" - \"Food and Grocery\" - \"Public Service\" - \"Hotel and Lodging\" - \"Medical and Health\" - \"Non-profit\" - \"Professional Services\" - \"Shopping and Retail\" - \"Travel and Transportation\" - \"Restaurant\" - \"Other\"
        :ivar websites: The websites of the sender.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.name: Optional[str] = payload.get("name")
            self.about: Optional[str] = payload.get("about")
            self.address: Optional[str] = payload.get("address")
            self.description: Optional[str] = payload.get("description")
            self.emails: Optional[
                List[ChannelsSenderList.MessagingV2ChannelsSenderProfileEmails]
            ] = payload.get("emails")
            self.logo_url: Optional[str] = payload.get("logo_url")
            self.vertical: Optional[str] = payload.get("vertical")
            self.websites: Optional[
                List[ChannelsSenderList.MessagingV2ChannelsSenderProfileWebsites]
            ] = payload.get("websites")

        def to_dict(self):
            return {
                "name": self.name,
                "about": self.about,
                "address": self.address,
                "description": self.description,
                "emails": (
                    [emails.to_dict() for emails in self.emails]
                    if self.emails is not None
                    else None
                ),
                "logo_url": self.logo_url,
                "vertical": self.vertical,
                "websites": (
                    [websites.to_dict() for websites in self.websites]
                    if self.websites is not None
                    else None
                ),
            }

    class MessagingV2ChannelsSenderProfileEmails(object):
        """
        :ivar email: The email of the sender.
        :ivar label: The label of the sender.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.email: Optional[str] = payload.get("email")
            self.label: Optional[str] = payload.get("label")

        def to_dict(self):
            return {
                "email": self.email,
                "label": self.label,
            }

    class MessagingV2ChannelsSenderProfileWebsites(object):
        """
        :ivar label: The label of the sender.
        :ivar website: The website of the sender.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.label: Optional[str] = payload.get("label")
            self.website: Optional[str] = payload.get("website")

        def to_dict(self):
            return {
                "label": self.label,
                "website": self.website,
            }

    class MessagingV2ChannelsSenderRequestsCreate(object):
        """
        :ivar sender_id: The ID of this Sender prefixed with the channel, e.g., `whatsapp:E.164`
        :ivar configuration:
        :ivar webhook:
        :ivar profile:
        """

        def __init__(self, payload: Dict[str, Any]):

            self.sender_id: Optional[str] = payload.get("sender_id")
            self.configuration: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderConfiguration
            ] = payload.get("configuration")
            self.webhook: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderWebhook
            ] = payload.get("webhook")
            self.profile: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderProfile
            ] = payload.get("profile")

        def to_dict(self):
            return {
                "sender_id": self.sender_id,
                "configuration": (
                    self.configuration.to_dict()
                    if self.configuration is not None
                    else None
                ),
                "webhook": self.webhook.to_dict() if self.webhook is not None else None,
                "profile": self.profile.to_dict() if self.profile is not None else None,
            }

    class MessagingV2ChannelsSenderRequestsUpdate(object):
        """
        :ivar configuration:
        :ivar webhook:
        :ivar profile:
        """

        def __init__(self, payload: Dict[str, Any]):

            self.configuration: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderConfiguration
            ] = payload.get("configuration")
            self.webhook: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderWebhook
            ] = payload.get("webhook")
            self.profile: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderProfile
            ] = payload.get("profile")

        def to_dict(self):
            return {
                "configuration": (
                    self.configuration.to_dict()
                    if self.configuration is not None
                    else None
                ),
                "webhook": self.webhook.to_dict() if self.webhook is not None else None,
                "profile": self.profile.to_dict() if self.profile is not None else None,
            }

    class MessagingV2ChannelsSenderWebhook(object):
        """
        :ivar callback_url: The URL to send the webhook to.
        :ivar callback_method: The HTTP method to use for the webhook. Either \"POST\" or \"PUT\".
        :ivar fallback_url: The URL to send the fallback webhook to.
        :ivar fallback_method: The HTTP method to use for the fallback webhook. Either \"POST\" or \"PUT\".
        :ivar status_callback_url: The URL to send the status callback to.
        :ivar status_callback_method: The HTTP method to use for the status callback.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.callback_url: Optional[str] = payload.get("callback_url")
            self.callback_method: Optional["ChannelsSenderInstance.str"] = payload.get(
                "callback_method"
            )
            self.fallback_url: Optional[str] = payload.get("fallback_url")
            self.fallback_method: Optional["ChannelsSenderInstance.str"] = payload.get(
                "fallback_method"
            )
            self.status_callback_url: Optional[str] = payload.get("status_callback_url")
            self.status_callback_method: Optional[str] = payload.get(
                "status_callback_method"
            )

        def to_dict(self):
            return {
                "callback_url": self.callback_url,
                "callback_method": self.callback_method,
                "fallback_url": self.fallback_url,
                "fallback_method": self.fallback_method,
                "status_callback_url": self.status_callback_url,
                "status_callback_method": self.status_callback_method,
            }

    def __init__(self, version: Version, sid: str):
        """
        Initialize the ChannelsSenderContext

        :param version: Version that contains the resource
        :param sid: A 34 character string that uniquely identifies this Sender.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/Channels/Senders/{sid}".format(**self._solution)

    def delete(self) -> bool:
        """
        Deletes the ChannelsSenderInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ChannelsSenderInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> ChannelsSenderInstance:
        """
        Fetch the ChannelsSenderInstance


        :returns: The fetched ChannelsSenderInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ChannelsSenderInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> ChannelsSenderInstance:
        """
        Asynchronous coroutine to fetch the ChannelsSenderInstance


        :returns: The fetched ChannelsSenderInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ChannelsSenderInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def update(
        self,
        messaging_v2_channels_sender_requests_update: Union[
            MessagingV2ChannelsSenderRequestsUpdate, object
        ] = values.unset,
    ) -> ChannelsSenderInstance:
        """
        Update the ChannelsSenderInstance

        :param messaging_v2_channels_sender_requests_update:

        :returns: The updated ChannelsSenderInstance
        """
        data = messaging_v2_channels_sender_requests_update.to_dict()

        headers = values.of({})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ChannelsSenderInstance(self._version, payload, sid=self._solution["sid"])

    async def update_async(
        self,
        messaging_v2_channels_sender_requests_update: Union[
            MessagingV2ChannelsSenderRequestsUpdate, object
        ] = values.unset,
    ) -> ChannelsSenderInstance:
        """
        Asynchronous coroutine to update the ChannelsSenderInstance

        :param messaging_v2_channels_sender_requests_update:

        :returns: The updated ChannelsSenderInstance
        """
        data = messaging_v2_channels_sender_requests_update.to_dict()

        headers = values.of({})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ChannelsSenderInstance(self._version, payload, sid=self._solution["sid"])

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Messaging.V2.ChannelsSenderContext {}>".format(context)


class ChannelsSenderPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> ChannelsSenderInstance:
        """
        Build an instance of ChannelsSenderInstance

        :param payload: Payload response from the API
        """
        return ChannelsSenderInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Messaging.V2.ChannelsSenderPage>"


class ChannelsSenderList(ListResource):

    class MessagingV2ChannelsSenderConfiguration(object):
        """
        :ivar waba_id: The ID of the WhatsApp Business Account to use for this sender.
        :ivar verification_method: The method to use for verification. Either \"sms\" or \"voice\".
        :ivar verification_code: The verification code to use for this sender.
        :ivar voice_application_sid: The SID of the Twilio Voice application to use for this sender.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.waba_id: Optional[str] = payload.get("waba_id")
            self.verification_method: Optional["ChannelsSenderInstance.str"] = (
                payload.get("verification_method")
            )
            self.verification_code: Optional[str] = payload.get("verification_code")
            self.voice_application_sid: Optional[str] = payload.get(
                "voice_application_sid"
            )

        def to_dict(self):
            return {
                "waba_id": self.waba_id,
                "verification_method": self.verification_method,
                "verification_code": self.verification_code,
                "voice_application_sid": self.voice_application_sid,
            }

    class MessagingV2ChannelsSenderProfile(object):
        """
        :ivar name: The name of the sender.
        :ivar about: The about text of the sender.
        :ivar address: The address of the sender.
        :ivar description: The description of the sender.
        :ivar emails: The emails of the sender.
        :ivar logo_url: The logo URL of the sender.
        :ivar vertical: The vertical of the sender. Allowed values are: - \"Automotive\" - \"Beauty, Spa and Salon\" - \"Clothing and Apparel\" - \"Education\" - \"Entertainment\" - \"Event Planning and Service\" - \"Finance and Banking\" - \"Food and Grocery\" - \"Public Service\" - \"Hotel and Lodging\" - \"Medical and Health\" - \"Non-profit\" - \"Professional Services\" - \"Shopping and Retail\" - \"Travel and Transportation\" - \"Restaurant\" - \"Other\"
        :ivar websites: The websites of the sender.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.name: Optional[str] = payload.get("name")
            self.about: Optional[str] = payload.get("about")
            self.address: Optional[str] = payload.get("address")
            self.description: Optional[str] = payload.get("description")
            self.emails: Optional[
                List[ChannelsSenderList.MessagingV2ChannelsSenderProfileEmails]
            ] = payload.get("emails")
            self.logo_url: Optional[str] = payload.get("logo_url")
            self.vertical: Optional[str] = payload.get("vertical")
            self.websites: Optional[
                List[ChannelsSenderList.MessagingV2ChannelsSenderProfileWebsites]
            ] = payload.get("websites")

        def to_dict(self):
            return {
                "name": self.name,
                "about": self.about,
                "address": self.address,
                "description": self.description,
                "emails": (
                    [emails.to_dict() for emails in self.emails]
                    if self.emails is not None
                    else None
                ),
                "logo_url": self.logo_url,
                "vertical": self.vertical,
                "websites": (
                    [websites.to_dict() for websites in self.websites]
                    if self.websites is not None
                    else None
                ),
            }

    class MessagingV2ChannelsSenderProfileEmails(object):
        """
        :ivar email: The email of the sender.
        :ivar label: The label of the sender.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.email: Optional[str] = payload.get("email")
            self.label: Optional[str] = payload.get("label")

        def to_dict(self):
            return {
                "email": self.email,
                "label": self.label,
            }

    class MessagingV2ChannelsSenderProfileWebsites(object):
        """
        :ivar label: The label of the sender.
        :ivar website: The website of the sender.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.label: Optional[str] = payload.get("label")
            self.website: Optional[str] = payload.get("website")

        def to_dict(self):
            return {
                "label": self.label,
                "website": self.website,
            }

    class MessagingV2ChannelsSenderRequestsCreate(object):
        """
        :ivar sender_id: The ID of this Sender prefixed with the channel, e.g., `whatsapp:E.164`
        :ivar configuration:
        :ivar webhook:
        :ivar profile:
        """

        def __init__(self, payload: Dict[str, Any]):

            self.sender_id: Optional[str] = payload.get("sender_id")
            self.configuration: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderConfiguration
            ] = payload.get("configuration")
            self.webhook: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderWebhook
            ] = payload.get("webhook")
            self.profile: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderProfile
            ] = payload.get("profile")

        def to_dict(self):
            return {
                "sender_id": self.sender_id,
                "configuration": (
                    self.configuration.to_dict()
                    if self.configuration is not None
                    else None
                ),
                "webhook": self.webhook.to_dict() if self.webhook is not None else None,
                "profile": self.profile.to_dict() if self.profile is not None else None,
            }

    class MessagingV2ChannelsSenderRequestsUpdate(object):
        """
        :ivar configuration:
        :ivar webhook:
        :ivar profile:
        """

        def __init__(self, payload: Dict[str, Any]):

            self.configuration: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderConfiguration
            ] = payload.get("configuration")
            self.webhook: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderWebhook
            ] = payload.get("webhook")
            self.profile: Optional[
                ChannelsSenderList.MessagingV2ChannelsSenderProfile
            ] = payload.get("profile")

        def to_dict(self):
            return {
                "configuration": (
                    self.configuration.to_dict()
                    if self.configuration is not None
                    else None
                ),
                "webhook": self.webhook.to_dict() if self.webhook is not None else None,
                "profile": self.profile.to_dict() if self.profile is not None else None,
            }

    class MessagingV2ChannelsSenderWebhook(object):
        """
        :ivar callback_url: The URL to send the webhook to.
        :ivar callback_method: The HTTP method to use for the webhook. Either \"POST\" or \"PUT\".
        :ivar fallback_url: The URL to send the fallback webhook to.
        :ivar fallback_method: The HTTP method to use for the fallback webhook. Either \"POST\" or \"PUT\".
        :ivar status_callback_url: The URL to send the status callback to.
        :ivar status_callback_method: The HTTP method to use for the status callback.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.callback_url: Optional[str] = payload.get("callback_url")
            self.callback_method: Optional["ChannelsSenderInstance.str"] = payload.get(
                "callback_method"
            )
            self.fallback_url: Optional[str] = payload.get("fallback_url")
            self.fallback_method: Optional["ChannelsSenderInstance.str"] = payload.get(
                "fallback_method"
            )
            self.status_callback_url: Optional[str] = payload.get("status_callback_url")
            self.status_callback_method: Optional[str] = payload.get(
                "status_callback_method"
            )

        def to_dict(self):
            return {
                "callback_url": self.callback_url,
                "callback_method": self.callback_method,
                "fallback_url": self.fallback_url,
                "fallback_method": self.fallback_method,
                "status_callback_url": self.status_callback_url,
                "status_callback_method": self.status_callback_method,
            }

    def __init__(self, version: Version):
        """
        Initialize the ChannelsSenderList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Channels/Senders"

    def create(
        self,
        messaging_v2_channels_sender_requests_create: MessagingV2ChannelsSenderRequestsCreate,
    ) -> ChannelsSenderInstance:
        """
        Create the ChannelsSenderInstance

        :param messaging_v2_channels_sender_requests_create:

        :returns: The created ChannelsSenderInstance
        """
        data = messaging_v2_channels_sender_requests_create.to_dict()

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ChannelsSenderInstance(self._version, payload)

    async def create_async(
        self,
        messaging_v2_channels_sender_requests_create: MessagingV2ChannelsSenderRequestsCreate,
    ) -> ChannelsSenderInstance:
        """
        Asynchronously create the ChannelsSenderInstance

        :param messaging_v2_channels_sender_requests_create:

        :returns: The created ChannelsSenderInstance
        """
        data = messaging_v2_channels_sender_requests_create.to_dict()

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ChannelsSenderInstance(self._version, payload)

    def stream(
        self,
        channel: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ChannelsSenderInstance]:
        """
        Streams ChannelsSenderInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str channel:
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(channel=channel, page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        channel: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ChannelsSenderInstance]:
        """
        Asynchronously streams ChannelsSenderInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str channel:
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(channel=channel, page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        channel: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ChannelsSenderInstance]:
        """
        Lists ChannelsSenderInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str channel:
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                channel=channel,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        channel: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ChannelsSenderInstance]:
        """
        Asynchronously lists ChannelsSenderInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str channel:
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                channel=channel,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        channel: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ChannelsSenderPage:
        """
        Retrieve a single page of ChannelsSenderInstance records from the API.
        Request is executed immediately

        :param channel:
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ChannelsSenderInstance
        """
        data = values.of(
            {
                "Channel": channel,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ChannelsSenderPage(self._version, response)

    async def page_async(
        self,
        channel: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ChannelsSenderPage:
        """
        Asynchronously retrieve a single page of ChannelsSenderInstance records from the API.
        Request is executed immediately

        :param channel:
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ChannelsSenderInstance
        """
        data = values.of(
            {
                "Channel": channel,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ChannelsSenderPage(self._version, response)

    def get_page(self, target_url: str) -> ChannelsSenderPage:
        """
        Retrieve a specific page of ChannelsSenderInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ChannelsSenderInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ChannelsSenderPage(self._version, response)

    async def get_page_async(self, target_url: str) -> ChannelsSenderPage:
        """
        Asynchronously retrieve a specific page of ChannelsSenderInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ChannelsSenderInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ChannelsSenderPage(self._version, response)

    def get(self, sid: str) -> ChannelsSenderContext:
        """
        Constructs a ChannelsSenderContext

        :param sid: A 34 character string that uniquely identifies this Sender.
        """
        return ChannelsSenderContext(self._version, sid=sid)

    def __call__(self, sid: str) -> ChannelsSenderContext:
        """
        Constructs a ChannelsSenderContext

        :param sid: A 34 character string that uniquely identifies this Sender.
        """
        return ChannelsSenderContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Messaging.V2.ChannelsSenderList>"
