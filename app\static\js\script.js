// Global variables
let map;
let markers;
let reports = [];

// Initialize the map
function initMap() {
    // Create map centered on South Africa
    map = L.map('map').setView([-30.5595, 22.9375], 6);
    
    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);
    
    // Initialize marker cluster group
    markers = L.markerClusterGroup();
    map.addLayer(markers);
    
    // Add event listeners for filters
    document.getElementById('category-filter').addEventListener('change', filterReports);
    document.getElementById('days-filter').addEventListener('change', filterReports);
}

// Load reports from API
function loadReports() {
    // Get filter values
    const daysFilter = document.getElementById('days-filter').value;
    
    // Fetch reports from API
    fetch(`/api/reports?days=${daysFilter}`)
        .then(response => response.json())
        .then(data => {
            reports = data;
            displayReports(reports);
        })
        .catch(error => {
            console.error('Error fetching reports:', error);
        });
}

// Display reports on the map
function displayReports(reportsToShow) {
    // Clear existing markers
    markers.clearLayers();
    
    // Add markers for each report
    reportsToShow.forEach(report => {
        // Create marker
        const marker = L.marker([report.location.latitude, report.location.longitude]);
        
        // Set marker color based on category
        const markerIcon = getMarkerIcon(report.category);
        marker.setIcon(markerIcon);
        
        // Create popup content
        const popupContent = `
            <div class="report-popup">
                <h3>${getCategoryLabel(report.category)}</h3>
                <p>${report.description}</p>
                <p><strong>Date:</strong> ${formatDate(report.timestamp)}</p>
                ${report.image_url ? `<img src="${report.image_url}" alt="Report Image" style="max-width: 100%; max-height: 200px;">` : ''}
            </div>
        `;
        
        // Add popup to marker
        marker.bindPopup(popupContent);
        
        // Add marker to cluster group
        markers.addLayer(marker);
    });
}

// Filter reports based on selected filters
function filterReports() {
    // Get filter values
    const categoryFilter = document.getElementById('category-filter').value;
    const daysFilter = document.getElementById('days-filter').value;
    
    // If we need to fetch new data based on days filter
    if (daysFilter !== document.getElementById('days-filter').dataset.lastValue) {
        loadReports();
        document.getElementById('days-filter').dataset.lastValue = daysFilter;
        return;
    }
    
    // Filter reports based on category
    let filteredReports = reports;
    if (categoryFilter !== 'all') {
        filteredReports = reports.filter(report => report.category === categoryFilter);
    }
    
    // Display filtered reports
    displayReports(filteredReports);
}

// Get marker icon based on category
function getMarkerIcon(category) {
    // Define colors for each category
    const colors = {
        'crime': 'red',
        'ems': 'blue',
        'infrastructure': 'orange'
    };
    
    // Get color for category
    const color = colors[category] || 'gray';
    
    // Create icon
    return L.divIcon({
        className: `marker-icon marker-${category}`,
        html: `<div style="background-color: ${color}; width: 100%; height: 100%; border-radius: 50%;"></div>`,
        iconSize: [20, 20],
        iconAnchor: [10, 10]
    });
}

// Get human-readable category label
function getCategoryLabel(category) {
    const labels = {
        'crime': 'Crime Report',
        'ems': 'Emergency Services',
        'infrastructure': 'Infrastructure Issue'
    };
    
    return labels[category] || 'Report';
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString();
}
