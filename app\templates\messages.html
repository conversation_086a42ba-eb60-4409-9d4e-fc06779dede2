{% extends "base.html" %}

{% block title %}WhatsApp Messages - Hello Cyril{% endblock %}

{% block extra_css %}
<style>
  .message-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    padding: 20px;
    transition: transform 0.3s ease;
  }
  
  .message-card:hover {
    transform: translateY(-5px);
  }
  
  .message-header {
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
    padding-bottom: 10px;
  }
  
  .message-header h3 {
    color: #ee4d4d;
    margin: 0;
  }
  
  .message-meta {
    color: #888;
    font-size: 0.9em;
    margin-top: 5px;
  }
  
  .message-content {
    margin-bottom: 15px;
  }
  
  .message-image {
    border-radius: 5px;
    max-width: 100%;
    max-height: 200px;
  }
  
  .message-location {
    background-color: #f5f5f5;
    border-radius: 5px;
    margin-top: 10px;
    padding: 10px;
  }
  
  .category-badge {
    border-radius: 20px;
    display: inline-block;
    font-size: 0.8em;
    margin-left: 10px;
    padding: 3px 10px;
    text-transform: uppercase;
  }
  
  .category-crime {
    background-color: #ffebee;
    color: #c62828;
  }
  
  .category-ems {
    background-color: #e3f2fd;
    color: #1565c0;
  }
  
  .category-infrastructure {
    background-color: #fff8e1;
    color: #ff8f00;
  }
  
  .refresh-btn {
    background-color: #ee4d4d;
    border: none;
    border-radius: 5px;
    color: white;
    cursor: pointer;
    font-family: "Dosis", sans-serif;
    font-size: 16px;
    margin-bottom: 20px;
    padding: 10px 20px;
    text-transform: uppercase;
    transition: background-color 0.3s ease;
  }
  
  .refresh-btn:hover {
    background-color: #d32f2f;
  }
  
  .no-messages {
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
  }
</style>
{% endblock %}

{% block content %}
<h1 class="project-name">WHATSAPP MESSAGES DASHBOARD</h1>

<div class="dashboard-controls">
  <button class="refresh-btn" onclick="loadMessages()">Refresh Messages</button>
</div>

<div id="messages-container">
  <div class="loading">Loading messages...</div>
</div>

{% endblock %}

{% block extra_js %}
<script>
  // Load messages when the page loads
  document.addEventListener('DOMContentLoaded', function() {
    loadMessages();
  });
  
  // Function to load messages from the API
  function loadMessages() {
    const container = document.getElementById('messages-container');
    container.innerHTML = '<div class="loading">Loading messages...</div>';
    
    fetch('/api/reports')
      .then(response => response.json())
      .then(data => {
        displayMessages(data);
      })
      .catch(error => {
        console.error('Error fetching messages:', error);
        container.innerHTML = '<div class="no-messages">Error loading messages. Please try again.</div>';
      });
  }
  
  // Function to display messages
  function displayMessages(messages) {
    const container = document.getElementById('messages-container');
    
    if (messages.length === 0) {
      container.innerHTML = '<div class="no-messages">No messages received yet. Send a WhatsApp message to see it here.</div>';
      return;
    }
    
    let html = '';
    
    messages.forEach(message => {
      const categoryClass = `category-${message.category}`;
      const formattedDate = new Date(message.timestamp).toLocaleString();
      
      html += `
        <div class="message-card">
          <div class="message-header">
            <h3>
              Report #${message.id.substring(0, 8)}
              <span class="category-badge ${categoryClass}">${message.category}</span>
            </h3>
            <div class="message-meta">
              Received: ${formattedDate}
            </div>
          </div>
          
          <div class="message-content">
            <p>${message.description}</p>
            
            ${message.image_url ? `
              <div class="message-image-container">
                <img src="${message.image_url}" alt="Report Image" class="message-image">
              </div>
            ` : ''}
            
            <div class="message-location">
              <strong>Location:</strong> 
              Latitude: ${message.location.latitude}, 
              Longitude: ${message.location.longitude}
              <br>
              <a 
                href="https://www.google.com/maps?q=${message.location.latitude},${message.location.longitude}" 
                target="_blank" 
                rel="noopener noreferrer"
              >
                <br>
                <button class="btn">Google Map</button>
              </a>
            </div>
          </div>
        </div>
      `;
    });
    
    container.innerHTML = html;
  }
</script>
{% endblock %}
