r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Ip_messaging
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.ip_messaging.v1.service.channel import ChannelList
from twilio.rest.ip_messaging.v1.service.role import RoleList
from twilio.rest.ip_messaging.v1.service.user import UserList


class ServiceInstance(InstanceResource):
    """
    :ivar sid:
    :ivar account_sid:
    :ivar friendly_name:
    :ivar date_created:
    :ivar date_updated:
    :ivar default_service_role_sid:
    :ivar default_channel_role_sid:
    :ivar default_channel_creator_role_sid:
    :ivar read_status_enabled:
    :ivar reachability_enabled:
    :ivar typing_indicator_timeout:
    :ivar consumption_report_interval:
    :ivar limits:
    :ivar webhooks:
    :ivar pre_webhook_url:
    :ivar post_webhook_url:
    :ivar webhook_method:
    :ivar webhook_filters:
    :ivar notifications:
    :ivar url:
    :ivar links:
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.default_service_role_sid: Optional[str] = payload.get(
            "default_service_role_sid"
        )
        self.default_channel_role_sid: Optional[str] = payload.get(
            "default_channel_role_sid"
        )
        self.default_channel_creator_role_sid: Optional[str] = payload.get(
            "default_channel_creator_role_sid"
        )
        self.read_status_enabled: Optional[bool] = payload.get("read_status_enabled")
        self.reachability_enabled: Optional[bool] = payload.get("reachability_enabled")
        self.typing_indicator_timeout: Optional[int] = deserialize.integer(
            payload.get("typing_indicator_timeout")
        )
        self.consumption_report_interval: Optional[int] = deserialize.integer(
            payload.get("consumption_report_interval")
        )
        self.limits: Optional[Dict[str, object]] = payload.get("limits")
        self.webhooks: Optional[Dict[str, object]] = payload.get("webhooks")
        self.pre_webhook_url: Optional[str] = payload.get("pre_webhook_url")
        self.post_webhook_url: Optional[str] = payload.get("post_webhook_url")
        self.webhook_method: Optional[str] = payload.get("webhook_method")
        self.webhook_filters: Optional[List[str]] = payload.get("webhook_filters")
        self.notifications: Optional[Dict[str, object]] = payload.get("notifications")
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[ServiceContext] = None

    @property
    def _proxy(self) -> "ServiceContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ServiceContext for this ServiceInstance
        """
        if self._context is None:
            self._context = ServiceContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the ServiceInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ServiceInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "ServiceInstance":
        """
        Fetch the ServiceInstance


        :returns: The fetched ServiceInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ServiceInstance":
        """
        Asynchronous coroutine to fetch the ServiceInstance


        :returns: The fetched ServiceInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        default_service_role_sid: Union[str, object] = values.unset,
        default_channel_role_sid: Union[str, object] = values.unset,
        default_channel_creator_role_sid: Union[str, object] = values.unset,
        read_status_enabled: Union[bool, object] = values.unset,
        reachability_enabled: Union[bool, object] = values.unset,
        typing_indicator_timeout: Union[int, object] = values.unset,
        consumption_report_interval: Union[int, object] = values.unset,
        notifications_new_message_enabled: Union[bool, object] = values.unset,
        notifications_new_message_template: Union[str, object] = values.unset,
        notifications_added_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_added_to_channel_template: Union[str, object] = values.unset,
        notifications_removed_from_channel_enabled: Union[bool, object] = values.unset,
        notifications_removed_from_channel_template: Union[str, object] = values.unset,
        notifications_invited_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_invited_to_channel_template: Union[str, object] = values.unset,
        pre_webhook_url: Union[str, object] = values.unset,
        post_webhook_url: Union[str, object] = values.unset,
        webhook_method: Union[str, object] = values.unset,
        webhook_filters: Union[List[str], object] = values.unset,
        webhooks_on_message_send_url: Union[str, object] = values.unset,
        webhooks_on_message_send_method: Union[str, object] = values.unset,
        webhooks_on_message_update_url: Union[str, object] = values.unset,
        webhooks_on_message_update_method: Union[str, object] = values.unset,
        webhooks_on_message_remove_url: Union[str, object] = values.unset,
        webhooks_on_message_remove_method: Union[str, object] = values.unset,
        webhooks_on_channel_add_url: Union[str, object] = values.unset,
        webhooks_on_channel_add_method: Union[str, object] = values.unset,
        webhooks_on_channel_destroy_url: Union[str, object] = values.unset,
        webhooks_on_channel_destroy_method: Union[str, object] = values.unset,
        webhooks_on_channel_update_url: Union[str, object] = values.unset,
        webhooks_on_channel_update_method: Union[str, object] = values.unset,
        webhooks_on_member_add_url: Union[str, object] = values.unset,
        webhooks_on_member_add_method: Union[str, object] = values.unset,
        webhooks_on_member_remove_url: Union[str, object] = values.unset,
        webhooks_on_member_remove_method: Union[str, object] = values.unset,
        webhooks_on_message_sent_url: Union[str, object] = values.unset,
        webhooks_on_message_sent_method: Union[str, object] = values.unset,
        webhooks_on_message_updated_url: Union[str, object] = values.unset,
        webhooks_on_message_updated_method: Union[str, object] = values.unset,
        webhooks_on_message_removed_url: Union[str, object] = values.unset,
        webhooks_on_message_removed_method: Union[str, object] = values.unset,
        webhooks_on_channel_added_url: Union[str, object] = values.unset,
        webhooks_on_channel_added_method: Union[str, object] = values.unset,
        webhooks_on_channel_destroyed_url: Union[str, object] = values.unset,
        webhooks_on_channel_destroyed_method: Union[str, object] = values.unset,
        webhooks_on_channel_updated_url: Union[str, object] = values.unset,
        webhooks_on_channel_updated_method: Union[str, object] = values.unset,
        webhooks_on_member_added_url: Union[str, object] = values.unset,
        webhooks_on_member_added_method: Union[str, object] = values.unset,
        webhooks_on_member_removed_url: Union[str, object] = values.unset,
        webhooks_on_member_removed_method: Union[str, object] = values.unset,
        limits_channel_members: Union[int, object] = values.unset,
        limits_user_channels: Union[int, object] = values.unset,
    ) -> "ServiceInstance":
        """
        Update the ServiceInstance

        :param friendly_name:
        :param default_service_role_sid:
        :param default_channel_role_sid:
        :param default_channel_creator_role_sid:
        :param read_status_enabled:
        :param reachability_enabled:
        :param typing_indicator_timeout:
        :param consumption_report_interval:
        :param notifications_new_message_enabled:
        :param notifications_new_message_template:
        :param notifications_added_to_channel_enabled:
        :param notifications_added_to_channel_template:
        :param notifications_removed_from_channel_enabled:
        :param notifications_removed_from_channel_template:
        :param notifications_invited_to_channel_enabled:
        :param notifications_invited_to_channel_template:
        :param pre_webhook_url:
        :param post_webhook_url:
        :param webhook_method:
        :param webhook_filters:
        :param webhooks_on_message_send_url:
        :param webhooks_on_message_send_method:
        :param webhooks_on_message_update_url:
        :param webhooks_on_message_update_method:
        :param webhooks_on_message_remove_url:
        :param webhooks_on_message_remove_method:
        :param webhooks_on_channel_add_url:
        :param webhooks_on_channel_add_method:
        :param webhooks_on_channel_destroy_url:
        :param webhooks_on_channel_destroy_method:
        :param webhooks_on_channel_update_url:
        :param webhooks_on_channel_update_method:
        :param webhooks_on_member_add_url:
        :param webhooks_on_member_add_method:
        :param webhooks_on_member_remove_url:
        :param webhooks_on_member_remove_method:
        :param webhooks_on_message_sent_url:
        :param webhooks_on_message_sent_method:
        :param webhooks_on_message_updated_url:
        :param webhooks_on_message_updated_method:
        :param webhooks_on_message_removed_url:
        :param webhooks_on_message_removed_method:
        :param webhooks_on_channel_added_url:
        :param webhooks_on_channel_added_method:
        :param webhooks_on_channel_destroyed_url:
        :param webhooks_on_channel_destroyed_method:
        :param webhooks_on_channel_updated_url:
        :param webhooks_on_channel_updated_method:
        :param webhooks_on_member_added_url:
        :param webhooks_on_member_added_method:
        :param webhooks_on_member_removed_url:
        :param webhooks_on_member_removed_method:
        :param limits_channel_members:
        :param limits_user_channels:

        :returns: The updated ServiceInstance
        """
        return self._proxy.update(
            friendly_name=friendly_name,
            default_service_role_sid=default_service_role_sid,
            default_channel_role_sid=default_channel_role_sid,
            default_channel_creator_role_sid=default_channel_creator_role_sid,
            read_status_enabled=read_status_enabled,
            reachability_enabled=reachability_enabled,
            typing_indicator_timeout=typing_indicator_timeout,
            consumption_report_interval=consumption_report_interval,
            notifications_new_message_enabled=notifications_new_message_enabled,
            notifications_new_message_template=notifications_new_message_template,
            notifications_added_to_channel_enabled=notifications_added_to_channel_enabled,
            notifications_added_to_channel_template=notifications_added_to_channel_template,
            notifications_removed_from_channel_enabled=notifications_removed_from_channel_enabled,
            notifications_removed_from_channel_template=notifications_removed_from_channel_template,
            notifications_invited_to_channel_enabled=notifications_invited_to_channel_enabled,
            notifications_invited_to_channel_template=notifications_invited_to_channel_template,
            pre_webhook_url=pre_webhook_url,
            post_webhook_url=post_webhook_url,
            webhook_method=webhook_method,
            webhook_filters=webhook_filters,
            webhooks_on_message_send_url=webhooks_on_message_send_url,
            webhooks_on_message_send_method=webhooks_on_message_send_method,
            webhooks_on_message_update_url=webhooks_on_message_update_url,
            webhooks_on_message_update_method=webhooks_on_message_update_method,
            webhooks_on_message_remove_url=webhooks_on_message_remove_url,
            webhooks_on_message_remove_method=webhooks_on_message_remove_method,
            webhooks_on_channel_add_url=webhooks_on_channel_add_url,
            webhooks_on_channel_add_method=webhooks_on_channel_add_method,
            webhooks_on_channel_destroy_url=webhooks_on_channel_destroy_url,
            webhooks_on_channel_destroy_method=webhooks_on_channel_destroy_method,
            webhooks_on_channel_update_url=webhooks_on_channel_update_url,
            webhooks_on_channel_update_method=webhooks_on_channel_update_method,
            webhooks_on_member_add_url=webhooks_on_member_add_url,
            webhooks_on_member_add_method=webhooks_on_member_add_method,
            webhooks_on_member_remove_url=webhooks_on_member_remove_url,
            webhooks_on_member_remove_method=webhooks_on_member_remove_method,
            webhooks_on_message_sent_url=webhooks_on_message_sent_url,
            webhooks_on_message_sent_method=webhooks_on_message_sent_method,
            webhooks_on_message_updated_url=webhooks_on_message_updated_url,
            webhooks_on_message_updated_method=webhooks_on_message_updated_method,
            webhooks_on_message_removed_url=webhooks_on_message_removed_url,
            webhooks_on_message_removed_method=webhooks_on_message_removed_method,
            webhooks_on_channel_added_url=webhooks_on_channel_added_url,
            webhooks_on_channel_added_method=webhooks_on_channel_added_method,
            webhooks_on_channel_destroyed_url=webhooks_on_channel_destroyed_url,
            webhooks_on_channel_destroyed_method=webhooks_on_channel_destroyed_method,
            webhooks_on_channel_updated_url=webhooks_on_channel_updated_url,
            webhooks_on_channel_updated_method=webhooks_on_channel_updated_method,
            webhooks_on_member_added_url=webhooks_on_member_added_url,
            webhooks_on_member_added_method=webhooks_on_member_added_method,
            webhooks_on_member_removed_url=webhooks_on_member_removed_url,
            webhooks_on_member_removed_method=webhooks_on_member_removed_method,
            limits_channel_members=limits_channel_members,
            limits_user_channels=limits_user_channels,
        )

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        default_service_role_sid: Union[str, object] = values.unset,
        default_channel_role_sid: Union[str, object] = values.unset,
        default_channel_creator_role_sid: Union[str, object] = values.unset,
        read_status_enabled: Union[bool, object] = values.unset,
        reachability_enabled: Union[bool, object] = values.unset,
        typing_indicator_timeout: Union[int, object] = values.unset,
        consumption_report_interval: Union[int, object] = values.unset,
        notifications_new_message_enabled: Union[bool, object] = values.unset,
        notifications_new_message_template: Union[str, object] = values.unset,
        notifications_added_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_added_to_channel_template: Union[str, object] = values.unset,
        notifications_removed_from_channel_enabled: Union[bool, object] = values.unset,
        notifications_removed_from_channel_template: Union[str, object] = values.unset,
        notifications_invited_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_invited_to_channel_template: Union[str, object] = values.unset,
        pre_webhook_url: Union[str, object] = values.unset,
        post_webhook_url: Union[str, object] = values.unset,
        webhook_method: Union[str, object] = values.unset,
        webhook_filters: Union[List[str], object] = values.unset,
        webhooks_on_message_send_url: Union[str, object] = values.unset,
        webhooks_on_message_send_method: Union[str, object] = values.unset,
        webhooks_on_message_update_url: Union[str, object] = values.unset,
        webhooks_on_message_update_method: Union[str, object] = values.unset,
        webhooks_on_message_remove_url: Union[str, object] = values.unset,
        webhooks_on_message_remove_method: Union[str, object] = values.unset,
        webhooks_on_channel_add_url: Union[str, object] = values.unset,
        webhooks_on_channel_add_method: Union[str, object] = values.unset,
        webhooks_on_channel_destroy_url: Union[str, object] = values.unset,
        webhooks_on_channel_destroy_method: Union[str, object] = values.unset,
        webhooks_on_channel_update_url: Union[str, object] = values.unset,
        webhooks_on_channel_update_method: Union[str, object] = values.unset,
        webhooks_on_member_add_url: Union[str, object] = values.unset,
        webhooks_on_member_add_method: Union[str, object] = values.unset,
        webhooks_on_member_remove_url: Union[str, object] = values.unset,
        webhooks_on_member_remove_method: Union[str, object] = values.unset,
        webhooks_on_message_sent_url: Union[str, object] = values.unset,
        webhooks_on_message_sent_method: Union[str, object] = values.unset,
        webhooks_on_message_updated_url: Union[str, object] = values.unset,
        webhooks_on_message_updated_method: Union[str, object] = values.unset,
        webhooks_on_message_removed_url: Union[str, object] = values.unset,
        webhooks_on_message_removed_method: Union[str, object] = values.unset,
        webhooks_on_channel_added_url: Union[str, object] = values.unset,
        webhooks_on_channel_added_method: Union[str, object] = values.unset,
        webhooks_on_channel_destroyed_url: Union[str, object] = values.unset,
        webhooks_on_channel_destroyed_method: Union[str, object] = values.unset,
        webhooks_on_channel_updated_url: Union[str, object] = values.unset,
        webhooks_on_channel_updated_method: Union[str, object] = values.unset,
        webhooks_on_member_added_url: Union[str, object] = values.unset,
        webhooks_on_member_added_method: Union[str, object] = values.unset,
        webhooks_on_member_removed_url: Union[str, object] = values.unset,
        webhooks_on_member_removed_method: Union[str, object] = values.unset,
        limits_channel_members: Union[int, object] = values.unset,
        limits_user_channels: Union[int, object] = values.unset,
    ) -> "ServiceInstance":
        """
        Asynchronous coroutine to update the ServiceInstance

        :param friendly_name:
        :param default_service_role_sid:
        :param default_channel_role_sid:
        :param default_channel_creator_role_sid:
        :param read_status_enabled:
        :param reachability_enabled:
        :param typing_indicator_timeout:
        :param consumption_report_interval:
        :param notifications_new_message_enabled:
        :param notifications_new_message_template:
        :param notifications_added_to_channel_enabled:
        :param notifications_added_to_channel_template:
        :param notifications_removed_from_channel_enabled:
        :param notifications_removed_from_channel_template:
        :param notifications_invited_to_channel_enabled:
        :param notifications_invited_to_channel_template:
        :param pre_webhook_url:
        :param post_webhook_url:
        :param webhook_method:
        :param webhook_filters:
        :param webhooks_on_message_send_url:
        :param webhooks_on_message_send_method:
        :param webhooks_on_message_update_url:
        :param webhooks_on_message_update_method:
        :param webhooks_on_message_remove_url:
        :param webhooks_on_message_remove_method:
        :param webhooks_on_channel_add_url:
        :param webhooks_on_channel_add_method:
        :param webhooks_on_channel_destroy_url:
        :param webhooks_on_channel_destroy_method:
        :param webhooks_on_channel_update_url:
        :param webhooks_on_channel_update_method:
        :param webhooks_on_member_add_url:
        :param webhooks_on_member_add_method:
        :param webhooks_on_member_remove_url:
        :param webhooks_on_member_remove_method:
        :param webhooks_on_message_sent_url:
        :param webhooks_on_message_sent_method:
        :param webhooks_on_message_updated_url:
        :param webhooks_on_message_updated_method:
        :param webhooks_on_message_removed_url:
        :param webhooks_on_message_removed_method:
        :param webhooks_on_channel_added_url:
        :param webhooks_on_channel_added_method:
        :param webhooks_on_channel_destroyed_url:
        :param webhooks_on_channel_destroyed_method:
        :param webhooks_on_channel_updated_url:
        :param webhooks_on_channel_updated_method:
        :param webhooks_on_member_added_url:
        :param webhooks_on_member_added_method:
        :param webhooks_on_member_removed_url:
        :param webhooks_on_member_removed_method:
        :param limits_channel_members:
        :param limits_user_channels:

        :returns: The updated ServiceInstance
        """
        return await self._proxy.update_async(
            friendly_name=friendly_name,
            default_service_role_sid=default_service_role_sid,
            default_channel_role_sid=default_channel_role_sid,
            default_channel_creator_role_sid=default_channel_creator_role_sid,
            read_status_enabled=read_status_enabled,
            reachability_enabled=reachability_enabled,
            typing_indicator_timeout=typing_indicator_timeout,
            consumption_report_interval=consumption_report_interval,
            notifications_new_message_enabled=notifications_new_message_enabled,
            notifications_new_message_template=notifications_new_message_template,
            notifications_added_to_channel_enabled=notifications_added_to_channel_enabled,
            notifications_added_to_channel_template=notifications_added_to_channel_template,
            notifications_removed_from_channel_enabled=notifications_removed_from_channel_enabled,
            notifications_removed_from_channel_template=notifications_removed_from_channel_template,
            notifications_invited_to_channel_enabled=notifications_invited_to_channel_enabled,
            notifications_invited_to_channel_template=notifications_invited_to_channel_template,
            pre_webhook_url=pre_webhook_url,
            post_webhook_url=post_webhook_url,
            webhook_method=webhook_method,
            webhook_filters=webhook_filters,
            webhooks_on_message_send_url=webhooks_on_message_send_url,
            webhooks_on_message_send_method=webhooks_on_message_send_method,
            webhooks_on_message_update_url=webhooks_on_message_update_url,
            webhooks_on_message_update_method=webhooks_on_message_update_method,
            webhooks_on_message_remove_url=webhooks_on_message_remove_url,
            webhooks_on_message_remove_method=webhooks_on_message_remove_method,
            webhooks_on_channel_add_url=webhooks_on_channel_add_url,
            webhooks_on_channel_add_method=webhooks_on_channel_add_method,
            webhooks_on_channel_destroy_url=webhooks_on_channel_destroy_url,
            webhooks_on_channel_destroy_method=webhooks_on_channel_destroy_method,
            webhooks_on_channel_update_url=webhooks_on_channel_update_url,
            webhooks_on_channel_update_method=webhooks_on_channel_update_method,
            webhooks_on_member_add_url=webhooks_on_member_add_url,
            webhooks_on_member_add_method=webhooks_on_member_add_method,
            webhooks_on_member_remove_url=webhooks_on_member_remove_url,
            webhooks_on_member_remove_method=webhooks_on_member_remove_method,
            webhooks_on_message_sent_url=webhooks_on_message_sent_url,
            webhooks_on_message_sent_method=webhooks_on_message_sent_method,
            webhooks_on_message_updated_url=webhooks_on_message_updated_url,
            webhooks_on_message_updated_method=webhooks_on_message_updated_method,
            webhooks_on_message_removed_url=webhooks_on_message_removed_url,
            webhooks_on_message_removed_method=webhooks_on_message_removed_method,
            webhooks_on_channel_added_url=webhooks_on_channel_added_url,
            webhooks_on_channel_added_method=webhooks_on_channel_added_method,
            webhooks_on_channel_destroyed_url=webhooks_on_channel_destroyed_url,
            webhooks_on_channel_destroyed_method=webhooks_on_channel_destroyed_method,
            webhooks_on_channel_updated_url=webhooks_on_channel_updated_url,
            webhooks_on_channel_updated_method=webhooks_on_channel_updated_method,
            webhooks_on_member_added_url=webhooks_on_member_added_url,
            webhooks_on_member_added_method=webhooks_on_member_added_method,
            webhooks_on_member_removed_url=webhooks_on_member_removed_url,
            webhooks_on_member_removed_method=webhooks_on_member_removed_method,
            limits_channel_members=limits_channel_members,
            limits_user_channels=limits_user_channels,
        )

    @property
    def channels(self) -> ChannelList:
        """
        Access the channels
        """
        return self._proxy.channels

    @property
    def roles(self) -> RoleList:
        """
        Access the roles
        """
        return self._proxy.roles

    @property
    def users(self) -> UserList:
        """
        Access the users
        """
        return self._proxy.users

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.IpMessaging.V1.ServiceInstance {}>".format(context)


class ServiceContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the ServiceContext

        :param version: Version that contains the resource
        :param sid:
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/Services/{sid}".format(**self._solution)

        self._channels: Optional[ChannelList] = None
        self._roles: Optional[RoleList] = None
        self._users: Optional[UserList] = None

    def delete(self) -> bool:
        """
        Deletes the ServiceInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ServiceInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> ServiceInstance:
        """
        Fetch the ServiceInstance


        :returns: The fetched ServiceInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ServiceInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> ServiceInstance:
        """
        Asynchronous coroutine to fetch the ServiceInstance


        :returns: The fetched ServiceInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ServiceInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        default_service_role_sid: Union[str, object] = values.unset,
        default_channel_role_sid: Union[str, object] = values.unset,
        default_channel_creator_role_sid: Union[str, object] = values.unset,
        read_status_enabled: Union[bool, object] = values.unset,
        reachability_enabled: Union[bool, object] = values.unset,
        typing_indicator_timeout: Union[int, object] = values.unset,
        consumption_report_interval: Union[int, object] = values.unset,
        notifications_new_message_enabled: Union[bool, object] = values.unset,
        notifications_new_message_template: Union[str, object] = values.unset,
        notifications_added_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_added_to_channel_template: Union[str, object] = values.unset,
        notifications_removed_from_channel_enabled: Union[bool, object] = values.unset,
        notifications_removed_from_channel_template: Union[str, object] = values.unset,
        notifications_invited_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_invited_to_channel_template: Union[str, object] = values.unset,
        pre_webhook_url: Union[str, object] = values.unset,
        post_webhook_url: Union[str, object] = values.unset,
        webhook_method: Union[str, object] = values.unset,
        webhook_filters: Union[List[str], object] = values.unset,
        webhooks_on_message_send_url: Union[str, object] = values.unset,
        webhooks_on_message_send_method: Union[str, object] = values.unset,
        webhooks_on_message_update_url: Union[str, object] = values.unset,
        webhooks_on_message_update_method: Union[str, object] = values.unset,
        webhooks_on_message_remove_url: Union[str, object] = values.unset,
        webhooks_on_message_remove_method: Union[str, object] = values.unset,
        webhooks_on_channel_add_url: Union[str, object] = values.unset,
        webhooks_on_channel_add_method: Union[str, object] = values.unset,
        webhooks_on_channel_destroy_url: Union[str, object] = values.unset,
        webhooks_on_channel_destroy_method: Union[str, object] = values.unset,
        webhooks_on_channel_update_url: Union[str, object] = values.unset,
        webhooks_on_channel_update_method: Union[str, object] = values.unset,
        webhooks_on_member_add_url: Union[str, object] = values.unset,
        webhooks_on_member_add_method: Union[str, object] = values.unset,
        webhooks_on_member_remove_url: Union[str, object] = values.unset,
        webhooks_on_member_remove_method: Union[str, object] = values.unset,
        webhooks_on_message_sent_url: Union[str, object] = values.unset,
        webhooks_on_message_sent_method: Union[str, object] = values.unset,
        webhooks_on_message_updated_url: Union[str, object] = values.unset,
        webhooks_on_message_updated_method: Union[str, object] = values.unset,
        webhooks_on_message_removed_url: Union[str, object] = values.unset,
        webhooks_on_message_removed_method: Union[str, object] = values.unset,
        webhooks_on_channel_added_url: Union[str, object] = values.unset,
        webhooks_on_channel_added_method: Union[str, object] = values.unset,
        webhooks_on_channel_destroyed_url: Union[str, object] = values.unset,
        webhooks_on_channel_destroyed_method: Union[str, object] = values.unset,
        webhooks_on_channel_updated_url: Union[str, object] = values.unset,
        webhooks_on_channel_updated_method: Union[str, object] = values.unset,
        webhooks_on_member_added_url: Union[str, object] = values.unset,
        webhooks_on_member_added_method: Union[str, object] = values.unset,
        webhooks_on_member_removed_url: Union[str, object] = values.unset,
        webhooks_on_member_removed_method: Union[str, object] = values.unset,
        limits_channel_members: Union[int, object] = values.unset,
        limits_user_channels: Union[int, object] = values.unset,
    ) -> ServiceInstance:
        """
        Update the ServiceInstance

        :param friendly_name:
        :param default_service_role_sid:
        :param default_channel_role_sid:
        :param default_channel_creator_role_sid:
        :param read_status_enabled:
        :param reachability_enabled:
        :param typing_indicator_timeout:
        :param consumption_report_interval:
        :param notifications_new_message_enabled:
        :param notifications_new_message_template:
        :param notifications_added_to_channel_enabled:
        :param notifications_added_to_channel_template:
        :param notifications_removed_from_channel_enabled:
        :param notifications_removed_from_channel_template:
        :param notifications_invited_to_channel_enabled:
        :param notifications_invited_to_channel_template:
        :param pre_webhook_url:
        :param post_webhook_url:
        :param webhook_method:
        :param webhook_filters:
        :param webhooks_on_message_send_url:
        :param webhooks_on_message_send_method:
        :param webhooks_on_message_update_url:
        :param webhooks_on_message_update_method:
        :param webhooks_on_message_remove_url:
        :param webhooks_on_message_remove_method:
        :param webhooks_on_channel_add_url:
        :param webhooks_on_channel_add_method:
        :param webhooks_on_channel_destroy_url:
        :param webhooks_on_channel_destroy_method:
        :param webhooks_on_channel_update_url:
        :param webhooks_on_channel_update_method:
        :param webhooks_on_member_add_url:
        :param webhooks_on_member_add_method:
        :param webhooks_on_member_remove_url:
        :param webhooks_on_member_remove_method:
        :param webhooks_on_message_sent_url:
        :param webhooks_on_message_sent_method:
        :param webhooks_on_message_updated_url:
        :param webhooks_on_message_updated_method:
        :param webhooks_on_message_removed_url:
        :param webhooks_on_message_removed_method:
        :param webhooks_on_channel_added_url:
        :param webhooks_on_channel_added_method:
        :param webhooks_on_channel_destroyed_url:
        :param webhooks_on_channel_destroyed_method:
        :param webhooks_on_channel_updated_url:
        :param webhooks_on_channel_updated_method:
        :param webhooks_on_member_added_url:
        :param webhooks_on_member_added_method:
        :param webhooks_on_member_removed_url:
        :param webhooks_on_member_removed_method:
        :param limits_channel_members:
        :param limits_user_channels:

        :returns: The updated ServiceInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "DefaultServiceRoleSid": default_service_role_sid,
                "DefaultChannelRoleSid": default_channel_role_sid,
                "DefaultChannelCreatorRoleSid": default_channel_creator_role_sid,
                "ReadStatusEnabled": serialize.boolean_to_string(read_status_enabled),
                "ReachabilityEnabled": serialize.boolean_to_string(
                    reachability_enabled
                ),
                "TypingIndicatorTimeout": typing_indicator_timeout,
                "ConsumptionReportInterval": consumption_report_interval,
                "Notifications.NewMessage.Enabled": serialize.boolean_to_string(
                    notifications_new_message_enabled
                ),
                "Notifications.NewMessage.Template": notifications_new_message_template,
                "Notifications.AddedToChannel.Enabled": serialize.boolean_to_string(
                    notifications_added_to_channel_enabled
                ),
                "Notifications.AddedToChannel.Template": notifications_added_to_channel_template,
                "Notifications.RemovedFromChannel.Enabled": serialize.boolean_to_string(
                    notifications_removed_from_channel_enabled
                ),
                "Notifications.RemovedFromChannel.Template": notifications_removed_from_channel_template,
                "Notifications.InvitedToChannel.Enabled": serialize.boolean_to_string(
                    notifications_invited_to_channel_enabled
                ),
                "Notifications.InvitedToChannel.Template": notifications_invited_to_channel_template,
                "PreWebhookUrl": pre_webhook_url,
                "PostWebhookUrl": post_webhook_url,
                "WebhookMethod": webhook_method,
                "WebhookFilters": serialize.map(webhook_filters, lambda e: e),
                "Webhooks.OnMessageSend.Url": webhooks_on_message_send_url,
                "Webhooks.OnMessageSend.Method": webhooks_on_message_send_method,
                "Webhooks.OnMessageUpdate.Url": webhooks_on_message_update_url,
                "Webhooks.OnMessageUpdate.Method": webhooks_on_message_update_method,
                "Webhooks.OnMessageRemove.Url": webhooks_on_message_remove_url,
                "Webhooks.OnMessageRemove.Method": webhooks_on_message_remove_method,
                "Webhooks.OnChannelAdd.Url": webhooks_on_channel_add_url,
                "Webhooks.OnChannelAdd.Method": webhooks_on_channel_add_method,
                "Webhooks.OnChannelDestroy.Url": webhooks_on_channel_destroy_url,
                "Webhooks.OnChannelDestroy.Method": webhooks_on_channel_destroy_method,
                "Webhooks.OnChannelUpdate.Url": webhooks_on_channel_update_url,
                "Webhooks.OnChannelUpdate.Method": webhooks_on_channel_update_method,
                "Webhooks.OnMemberAdd.Url": webhooks_on_member_add_url,
                "Webhooks.OnMemberAdd.Method": webhooks_on_member_add_method,
                "Webhooks.OnMemberRemove.Url": webhooks_on_member_remove_url,
                "Webhooks.OnMemberRemove.Method": webhooks_on_member_remove_method,
                "Webhooks.OnMessageSent.Url": webhooks_on_message_sent_url,
                "Webhooks.OnMessageSent.Method": webhooks_on_message_sent_method,
                "Webhooks.OnMessageUpdated.Url": webhooks_on_message_updated_url,
                "Webhooks.OnMessageUpdated.Method": webhooks_on_message_updated_method,
                "Webhooks.OnMessageRemoved.Url": webhooks_on_message_removed_url,
                "Webhooks.OnMessageRemoved.Method": webhooks_on_message_removed_method,
                "Webhooks.OnChannelAdded.Url": webhooks_on_channel_added_url,
                "Webhooks.OnChannelAdded.Method": webhooks_on_channel_added_method,
                "Webhooks.OnChannelDestroyed.Url": webhooks_on_channel_destroyed_url,
                "Webhooks.OnChannelDestroyed.Method": webhooks_on_channel_destroyed_method,
                "Webhooks.OnChannelUpdated.Url": webhooks_on_channel_updated_url,
                "Webhooks.OnChannelUpdated.Method": webhooks_on_channel_updated_method,
                "Webhooks.OnMemberAdded.Url": webhooks_on_member_added_url,
                "Webhooks.OnMemberAdded.Method": webhooks_on_member_added_method,
                "Webhooks.OnMemberRemoved.Url": webhooks_on_member_removed_url,
                "Webhooks.OnMemberRemoved.Method": webhooks_on_member_removed_method,
                "Limits.ChannelMembers": limits_channel_members,
                "Limits.UserChannels": limits_user_channels,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ServiceInstance(self._version, payload, sid=self._solution["sid"])

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        default_service_role_sid: Union[str, object] = values.unset,
        default_channel_role_sid: Union[str, object] = values.unset,
        default_channel_creator_role_sid: Union[str, object] = values.unset,
        read_status_enabled: Union[bool, object] = values.unset,
        reachability_enabled: Union[bool, object] = values.unset,
        typing_indicator_timeout: Union[int, object] = values.unset,
        consumption_report_interval: Union[int, object] = values.unset,
        notifications_new_message_enabled: Union[bool, object] = values.unset,
        notifications_new_message_template: Union[str, object] = values.unset,
        notifications_added_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_added_to_channel_template: Union[str, object] = values.unset,
        notifications_removed_from_channel_enabled: Union[bool, object] = values.unset,
        notifications_removed_from_channel_template: Union[str, object] = values.unset,
        notifications_invited_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_invited_to_channel_template: Union[str, object] = values.unset,
        pre_webhook_url: Union[str, object] = values.unset,
        post_webhook_url: Union[str, object] = values.unset,
        webhook_method: Union[str, object] = values.unset,
        webhook_filters: Union[List[str], object] = values.unset,
        webhooks_on_message_send_url: Union[str, object] = values.unset,
        webhooks_on_message_send_method: Union[str, object] = values.unset,
        webhooks_on_message_update_url: Union[str, object] = values.unset,
        webhooks_on_message_update_method: Union[str, object] = values.unset,
        webhooks_on_message_remove_url: Union[str, object] = values.unset,
        webhooks_on_message_remove_method: Union[str, object] = values.unset,
        webhooks_on_channel_add_url: Union[str, object] = values.unset,
        webhooks_on_channel_add_method: Union[str, object] = values.unset,
        webhooks_on_channel_destroy_url: Union[str, object] = values.unset,
        webhooks_on_channel_destroy_method: Union[str, object] = values.unset,
        webhooks_on_channel_update_url: Union[str, object] = values.unset,
        webhooks_on_channel_update_method: Union[str, object] = values.unset,
        webhooks_on_member_add_url: Union[str, object] = values.unset,
        webhooks_on_member_add_method: Union[str, object] = values.unset,
        webhooks_on_member_remove_url: Union[str, object] = values.unset,
        webhooks_on_member_remove_method: Union[str, object] = values.unset,
        webhooks_on_message_sent_url: Union[str, object] = values.unset,
        webhooks_on_message_sent_method: Union[str, object] = values.unset,
        webhooks_on_message_updated_url: Union[str, object] = values.unset,
        webhooks_on_message_updated_method: Union[str, object] = values.unset,
        webhooks_on_message_removed_url: Union[str, object] = values.unset,
        webhooks_on_message_removed_method: Union[str, object] = values.unset,
        webhooks_on_channel_added_url: Union[str, object] = values.unset,
        webhooks_on_channel_added_method: Union[str, object] = values.unset,
        webhooks_on_channel_destroyed_url: Union[str, object] = values.unset,
        webhooks_on_channel_destroyed_method: Union[str, object] = values.unset,
        webhooks_on_channel_updated_url: Union[str, object] = values.unset,
        webhooks_on_channel_updated_method: Union[str, object] = values.unset,
        webhooks_on_member_added_url: Union[str, object] = values.unset,
        webhooks_on_member_added_method: Union[str, object] = values.unset,
        webhooks_on_member_removed_url: Union[str, object] = values.unset,
        webhooks_on_member_removed_method: Union[str, object] = values.unset,
        limits_channel_members: Union[int, object] = values.unset,
        limits_user_channels: Union[int, object] = values.unset,
    ) -> ServiceInstance:
        """
        Asynchronous coroutine to update the ServiceInstance

        :param friendly_name:
        :param default_service_role_sid:
        :param default_channel_role_sid:
        :param default_channel_creator_role_sid:
        :param read_status_enabled:
        :param reachability_enabled:
        :param typing_indicator_timeout:
        :param consumption_report_interval:
        :param notifications_new_message_enabled:
        :param notifications_new_message_template:
        :param notifications_added_to_channel_enabled:
        :param notifications_added_to_channel_template:
        :param notifications_removed_from_channel_enabled:
        :param notifications_removed_from_channel_template:
        :param notifications_invited_to_channel_enabled:
        :param notifications_invited_to_channel_template:
        :param pre_webhook_url:
        :param post_webhook_url:
        :param webhook_method:
        :param webhook_filters:
        :param webhooks_on_message_send_url:
        :param webhooks_on_message_send_method:
        :param webhooks_on_message_update_url:
        :param webhooks_on_message_update_method:
        :param webhooks_on_message_remove_url:
        :param webhooks_on_message_remove_method:
        :param webhooks_on_channel_add_url:
        :param webhooks_on_channel_add_method:
        :param webhooks_on_channel_destroy_url:
        :param webhooks_on_channel_destroy_method:
        :param webhooks_on_channel_update_url:
        :param webhooks_on_channel_update_method:
        :param webhooks_on_member_add_url:
        :param webhooks_on_member_add_method:
        :param webhooks_on_member_remove_url:
        :param webhooks_on_member_remove_method:
        :param webhooks_on_message_sent_url:
        :param webhooks_on_message_sent_method:
        :param webhooks_on_message_updated_url:
        :param webhooks_on_message_updated_method:
        :param webhooks_on_message_removed_url:
        :param webhooks_on_message_removed_method:
        :param webhooks_on_channel_added_url:
        :param webhooks_on_channel_added_method:
        :param webhooks_on_channel_destroyed_url:
        :param webhooks_on_channel_destroyed_method:
        :param webhooks_on_channel_updated_url:
        :param webhooks_on_channel_updated_method:
        :param webhooks_on_member_added_url:
        :param webhooks_on_member_added_method:
        :param webhooks_on_member_removed_url:
        :param webhooks_on_member_removed_method:
        :param limits_channel_members:
        :param limits_user_channels:

        :returns: The updated ServiceInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "DefaultServiceRoleSid": default_service_role_sid,
                "DefaultChannelRoleSid": default_channel_role_sid,
                "DefaultChannelCreatorRoleSid": default_channel_creator_role_sid,
                "ReadStatusEnabled": serialize.boolean_to_string(read_status_enabled),
                "ReachabilityEnabled": serialize.boolean_to_string(
                    reachability_enabled
                ),
                "TypingIndicatorTimeout": typing_indicator_timeout,
                "ConsumptionReportInterval": consumption_report_interval,
                "Notifications.NewMessage.Enabled": serialize.boolean_to_string(
                    notifications_new_message_enabled
                ),
                "Notifications.NewMessage.Template": notifications_new_message_template,
                "Notifications.AddedToChannel.Enabled": serialize.boolean_to_string(
                    notifications_added_to_channel_enabled
                ),
                "Notifications.AddedToChannel.Template": notifications_added_to_channel_template,
                "Notifications.RemovedFromChannel.Enabled": serialize.boolean_to_string(
                    notifications_removed_from_channel_enabled
                ),
                "Notifications.RemovedFromChannel.Template": notifications_removed_from_channel_template,
                "Notifications.InvitedToChannel.Enabled": serialize.boolean_to_string(
                    notifications_invited_to_channel_enabled
                ),
                "Notifications.InvitedToChannel.Template": notifications_invited_to_channel_template,
                "PreWebhookUrl": pre_webhook_url,
                "PostWebhookUrl": post_webhook_url,
                "WebhookMethod": webhook_method,
                "WebhookFilters": serialize.map(webhook_filters, lambda e: e),
                "Webhooks.OnMessageSend.Url": webhooks_on_message_send_url,
                "Webhooks.OnMessageSend.Method": webhooks_on_message_send_method,
                "Webhooks.OnMessageUpdate.Url": webhooks_on_message_update_url,
                "Webhooks.OnMessageUpdate.Method": webhooks_on_message_update_method,
                "Webhooks.OnMessageRemove.Url": webhooks_on_message_remove_url,
                "Webhooks.OnMessageRemove.Method": webhooks_on_message_remove_method,
                "Webhooks.OnChannelAdd.Url": webhooks_on_channel_add_url,
                "Webhooks.OnChannelAdd.Method": webhooks_on_channel_add_method,
                "Webhooks.OnChannelDestroy.Url": webhooks_on_channel_destroy_url,
                "Webhooks.OnChannelDestroy.Method": webhooks_on_channel_destroy_method,
                "Webhooks.OnChannelUpdate.Url": webhooks_on_channel_update_url,
                "Webhooks.OnChannelUpdate.Method": webhooks_on_channel_update_method,
                "Webhooks.OnMemberAdd.Url": webhooks_on_member_add_url,
                "Webhooks.OnMemberAdd.Method": webhooks_on_member_add_method,
                "Webhooks.OnMemberRemove.Url": webhooks_on_member_remove_url,
                "Webhooks.OnMemberRemove.Method": webhooks_on_member_remove_method,
                "Webhooks.OnMessageSent.Url": webhooks_on_message_sent_url,
                "Webhooks.OnMessageSent.Method": webhooks_on_message_sent_method,
                "Webhooks.OnMessageUpdated.Url": webhooks_on_message_updated_url,
                "Webhooks.OnMessageUpdated.Method": webhooks_on_message_updated_method,
                "Webhooks.OnMessageRemoved.Url": webhooks_on_message_removed_url,
                "Webhooks.OnMessageRemoved.Method": webhooks_on_message_removed_method,
                "Webhooks.OnChannelAdded.Url": webhooks_on_channel_added_url,
                "Webhooks.OnChannelAdded.Method": webhooks_on_channel_added_method,
                "Webhooks.OnChannelDestroyed.Url": webhooks_on_channel_destroyed_url,
                "Webhooks.OnChannelDestroyed.Method": webhooks_on_channel_destroyed_method,
                "Webhooks.OnChannelUpdated.Url": webhooks_on_channel_updated_url,
                "Webhooks.OnChannelUpdated.Method": webhooks_on_channel_updated_method,
                "Webhooks.OnMemberAdded.Url": webhooks_on_member_added_url,
                "Webhooks.OnMemberAdded.Method": webhooks_on_member_added_method,
                "Webhooks.OnMemberRemoved.Url": webhooks_on_member_removed_url,
                "Webhooks.OnMemberRemoved.Method": webhooks_on_member_removed_method,
                "Limits.ChannelMembers": limits_channel_members,
                "Limits.UserChannels": limits_user_channels,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ServiceInstance(self._version, payload, sid=self._solution["sid"])

    @property
    def channels(self) -> ChannelList:
        """
        Access the channels
        """
        if self._channels is None:
            self._channels = ChannelList(
                self._version,
                self._solution["sid"],
            )
        return self._channels

    @property
    def roles(self) -> RoleList:
        """
        Access the roles
        """
        if self._roles is None:
            self._roles = RoleList(
                self._version,
                self._solution["sid"],
            )
        return self._roles

    @property
    def users(self) -> UserList:
        """
        Access the users
        """
        if self._users is None:
            self._users = UserList(
                self._version,
                self._solution["sid"],
            )
        return self._users

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.IpMessaging.V1.ServiceContext {}>".format(context)


class ServicePage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> ServiceInstance:
        """
        Build an instance of ServiceInstance

        :param payload: Payload response from the API
        """
        return ServiceInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.IpMessaging.V1.ServicePage>"


class ServiceList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the ServiceList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Services"

    def create(self, friendly_name: str) -> ServiceInstance:
        """
        Create the ServiceInstance

        :param friendly_name:

        :returns: The created ServiceInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ServiceInstance(self._version, payload)

    async def create_async(self, friendly_name: str) -> ServiceInstance:
        """
        Asynchronously create the ServiceInstance

        :param friendly_name:

        :returns: The created ServiceInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ServiceInstance(self._version, payload)

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ServiceInstance]:
        """
        Streams ServiceInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ServiceInstance]:
        """
        Asynchronously streams ServiceInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ServiceInstance]:
        """
        Lists ServiceInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ServiceInstance]:
        """
        Asynchronously lists ServiceInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ServicePage:
        """
        Retrieve a single page of ServiceInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ServiceInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ServicePage(self._version, response)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ServicePage:
        """
        Asynchronously retrieve a single page of ServiceInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ServiceInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ServicePage(self._version, response)

    def get_page(self, target_url: str) -> ServicePage:
        """
        Retrieve a specific page of ServiceInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ServiceInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ServicePage(self._version, response)

    async def get_page_async(self, target_url: str) -> ServicePage:
        """
        Asynchronously retrieve a specific page of ServiceInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ServiceInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ServicePage(self._version, response)

    def get(self, sid: str) -> ServiceContext:
        """
        Constructs a ServiceContext

        :param sid:
        """
        return ServiceContext(self._version, sid=sid)

    def __call__(self, sid: str) -> ServiceContext:
        """
        Constructs a ServiceContext

        :param sid:
        """
        return ServiceContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.IpMessaging.V1.ServiceList>"
