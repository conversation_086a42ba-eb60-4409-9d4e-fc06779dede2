from enum import Enum
from datetime import datetime, timezone, timedelta
import json
import uuid
import os
import logging
from typing import Dict, Any, Optional
import re

from app.models.report import ReportCategory, Report
from app.schemas.report import WhatsAppMessage

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define conversation states
class ConversationState(str, Enum):
    INITIAL = "initial"
    AWAITING_CATEGORY = "awaiting_category"
    AWAITING_DESCRIPTION = "awaiting_description"
    AWAITING_LOCATION = "awaiting_location"
    AWAITING_PHOTO = "awaiting_photo"
    COMPLETED = "completed"

# In-memory storage for user sessions (in production, use Redis or a database)
user_sessions = {}

# Session timeout (in minutes)
SESSION_TIMEOUT = 30

class ConversationManager:
    @staticmethod
    def get_or_create_session(phone_number: str) -> Dict[str, Any]:
        """
        Get or create a user session
        
        Args:
            phone_number: The user's phone number
            
        Returns:
            The user session
        """
        # Clean up expired sessions
        ConversationManager.cleanup_expired_sessions()
        
        # Get or create the session
        if phone_number not in user_sessions:
            user_sessions[phone_number] = {
                "state": ConversationState.INITIAL,
                "report_data": {},
                "last_activity": datetime.now(timezone.utc)
            }
        else:
            # Update last activity
            user_sessions[phone_number]["last_activity"] = datetime.now(timezone.utc)
            
        return user_sessions[phone_number]
    
    @staticmethod
    def update_session(phone_number: str, session_data: Dict[str, Any]) -> None:
        """
        Update a user session
        
        Args:
            phone_number: The user's phone number
            session_data: The updated session data
        """
        user_sessions[phone_number] = session_data
        user_sessions[phone_number]["last_activity"] = datetime.now(timezone.utc)
    
    @staticmethod
    def cleanup_expired_sessions() -> None:
        """
        Clean up expired sessions
        """
        now = datetime.now(timezone.utc)
        expired_keys = []
        
        for phone_number, session in user_sessions.items():
            last_activity = session.get("last_activity", datetime.min.replace(tzinfo=timezone.utc))
            if now - last_activity > timedelta(minutes=SESSION_TIMEOUT):
                expired_keys.append(phone_number)
        
        for key in expired_keys:
            del user_sessions[key]
            
        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired sessions")
    
    @staticmethod
    def process_message(message: WhatsAppMessage) -> str:
        """
        Process an incoming WhatsApp message and manage the conversation flow
        
        Args:
            message: The incoming WhatsApp message
            
        Returns:
            The response message
        """
        # Extract the phone number
        phone_number = message.From
        
        # Get or create the user session
        session = ConversationManager.get_or_create_session(phone_number)
        
        # Check for special commands
        if message.Body.lower() == "new":
            # Reset the session for a new report
            session = {
                "state": ConversationState.AWAITING_CATEGORY,
                "report_data": {},
                "last_activity": datetime.now(timezone.utc)
            }
            ConversationManager.update_session(phone_number, session)
            return ConversationManager.get_category_prompt()
        
        if message.Body.lower().startswith("#status"):
            # Handle status request (this is already implemented in whatsapp.py)
            return None
        
        # Process based on current state
        current_state = session["state"]
        
        if current_state == ConversationState.INITIAL:
            # Welcome message and prompt for category
            session["state"] = ConversationState.AWAITING_CATEGORY
            ConversationManager.update_session(phone_number, session)
            return ConversationManager.get_category_prompt()
        
        elif current_state == ConversationState.AWAITING_CATEGORY:
            # Process category selection
            return ConversationManager.process_category(message.Body, session, phone_number)
        
        elif current_state == ConversationState.AWAITING_DESCRIPTION:
            # Process description
            return ConversationManager.process_description(message.Body, session, phone_number)
        
        elif current_state == ConversationState.AWAITING_LOCATION:
            # Process location
            return ConversationManager.process_location(message, session, phone_number)
        
        elif current_state == ConversationState.AWAITING_PHOTO:
            # Process photo
            return ConversationManager.process_photo(message, session, phone_number)
        
        elif current_state == ConversationState.COMPLETED:
            # Handle completed state
            if message.Body.lower() == "new":
                session["state"] = ConversationState.AWAITING_CATEGORY
                session["report_data"] = {}
                ConversationManager.update_session(phone_number, session)
                return ConversationManager.get_category_prompt()
            else:
                return ("Your report has been submitted. To submit another report, send 'new'.\n"
                        "To check the status of your reports, send '#status REPORT_ID'.")
    
    @staticmethod
    def get_category_prompt() -> str:
        """Get the category prompt message"""
        return ("Welcome to Hello Cyril Incident Reporting!\n\n"
                "Please select the category of your incident:\n"
                "1. Crime\n"
                "2. EMS (Emergency Medical Services)\n"
                "3. Infrastructure")
    
    @staticmethod
    def process_category(message_body: str, session: Dict[str, Any], phone_number: str) -> str:
        """Process category selection"""
        category_map = {"1": "crime", "2": "ems", "3": "infrastructure"}
        
        # Also accept text input for categories
        text_category_map = {
            "crime": "crime", 
            "ems": "ems", 
            "emergency": "ems",
            "infrastructure": "infrastructure",
            "infra": "infrastructure"
        }
        
        # Try to match by number or text
        category = category_map.get(message_body.strip())
        if not category:
            category = text_category_map.get(message_body.lower().strip())
        
        if not category:
            return ("Please select a valid category number or name:\n"
                    "1. Crime\n"
                    "2. EMS (Emergency Medical Services)\n"
                    "3. Infrastructure")
        
        # Map to enum
        category_enum_map = {
            "crime": ReportCategory.CRIME,
            "ems": ReportCategory.EMS,
            "infrastructure": ReportCategory.INFRASTRUCTURE
        }
        
        # Update session
        session["report_data"]["category"] = category_enum_map[category]
        session["state"] = ConversationState.AWAITING_DESCRIPTION
        ConversationManager.update_session(phone_number, session)
        
        return "Please describe the incident in detail:"
    
    @staticmethod
    def process_description(message_body: str, session: Dict[str, Any], phone_number: str) -> str:
        """Process incident description"""
        if not message_body.strip():
            return "Please provide a description of the incident."
        
        # Update session
        session["report_data"]["description"] = message_body.strip()
        session["state"] = ConversationState.AWAITING_LOCATION
        ConversationManager.update_session(phone_number, session)
        
        return ("Thank you. Please share your location by:\n"
                "1. Tap the attachment icon (📎)\n"
                "2. Select 'Location'\n"
                "3. Choose 'Send your current location' or 'Send your live location'\n\n"
                "Alternatively, you can type coordinates in the format 'latitude,longitude'")
    
    @staticmethod
    def process_location(message: WhatsAppMessage, session: Dict[str, Any], phone_number: str) -> str:
        """Process location information"""
        # Check if location was shared via WhatsApp
        if message.Latitude and message.Longitude:
            session["report_data"]["latitude"] = message.Latitude
            session["report_data"]["longitude"] = message.Longitude
            session["state"] = ConversationState.AWAITING_PHOTO
            ConversationManager.update_session(phone_number, session)
            
            return ("Location received. If you have a photo of the incident, please send it now.\n"
                    "If not, reply with 'Skip'.")
        
        # Try to extract coordinates from text
        try:
            # Check for "latitude,longitude" format
            coords_match = re.search(r'(-?\d+\.\d+),\s*(-?\d+\.\d+)', message.Body)
            if coords_match:
                lat = float(coords_match.group(1))
                lng = float(coords_match.group(2))
                
                # Basic validation
                if -90 <= lat <= 90 and -180 <= lng <= 180:
                    session["report_data"]["latitude"] = lat
                    session["report_data"]["longitude"] = lng
                    session["state"] = ConversationState.AWAITING_PHOTO
                    ConversationManager.update_session(phone_number, session)
                    
                    return ("Location received. If you have a photo of the incident, please send it now.\n"
                            "If not, reply with 'Skip'.")
            
            # If we get here, the format wasn't recognized
            return ("Please send your location using WhatsApp's location sharing feature or in the format 'latitude,longitude'.\n"
                    "Example: -25.7461, 28.1881")
            
        except Exception as e:
            logger.error(f"Error processing location: {str(e)}")
            return ("Please send your location using WhatsApp's location sharing feature or in the format 'latitude,longitude'.\n"
                    "Example: -25.7461, 28.1881")
    
    @staticmethod
    def process_photo(message: WhatsAppMessage, session: Dict[str, Any], phone_number: str) -> str:
        """Process photo submission or Skip"""
        # Check if user wants to skip
        if message.Body.lower() == "skip":
            session["report_data"]["image_url"] = None
        # Check if media was sent
        elif message.NumMedia > 0 and message.MediaUrl0:
            session["report_data"]["image_url"] = message.MediaUrl0
        else:
            return ("Please send a photo of the incident or reply with 'Skip' if you don't have one.")
        
        # Generate a short ID for the report
        short_id = Report.generate_short_id()
        session["report_data"]["short_id"] = short_id
        
        # Hash the phone number for anonymity
        user_hash = Report.hash_phone_number(phone_number)
        session["report_data"]["user_hash"] = user_hash
        
        # Mark as completed
        session["state"] = ConversationState.COMPLETED
        ConversationManager.update_session(phone_number, session)
        
        # Return the report data for saving
        return session["report_data"]
