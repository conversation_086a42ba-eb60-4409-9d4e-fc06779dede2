# this file is automatically generated
from typing import List

from sqlalchemy.sql import functions
from sqlalchemy.sql.elements import ColumnElement
from sqlalchemy.sql.selectable import FromClause

import geoalchemy2.types

class GenericFunction(functions.GenericFunction): ...

class TableRowElement(ColumnElement):
    inherit_cache: bool = ...
    """The cache is disabled for this class."""

    def __init__(self, selectable: bool) -> None: ...
    @property
    def _from_objects(self) -> List[FromClause]: ...

class ST_AsGeoJSON(GenericFunction):
    """
    Return the geometry as a GeoJSON "geometry" object, or the row as a GeoJSON feature" object (PostGIS
    3 only). (Cf GeoJSON specifications RFC 7946). 2D and 3D Geometries are both supported. GeoJSON only
    support SFS 1.1 geometry types (no curve support for example). See
    https://postgis.net/docs/ST_AsGeoJSON.html

    see https://postgis.net/docs/ST_AsGeoJSON.html
    """

class AddGeometryColumn(GenericFunction):
    """
    Adds a geometry column to an existing table.

    see https://postgis.net/docs/AddGeometryColumn.html
    """

class DropGeometryColumn(GenericFunction):
    """
    Removes a geometry column from a spatial table.

    see https://postgis.net/docs/DropGeometryColumn.html
    """

class DropGeometryTable(GenericFunction):
    """
    Drops a table and all its references in geometry_columns.

    see https://postgis.net/docs/DropGeometryTable.html
    """

class Find_SRID(GenericFunction):
    """
    Returns the SRID defined for a geometry column.

    see https://postgis.net/docs/Find_SRID.html
    """

class Populate_Geometry_Columns(GenericFunction):
    """
    Ensures geometry columns are defined with type modifiers or have appropriate spatial constraints.

    see https://postgis.net/docs/Populate_Geometry_Columns.html
    """

class UpdateGeometrySRID(GenericFunction):
    """
    Updates the SRID of all features in a geometry column, and the table metadata.

    see https://postgis.net/docs/UpdateGeometrySRID.html
    """

class ST_Collect(GenericFunction):
    """
    Creates a GeometryCollection or Multi* geometry from a set of geometries.

    see https://postgis.net/docs/ST_Collect.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_LineFromMultiPoint(GenericFunction):
    """
    Creates a LineString from a MultiPoint geometry.

    see https://postgis.net/docs/ST_LineFromMultiPoint.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MakeEnvelope(GenericFunction):
    """
    Creates a rectangular Polygon from minimum and maximum coordinates.

    see https://postgis.net/docs/ST_MakeEnvelope.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MakeLine(GenericFunction):
    """
    Creates a Linestring from Point, MultiPoint, or LineString geometries.

    see https://postgis.net/docs/ST_MakeLine.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MakePoint(GenericFunction):
    """
    Creates a 2D, 3DZ or 4D Point.

    see https://postgis.net/docs/ST_MakePoint.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MakePointM(GenericFunction):
    """
    Creates a Point from X, Y and M values.

    see https://postgis.net/docs/ST_MakePointM.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MakePolygon(GenericFunction):
    """
    Creates a Polygon from a shell and optional list of holes.

    see https://postgis.net/docs/ST_MakePolygon.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Point(GenericFunction):
    """
    Creates a Point with the given coordinate values. Alias for ST_MakePoint.

    see https://postgis.net/docs/ST_Point.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Polygon(GenericFunction):
    """
    [geometry] Creates a Polygon from a LineString with a specified SRID.
    OR
    [raster] Returns a multipolygon geometry formed by the union of pixels that have a pixel value that
    is not no data value. If no band number is specified, band num defaults to 1.

    see https://postgis.net/docs/ST_Polygon.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_TileEnvelope(GenericFunction):
    """
    Creates a rectangular Polygon in Web Mercator (SRID:3857) using the XYZ tile system.

    see https://postgis.net/docs/ST_TileEnvelope.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class GeometryType(GenericFunction):
    """
    Returns the type of a geometry as text.

    see https://postgis.net/docs/GeometryType.html
    """

class ST_Boundary(GenericFunction):
    """
    Returns the boundary of a geometry.

    see https://postgis.net/docs/ST_Boundary.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_CoordDim(GenericFunction):
    """
    Return the coordinate dimension of a geometry.

    see https://postgis.net/docs/ST_CoordDim.html
    """

class ST_Dimension(GenericFunction):
    """
    Returns the topological dimension of a geometry.

    see https://postgis.net/docs/ST_Dimension.html
    """

class ST_Dump(GenericFunction):
    """
    Returns a set of geometry_dump rows for the components of a geometry.

    see https://postgis.net/docs/ST_Dump.html

    Return type: :class:`geoalchemy2.types.GeometryDump`.
    """

    type = geoalchemy2.types.GeometryDump()

class ST_DumpPoints(GenericFunction):
    """
    Returns a set of geometry_dump rows for the points in a geometry.

    see https://postgis.net/docs/ST_DumpPoints.html

    Return type: :class:`geoalchemy2.types.GeometryDump`.
    """

    type = geoalchemy2.types.GeometryDump()

class ST_DumpRings(GenericFunction):
    """
    Returns a set of geometry_dump rows for the exterior and interior rings of a Polygon.

    see https://postgis.net/docs/ST_DumpRings.html

    Return type: :class:`geoalchemy2.types.GeometryDump`.
    """

    type = geoalchemy2.types.GeometryDump()

class ST_EndPoint(GenericFunction):
    """
    Returns the last point of a LineString or CircularLineString.

    see https://postgis.net/docs/ST_EndPoint.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Envelope(GenericFunction):
    """
    [geometry] Returns a geometry representing the bounding box of a geometry.
    OR
    [raster] Returns the polygon representation of the extent of the raster.

    see https://postgis.net/docs/ST_Envelope.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_BoundingDiagonal(GenericFunction):
    """
    Returns the diagonal of a geometry's bounding box.

    see https://postgis.net/docs/ST_BoundingDiagonal.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ExteriorRing(GenericFunction):
    """
    Returns a LineString representing the exterior ring of a Polygon.

    see https://postgis.net/docs/ST_ExteriorRing.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeometryN(GenericFunction):
    """
    Return the Nth geometry element of a geometry collection.

    see https://postgis.net/docs/ST_GeometryN.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeometryType(GenericFunction):
    """
    Returns the SQL-MM type of a geometry as text.

    see https://postgis.net/docs/ST_GeometryType.html
    """

class ST_HasArc(GenericFunction):
    """
    Tests if a geometry contains a circular arc

    see https://postgis.net/docs/ST_HasArc.html
    """

class ST_InteriorRingN(GenericFunction):
    """
    Returns the Nth interior ring (hole) of a Polygon.

    see https://postgis.net/docs/ST_InteriorRingN.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_IsPolygonCCW(GenericFunction):
    """
    Tests if Polygons have exterior rings oriented counter-clockwise and interior rings oriented
    clockwise.

    see https://postgis.net/docs/ST_IsPolygonCCW.html
    """

class ST_IsPolygonCW(GenericFunction):
    """
    Tests if Polygons have exterior rings oriented clockwise and interior rings oriented counter-
    clockwise.

    see https://postgis.net/docs/ST_IsPolygonCW.html
    """

class ST_IsClosed(GenericFunction):
    """
    Tests if a LineStrings's start and end points are coincident. For a PolyhedralSurface tests if it is
    closed (volumetric).

    see https://postgis.net/docs/ST_IsClosed.html
    """

class ST_IsCollection(GenericFunction):
    """
    Tests if a geometry is a geometry collection type.

    see https://postgis.net/docs/ST_IsCollection.html
    """

class ST_IsEmpty(GenericFunction):
    """
    [geometry] Tests if a geometry is empty.
    OR
    [raster] Returns true if the raster is empty (width = 0 and height = 0). Otherwise, returns false.

    see https://postgis.net/docs/ST_IsEmpty.html
    """

class ST_IsRing(GenericFunction):
    """
    Tests if a LineString is closed and simple.

    see https://postgis.net/docs/ST_IsRing.html
    """

class ST_IsSimple(GenericFunction):
    """
    Tests if a geometry has no points of self-intersection or self-tangency.

    see https://postgis.net/docs/ST_IsSimple.html
    """

class ST_M(GenericFunction):
    """
    Returns the M coordinate of a Point.

    see https://postgis.net/docs/ST_M.html
    """

class ST_MemSize(GenericFunction):
    """
    [geometry] Returns the amount of memory space a geometry takes.
    OR
    [raster] Returns the amount of space (in bytes) the raster takes.

    see https://postgis.net/docs/ST_MemSize.html
    """

class ST_NDims(GenericFunction):
    """
    Returns the coordinate dimension of a geometry.

    see https://postgis.net/docs/ST_NDims.html
    """

class ST_NPoints(GenericFunction):
    """
    Returns the number of points (vertices) in a geometry.

    see https://postgis.net/docs/ST_NPoints.html
    """

class ST_NRings(GenericFunction):
    """
    Returns the number of rings in a polygonal geometry.

    see https://postgis.net/docs/ST_NRings.html
    """

class ST_NumGeometries(GenericFunction):
    """
    Returns the number of elements in a geometry collection.

    see https://postgis.net/docs/ST_NumGeometries.html
    """

class ST_NumInteriorRings(GenericFunction):
    """
    Returns the number of interior rings (holes) of a Polygon.

    see https://postgis.net/docs/ST_NumInteriorRings.html
    """

class ST_NumInteriorRing(GenericFunction):
    """
    Returns the number of interior rings (holes) of a Polygon. Aias for ST_NumInteriorRings

    see https://postgis.net/docs/ST_NumInteriorRing.html
    """

class ST_NumPatches(GenericFunction):
    """
    Return the number of faces on a Polyhedral Surface. Will return null for non-polyhedral geometries.

    see https://postgis.net/docs/ST_NumPatches.html
    """

class ST_NumPoints(GenericFunction):
    """
    Returns the number of points in a LineString or CircularString.

    see https://postgis.net/docs/ST_NumPoints.html
    """

class ST_PatchN(GenericFunction):
    """
    Returns the Nth geometry (face) of a PolyhedralSurface.

    see https://postgis.net/docs/ST_PatchN.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_PointN(GenericFunction):
    """
    Returns the Nth point in the first LineString or circular LineString in a geometry.

    see https://postgis.net/docs/ST_PointN.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Points(GenericFunction):
    """
    Returns a MultiPoint containing all the coordinates of a geometry.

    see https://postgis.net/docs/ST_Points.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_StartPoint(GenericFunction):
    """
    Returns the first point of a LineString.

    see https://postgis.net/docs/ST_StartPoint.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Summary(GenericFunction):
    """
    [geometry] Returns a text summary of the contents of a geometry.
    OR
    [raster] Returns a text summary of the contents of the raster.

    see https://postgis.net/docs/ST_Summary.html
    """

class ST_X(GenericFunction):
    """
    Returns the X coordinate of a Point.

    see https://postgis.net/docs/ST_X.html
    """

class ST_Y(GenericFunction):
    """
    Returns the Y coordinate of a Point.

    see https://postgis.net/docs/ST_Y.html
    """

class ST_Z(GenericFunction):
    """
    Returns the Z coordinate of a Point.

    see https://postgis.net/docs/ST_Z.html
    """

class ST_Zmflag(GenericFunction):
    """
    Returns a code indicating the ZM coordinate dimension of a geometry.

    see https://postgis.net/docs/ST_Zmflag.html
    """

class ST_AddPoint(GenericFunction):
    """
    Add a point to a LineString.

    see https://postgis.net/docs/ST_AddPoint.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_CollectionExtract(GenericFunction):
    """
    Given a (multi)geometry, return a (multi)geometry consisting only of elements of the specified type.

    see https://postgis.net/docs/ST_CollectionExtract.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_CollectionHomogenize(GenericFunction):
    """
    Given a geometry collection, return the "simplest" representation of the contents.

    see https://postgis.net/docs/ST_CollectionHomogenize.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Force2D(GenericFunction):
    """
    Force the geometries into a "2-dimensional mode".

    see https://postgis.net/docs/ST_Force2D.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Force3D(GenericFunction):
    """
    Force the geometries into XYZ mode. This is an alias for ST_Force3DZ.

    see https://postgis.net/docs/ST_Force_3D.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Force3DZ(GenericFunction):
    """
    Force the geometries into XYZ mode.

    see https://postgis.net/docs/ST_Force_3DZ.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Force3DM(GenericFunction):
    """
    Force the geometries into XYM mode.

    see https://postgis.net/docs/ST_Force_3DZ.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Force4D(GenericFunction):
    """
    Force the geometries into XYZM mode.

    see https://postgis.net/docs/ST_Force_4D.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ForcePolygonCCW(GenericFunction):
    """
    Orients all exterior rings counter-clockwise and all interior rings clockwise.

    see https://postgis.net/docs/ST_ForcePolygonCCW.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ForceCollection(GenericFunction):
    """
    Convert the geometry into a GEOMETRYCOLLECTION.

    see https://postgis.net/docs/ST_Force_Collection.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ForcePolygonCW(GenericFunction):
    """
    Orients all exterior rings clockwise and all interior rings counter-clockwise.

    see https://postgis.net/docs/ST_ForcePolygonCW.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ForceSFS(GenericFunction):
    """
    Force the geometries to use SFS 1.1 geometry types only.

    see https://postgis.net/docs/ST_ForceSFS.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ForceRHR(GenericFunction):
    """
    Force the orientation of the vertices in a polygon to follow the Right-Hand-Rule.

    see https://postgis.net/docs/ST_ForceRHR.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ForceCurve(GenericFunction):
    """
    Upcast a geometry into its curved type, if applicable.

    see https://postgis.net/docs/ST_ForceCurve.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_LineMerge(GenericFunction):
    """
    Return a (set of) LineString(s) formed by sewing together a MULTILINESTRING.

    see https://postgis.net/docs/ST_LineMerge.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Multi(GenericFunction):
    """
    Return the geometry as a MULTI* geometry.

    see https://postgis.net/docs/ST_Multi.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Normalize(GenericFunction):
    """
    Return the geometry in its canonical form.

    see https://postgis.net/docs/ST_Normalize.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_QuantizeCoordinates(GenericFunction):
    """
    Sets least significant bits of coordinates to zero

    see https://postgis.net/docs/ST_QuantizeCoordinates.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_RemovePoint(GenericFunction):
    """
    Remove point from a linestring.

    see https://postgis.net/docs/ST_RemovePoint.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Reverse(GenericFunction):
    """
    Return the geometry with vertex order reversed.

    see https://postgis.net/docs/ST_Reverse.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Segmentize(GenericFunction):
    """
    Return a modified geometry/geography having no segment longer than the given distance.

    see https://postgis.net/docs/ST_Segmentize.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_SetPoint(GenericFunction):
    """
    Replace point of a linestring with a given point.

    see https://postgis.net/docs/ST_SetPoint.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_SnapToGrid(GenericFunction):
    """
    [geometry] Snap all points of the input geometry to a regular grid.
    OR
    [raster] Resample a raster by snapping it to a grid. New pixel values are computed using the
    NearestNeighbor (english or american spelling), Bilinear, Cubic, CubicSpline or Lanczos resampling
    algorithm. Default is NearestNeighbor.

    see https://postgis.net/docs/ST_SnapToGrid.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Snap(GenericFunction):
    """
    Snap segments and vertices of input geometry to vertices of a reference geometry.

    see https://postgis.net/docs/ST_Snap.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_SwapOrdinates(GenericFunction):
    """
    Returns a version of the given geometry with given ordinate values swapped.

    see https://postgis.net/docs/ST_SwapOrdinates.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_IsValid(GenericFunction):
    """
    Tests if a geometry is well-formed in 2D.

    see https://postgis.net/docs/ST_IsValid.html
    """

class ST_IsValidDetail(GenericFunction):
    """
    Returns a valid_detail row stating if a geometry is valid, and if not a reason why and a location.

    see https://postgis.net/docs/ST_IsValidDetail.html
    """

class ST_IsValidReason(GenericFunction):
    """
    Returns text stating if a geometry is valid, or a reason for invalidity.

    see https://postgis.net/docs/ST_IsValidReason.html
    """

class ST_SetSRID(GenericFunction):
    """
    [geometry] Set the SRID on a geometry to a particular integer value.
    OR
    [raster] Sets the SRID of a raster to a particular integer srid defined in the spatial_ref_sys
    table.

    see https://postgis.net/docs/ST_SetSRID.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_SRID(GenericFunction):
    """
    [geometry] Returns the spatial reference identifier for the ST_Geometry as defined in
    spatial_ref_sys table.
    OR
    [raster] Returns the spatial reference identifier of the raster as defined in spatial_ref_sys table.

    see https://postgis.net/docs/ST_SRID.html
    """

class ST_Transform(GenericFunction):
    """
    [geometry] Return a new geometry with its coordinates transformed to a different spatial reference
    system.
    OR
    [raster] Reprojects a raster in a known spatial reference system to another known spatial reference
    system using specified resampling algorithm. Options are NearestNeighbor, Bilinear, Cubic,
    CubicSpline, Lanczos defaulting to NearestNeighbor.

    see https://postgis.net/docs/ST_Transform.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_BdPolyFromText(GenericFunction):
    """
    Construct a Polygon given an arbitrary collection of closed linestrings as a MultiLineString Well-
    Known text representation.

    see https://postgis.net/docs/ST_BdPolyFromText.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_BdMPolyFromText(GenericFunction):
    """
    Construct a MultiPolygon given an arbitrary collection of closed linestrings as a MultiLineString
    text representation Well-Known text representation.

    see https://postgis.net/docs/ST_BdMPolyFromText.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeogFromText(GenericFunction):
    """
    Return a specified geography value from Well-Known Text representation or extended (WKT).

    see https://postgis.net/docs/ST_GeogFromText.html

    Return type: :class:`geoalchemy2.types.Geography`.
    """

    type = geoalchemy2.types.Geography()

class ST_GeographyFromText(GenericFunction):
    """
    Return a specified geography value from Well-Known Text representation or extended (WKT).

    see https://postgis.net/docs/ST_GeographyFromText.html

    Return type: :class:`geoalchemy2.types.Geography`.
    """

    type = geoalchemy2.types.Geography()

class ST_GeomCollFromText(GenericFunction):
    """
    Makes a collection Geometry from collection WKT with the given SRID. If SRID is not given, it
    defaults to 0.

    see https://postgis.net/docs/ST_GeomCollFromText.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeomFromEWKT(GenericFunction):
    """
    Return a specified ST_Geometry value from Extended Well-Known Text representation (EWKT).

    see https://postgis.net/docs/ST_GeomFromEWKT.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeometryFromText(GenericFunction):
    """
    Return a specified ST_Geometry value from Well-Known Text representation (WKT). This is an alias
    name for ST_GeomFromText

    see https://postgis.net/docs/ST_GeometryFromText.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeomFromText(GenericFunction):
    """
    Return a specified ST_Geometry value from Well-Known Text representation (WKT).

    see https://postgis.net/docs/ST_GeomFromText.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_LineFromText(GenericFunction):
    """
    Makes a Geometry from WKT representation with the given SRID. If SRID is not given, it defaults to
    0.

    see https://postgis.net/docs/ST_LineFromText.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MLineFromText(GenericFunction):
    """
    Return a specified ST_MultiLineString value from WKT representation.

    see https://postgis.net/docs/ST_MLineFromText.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MPointFromText(GenericFunction):
    """
    Makes a Geometry from WKT with the given SRID. If SRID is not given, it defaults to 0.

    see https://postgis.net/docs/ST_MPointFromText.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MPolyFromText(GenericFunction):
    """
    Makes a MultiPolygon Geometry from WKT with the given SRID. If SRID is not given, it defaults to 0.

    see https://postgis.net/docs/ST_MPolyFromText.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_PointFromText(GenericFunction):
    """
    Makes a point Geometry from WKT with the given SRID. If SRID is not given, it defaults to unknown.

    see https://postgis.net/docs/ST_PointFromText.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_PolygonFromText(GenericFunction):
    """
    Makes a Geometry from WKT with the given SRID. If SRID is not given, it defaults to 0.

    see https://postgis.net/docs/ST_PolygonFromText.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_WKTToSQL(GenericFunction):
    """
    Return a specified ST_Geometry value from Well-Known Text representation (WKT). This is an alias
    name for ST_GeomFromText

    see https://postgis.net/docs/ST_WKTToSQL.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeogFromWKB(GenericFunction):
    """
    Creates a geography instance from a Well-Known Binary geometry representation (WKB) or extended Well
    Known Binary (EWKB).

    see https://postgis.net/docs/ST_GeogFromWKB.html

    Return type: :class:`geoalchemy2.types.Geography`.
    """

    type = geoalchemy2.types.Geography()

class ST_GeomFromEWKB(GenericFunction):
    """
    Return a specified ST_Geometry value from Extended Well-Known Binary representation (EWKB).

    see https://postgis.net/docs/ST_GeomFromEWKB.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeomFromWKB(GenericFunction):
    """
    Creates a geometry instance from a Well-Known Binary geometry representation (WKB) and optional
    SRID.

    see https://postgis.net/docs/ST_GeomFromWKB.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_LineFromWKB(GenericFunction):
    """
    Makes a LINESTRING from WKB with the given SRID

    see https://postgis.net/docs/ST_LineFromWKB.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_LinestringFromWKB(GenericFunction):
    """
    Makes a geometry from WKB with the given SRID.

    see https://postgis.net/docs/ST_LinestringFromWKB.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_PointFromWKB(GenericFunction):
    """
    Makes a geometry from WKB with the given SRID

    see https://postgis.net/docs/ST_PointFromWKB.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_WKBToSQL(GenericFunction):
    """
    Return a specified ST_Geometry value from Well-Known Binary representation (WKB). This is an alias
    name for ST_GeomFromWKB that takes no srid

    see https://postgis.net/docs/ST_WKBToSQL.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Box2dFromGeoHash(GenericFunction):
    """
    Return a BOX2D from a GeoHash string.

    see https://postgis.net/docs/ST_Box2dFromGeoHash.html
    """

class ST_GeomFromGeoHash(GenericFunction):
    """
    Return a geometry from a GeoHash string.

    see https://postgis.net/docs/ST_GeomFromGeoHash.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeomFromGML(GenericFunction):
    """
    Takes as input GML representation of geometry and outputs a PostGIS geometry object

    see https://postgis.net/docs/ST_GeomFromGML.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeomFromGeoJSON(GenericFunction):
    """
    Takes as input a geojson representation of a geometry and outputs a PostGIS geometry object

    see https://postgis.net/docs/ST_GeomFromGeoJSON.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeomFromKML(GenericFunction):
    """
    Takes as input KML representation of geometry and outputs a PostGIS geometry object

    see https://postgis.net/docs/ST_GeomFromKML.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeomFromTWKB(GenericFunction):
    """
    Creates a geometry instance from a TWKB ("Tiny Well-Known Binary") geometry representation.

    see https://postgis.net/docs/ST_GeomFromTWKB.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GMLToSQL(GenericFunction):
    """
    Return a specified ST_Geometry value from GML representation. This is an alias name for
    ST_GeomFromGML

    see https://postgis.net/docs/ST_GMLToSQL.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_LineFromEncodedPolyline(GenericFunction):
    """
    Creates a LineString from an Encoded Polyline.

    see https://postgis.net/docs/ST_LineFromEncodedPolyline.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_PointFromGeoHash(GenericFunction):
    """
    Return a point from a GeoHash string.

    see https://postgis.net/docs/ST_PointFromGeoHash.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_AsEWKT(GenericFunction):
    """
    Return the Well-Known Text (WKT) representation of the geometry with SRID meta data.

    see https://postgis.net/docs/ST_AsEWKT.html
    """

class ST_AsText(GenericFunction):
    """
    Return the Well-Known Text (WKT) representation of the geometry/geography without SRID metadata.

    see https://postgis.net/docs/ST_AsText.html
    """

class ST_AsBinary(GenericFunction):
    """
    [geometry] Return the Well-Known Binary (WKB) representation of the geometry/geography without SRID
    meta data.
    OR
    [raster] Return the Well-Known Binary (WKB) representation of the raster.

    see https://postgis.net/docs/ST_AsBinary.html
    """

class ST_AsEWKB(GenericFunction):
    """
    Return the Well-Known Binary (WKB) representation of the geometry with SRID meta data.

    see https://postgis.net/docs/ST_AsEWKB.html
    """

class ST_AsHEXEWKB(GenericFunction):
    """
    Returns a Geometry in HEXEWKB format (as text) using either little-endian (NDR) or big-endian (XDR)
    encoding.

    see https://postgis.net/docs/ST_AsHEXEWKB.html
    """

class ST_AsEncodedPolyline(GenericFunction):
    """
    Returns an Encoded Polyline from a LineString geometry.

    see https://postgis.net/docs/ST_AsEncodedPolyline.html
    """

class ST_AsGeobuf(GenericFunction):
    """
    Return a Geobuf representation of a set of rows.

    see https://postgis.net/docs/ST_AsGeobuf.html
    """

class ST_AsGML(GenericFunction):
    """
    Return the geometry as a GML version 2 or 3 element.

    see https://postgis.net/docs/ST_AsGML.html
    """

class ST_AsKML(GenericFunction):
    """
    Return the geometry as a KML element. Several variants. Default version=2, default
    maxdecimaldigits=15

    see https://postgis.net/docs/ST_AsKML.html
    """

class ST_AsLatLonText(GenericFunction):
    """
    Return the Degrees, Minutes, Seconds representation of the given point.

    see https://postgis.net/docs/ST_AsLatLonText.html
    """

class ST_AsMVTGeom(GenericFunction):
    """
    Transform a geometry into the coordinate space of a Mapbox Vector Tile.

    see https://postgis.net/docs/ST_AsMVTGeom.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_AsMVT(GenericFunction):
    """
    Aggregate function returning a Mapbox Vector Tile representation of a set of rows.

    see https://postgis.net/docs/ST_AsMVT.html
    """

class ST_AsSVG(GenericFunction):
    """
    Returns SVG path data for a geometry.

    see https://postgis.net/docs/ST_AsSVG.html
    """

class ST_AsTWKB(GenericFunction):
    """
    Returns the geometry as TWKB, aka "Tiny Well-Known Binary"

    see https://postgis.net/docs/ST_AsTWKB.html
    """

class ST_AsX3D(GenericFunction):
    """
    Returns a Geometry in X3D xml node element format: ISO-IEC-19776-1.2-X3DEncodings-XML

    see https://postgis.net/docs/ST_AsX3D.html
    """

class ST_GeoHash(GenericFunction):
    """
    Return a GeoHash representation of the geometry.

    see https://postgis.net/docs/ST_GeoHash.html
    """

class ST_3DIntersects(GenericFunction):
    """
    Returns TRUE if the Geometries "spatially intersect" in 3D - only for points, linestrings, polygons,
    polyhedral surface (area).

    see https://postgis.net/docs/ST_3DIntersects.html
    """

class ST_Contains(GenericFunction):
    """
    [geometry] Returns true if and only if no points of B lie in the exterior of A, and at least one
    point of the interior of B lies in the interior of A.
    OR
    [raster] Return true if no points of raster rastB lie in the exterior of raster rastA and at least
    one point of the interior of rastB lies in the interior of rastA.

    see https://postgis.net/docs/ST_Contains.html
    """

class ST_ContainsProperly(GenericFunction):
    """
    [geometry] Returns true if B intersects the interior of A but not the boundary (or exterior). A does
    not contain properly itself, but does contain itself.
    OR
    [raster] Return true if rastB intersects the interior of rastA but not the boundary or exterior of
    rastA.

    see https://postgis.net/docs/ST_ContainsProperly.html
    """

class ST_Covers(GenericFunction):
    """
    [geometry] Returns 1 (TRUE) if no point in Geometry B is outside Geometry A
    OR
    [raster] Return true if no points of raster rastB lie outside raster rastA.

    see https://postgis.net/docs/ST_Covers.html
    """

class ST_CoveredBy(GenericFunction):
    """
    [geometry] Returns 1 (TRUE) if no point in Geometry/Geography A is outside Geometry/Geography B
    OR
    [raster] Return true if no points of raster rastA lie outside raster rastB.

    see https://postgis.net/docs/ST_CoveredBy.html
    """

class ST_Crosses(GenericFunction):
    """
    Returns TRUE if the supplied geometries have some, but not all, interior points in common.

    see https://postgis.net/docs/ST_Crosses.html
    """

class ST_LineCrossingDirection(GenericFunction):
    """
    Given 2 linestrings, returns a number between -3 and 3 denoting what kind of crossing behavior. 0 is
    no crossing.

    see https://postgis.net/docs/ST_LineCrossingDirection.html
    """

class ST_Disjoint(GenericFunction):
    """
    [geometry] Returns TRUE if the Geometries do not "spatially intersect" - if they do not share any
    space together.
    OR
    [raster] Return true if raster rastA does not spatially intersect rastB.

    see https://postgis.net/docs/ST_Disjoint.html
    """

class ST_Equals(GenericFunction):
    """
    Returns true if the given geometries represent the same geometry. Directionality is ignored.

    see https://postgis.net/docs/ST_Equals.html
    """

class ST_Intersects(GenericFunction):
    """
    [geometry] Returns TRUE if the Geometries/Geography "spatially intersect in 2D" - (share any portion
    of space) and FALSE if they don't (they are Disjoint). For geography tolerance is 0.00001 meters (so
    any points that close are considered to intersect)
    OR
    [raster] Return true if raster rastA spatially intersects raster rastB.

    see https://postgis.net/docs/ST_Intersects.html
    """

class ST_OrderingEquals(GenericFunction):
    """
    Returns true if the given geometries represent the same geometry and points are in the same
    directional order.

    see https://postgis.net/docs/ST_OrderingEquals.html
    """

class ST_Overlaps(GenericFunction):
    """
    [geometry] Returns TRUE if the Geometries share space, are of the same dimension, but are not
    completely contained by each other.
    OR
    [raster] Return true if raster rastA and rastB intersect but one does not completely contain the
    other.

    see https://postgis.net/docs/ST_Overlaps.html
    """

class ST_PointInsideCircle(GenericFunction):
    """
    Is the point geometry inside the circle defined by center_x, center_y, radius

    see https://postgis.net/docs/ST_PointInsideCircle.html
    """

class ST_Relate(GenericFunction):
    """
    Returns true if this Geometry is spatially related to anotherGeometry, by testing for intersections
    between the Interior, Boundary and Exterior of the two geometries as specified by the values in the
    intersectionMatrixPattern. If no intersectionMatrixPattern is passed in, then returns the maximum
    intersectionMatrixPattern that relates the 2 geometries.

    see https://postgis.net/docs/ST_Relate.html
    """

class ST_RelateMatch(GenericFunction):
    """
    Returns true if intersectionMattrixPattern1 implies intersectionMatrixPattern2

    see https://postgis.net/docs/ST_RelateMatch.html
    """

class ST_Touches(GenericFunction):
    """
    [geometry] Returns TRUE if the geometries have at least one point in common, but their interiors do
    not intersect.
    OR
    [raster] Return true if raster rastA and rastB have at least one point in common but their interiors
    do not intersect.

    see https://postgis.net/docs/ST_Touches.html
    """

class ST_Within(GenericFunction):
    """
    [geometry] Returns true if the geometry A is completely inside geometry B
    OR
    [raster] Return true if no points of raster rastA lie in the exterior of raster rastB and at least
    one point of the interior of rastA lies in the interior of rastB.

    see https://postgis.net/docs/ST_Within.html
    """

class ST_3DDWithin(GenericFunction):
    """
    For 3d (z) geometry type Returns true if two geometries 3d distance is within number of units.

    see https://postgis.net/docs/ST_3DDWithin.html
    """

class ST_3DDFullyWithin(GenericFunction):
    """
    Returns true if all of the 3D geometries are within the specified distance of one another.

    see https://postgis.net/docs/ST_3DDFullyWithin.html
    """

class ST_DFullyWithin(GenericFunction):
    """
    [geometry] Returns true if all of the geometries are within the specified distance of one another
    OR
    [raster] Return true if rasters rastA and rastB are fully within the specified distance of each
    other.

    see https://postgis.net/docs/ST_DFullyWithin.html
    """

class ST_DWithin(GenericFunction):
    """
    [geometry] Returns true if the geometries are within the specified distance of one another. For
    geometry units are in those of spatial reference and for geography units are in meters and
    measurement is defaulted to use_spheroid=true (measure around spheroid), for faster check,
    use_spheroid=false to measure along sphere.
    OR
    [raster] Return true if rasters rastA and rastB are within the specified distance of each other.

    see https://postgis.net/docs/ST_DWithin.html
    """

class ST_Area(GenericFunction):
    """
    Returns the area of a polygonal geometry.

    see https://postgis.net/docs/ST_Area.html
    """

class ST_Azimuth(GenericFunction):
    """
    Returns the north-based azimuth as the angle in radians measured clockwise from the vertical on
    pointA to pointB.

    see https://postgis.net/docs/ST_Azimuth.html
    """

class ST_Angle(GenericFunction):
    """
    Returns the angle between 3 points, or between 2 vectors (4 points or 2 lines).

    see https://postgis.net/docs/ST_Angle.html
    """

class ST_ClosestPoint(GenericFunction):
    """
    Returns the 2D point on g1 that is closest to g2. This is the first point of the shortest line.

    see https://postgis.net/docs/ST_ClosestPoint.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_3DClosestPoint(GenericFunction):
    """
    Returns the 3D point on g1 that is closest to g2. This is the first point of the 3D shortest line.

    see https://postgis.net/docs/ST_3DClosestPoint.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Distance(GenericFunction):
    """
    Returns the distance between two geometry or geography values.

    see https://postgis.net/docs/ST_Distance.html
    """

class ST_3DDistance(GenericFunction):
    """
    Returns the 3D cartesian minimum distance (based on spatial ref) between two geometries in projected
    units.

    see https://postgis.net/docs/ST_3DDistance.html
    """

class ST_DistanceSphere(GenericFunction):
    """
    Returns minimum distance in meters between two lon/lat geometries using a spherical earth model.

    see https://postgis.net/docs/ST_DistanceSphere.html
    """

class ST_DistanceSpheroid(GenericFunction):
    """
    Returns the minimum distance between two lon/lat geometries using a spheroidal earth model.

    see https://postgis.net/docs/ST_DistanceSpheroid.html
    """

class ST_FrechetDistance(GenericFunction):
    """
    Returns the Fréchet distance between two geometries.

    see https://postgis.net/docs/ST_FrechetDistance.html
    """

class ST_HausdorffDistance(GenericFunction):
    """
    Returns the Hausdorff distance between two geometries.

    see https://postgis.net/docs/ST_HausdorffDistance.html
    """

class ST_Length(GenericFunction):
    """
    Returns the 2D length of a linear geometry.

    see https://postgis.net/docs/ST_Length.html
    """

class ST_Length2D(GenericFunction):
    """
    Returns the 2D length of a linear geometry. Alias for ST_Length

    see https://postgis.net/docs/ST_Length2D.html
    """

class ST_3DLength(GenericFunction):
    """
    Returns the 3D length of a linear geometry.

    see https://postgis.net/docs/ST_3DLength.html
    """

class ST_LengthSpheroid(GenericFunction):
    """
    Returns the 2D or 3D length/perimeter of a lon/lat geometry on a spheroid.

    see https://postgis.net/docs/ST_LengthSpheroid.html
    """

class ST_LongestLine(GenericFunction):
    """
    Returns the 2D longest line between two geometries.

    see https://postgis.net/docs/ST_LongestLine.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_3DLongestLine(GenericFunction):
    """
    Returns the 3D longest line between two geometries

    see https://postgis.net/docs/ST_3DLongestLine.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MaxDistance(GenericFunction):
    """
    Returns the 2D largest distance between two geometries in projected units.

    see https://postgis.net/docs/ST_MaxDistance.html
    """

class ST_3DMaxDistance(GenericFunction):
    """
    Returns the 3D cartesian maximum distance (based on spatial ref) between two geometries in projected
    units.

    see https://postgis.net/docs/ST_3DMaxDistance.html
    """

class ST_MinimumClearance(GenericFunction):
    """
    Returns the minimum clearance of a geometry, a measure of a geometry's robustness.

    see https://postgis.net/docs/ST_MinimumClearance.html
    """

class ST_MinimumClearanceLine(GenericFunction):
    """
    Returns the two-point LineString spanning a geometry's minimum clearance.

    see https://postgis.net/docs/ST_MinimumClearanceLine.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Perimeter(GenericFunction):
    """
    Returns the length of the boundary of a polygonal geometry or geography.

    see https://postgis.net/docs/ST_Perimeter.html
    """

class ST_Perimeter2D(GenericFunction):
    """
    Returns the 2D perimeter of a polygonal geometry. Alias for ST_Perimeter.

    see https://postgis.net/docs/ST_Perimeter2D.html
    """

class ST_3DPerimeter(GenericFunction):
    """
    Returns the 3D perimeter of a polygonal geometry.

    see https://postgis.net/docs/ST_3DPerimeter.html
    """

class ST_Project(GenericFunction):
    """
    Returns a point projected from a start point by a distance and bearing (azimuth).

    see https://postgis.net/docs/ST_Project.html

    Return type: :class:`geoalchemy2.types.Geography`.
    """

    type = geoalchemy2.types.Geography()

class ST_ShortestLine(GenericFunction):
    """
    Returns the 2D shortest line between two geometries

    see https://postgis.net/docs/ST_ShortestLine.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_3DShortestLine(GenericFunction):
    """
    Returns the 3D shortest line between two geometries

    see https://postgis.net/docs/ST_3DShortestLine.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Buffer(GenericFunction):
    """
    (T) Returns a geometry covering all points within a given distance from the input geometry.

    see https://postgis.net/docs/ST_Buffer.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_BuildArea(GenericFunction):
    """
    Creates an areal geometry formed by the constituent linework of given geometry

    see https://postgis.net/docs/ST_BuildArea.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Centroid(GenericFunction):
    """
    Returns the geometric center of a geometry.

    see https://postgis.net/docs/ST_Centroid.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ClipByBox2D(GenericFunction):
    """
    Returns the portion of a geometry falling within a rectangle.

    see https://postgis.net/docs/ST_ClipByBox2D.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ConcaveHull(GenericFunction):
    """
    The concave hull of a geometry represents a possibly concave geometry that encloses all geometries
    within the set. You can think of it as shrink wrapping.

    see https://postgis.net/docs/ST_ConcaveHull.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ConvexHull(GenericFunction):
    """
    [geometry] Computes the convex hull of a geometry.
    OR
    [raster] Return the convex hull geometry of the raster including pixel values equal to
    BandNoDataValue. For regular shaped and non-skewed rasters, this gives the same result as
    ST_Envelope so only useful for irregularly shaped or skewed rasters.

    see https://postgis.net/docs/ST_ConvexHull.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_CurveToLine(GenericFunction):
    """
    Converts a CIRCULARSTRING/CURVEPOLYGON/MULTISURFACE to a LINESTRING/POLYGON/MULTIPOLYGON

    see https://postgis.net/docs/ST_CurveToLine.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_DelaunayTriangles(GenericFunction):
    """
    Return a Delaunay triangulation around the given input points.

    see https://postgis.net/docs/ST_DelaunayTriangles.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Difference(GenericFunction):
    """
    Returns a geometry that represents that part of geometry A that does not intersect with geometry B.

    see https://postgis.net/docs/ST_Difference.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_FlipCoordinates(GenericFunction):
    """
    Returns a version of the given geometry with X and Y axis flipped. Useful for people who have built
    latitude/longitude features and need to fix them.

    see https://postgis.net/docs/ST_FlipCoordinates.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeneratePoints(GenericFunction):
    """
    Converts a polygon or multi-polygon into a multi-point composed of randomly location points within
    the original areas.

    see https://postgis.net/docs/ST_GeneratePoints.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_GeometricMedian(GenericFunction):
    """
    Returns the geometric median of a MultiPoint.

    see https://postgis.net/docs/ST_GeometricMedian.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Intersection(GenericFunction):
    """
    [geometry] (T) Returns a geometry that represents the shared portion of geomA and geomB.
    OR
    [raster] Returns a raster or a set of geometry-pixelvalue pairs representing the shared portion of
    two rasters or the geometrical intersection of a vectorization of the raster and a geometry.

    see https://postgis.net/docs/ST_Intersection.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_LineToCurve(GenericFunction):
    """
    Converts a LINESTRING/POLYGON to a CIRCULARSTRING, CURVEPOLYGON

    see https://postgis.net/docs/ST_LineToCurve.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MakeValid(GenericFunction):
    """
    Attempts to make an invalid geometry valid without losing vertices.

    see https://postgis.net/docs/ST_MakeValid.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MemUnion(GenericFunction):
    """
    Same as ST_Union, only memory-friendly (uses less memory and more processor time).

    see https://postgis.net/docs/ST_MemUnion.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MinimumBoundingCircle(GenericFunction):
    """
    Returns the smallest circle polygon that can fully contain a geometry. Default uses 48 segments per
    quarter circle.

    see https://postgis.net/docs/ST_MinimumBoundingCircle.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MinimumBoundingRadius(GenericFunction):
    """
    Returns the center point and radius of the smallest circle that can fully contain a geometry.

    see https://postgis.net/docs/ST_MinimumBoundingRadius.html
    """

class ST_OrientedEnvelope(GenericFunction):
    """
    Returns a minimum rotated rectangle enclosing a geometry.

    see https://postgis.net/docs/ST_OrientedEnvelope.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Polygonize(GenericFunction):
    """
    Aggregate. Creates a GeometryCollection containing possible polygons formed from the constituent
    linework of a set of geometries.

    see https://postgis.net/docs/ST_Polygonize.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Node(GenericFunction):
    """
    Node a set of linestrings.

    see https://postgis.net/docs/ST_Node.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_OffsetCurve(GenericFunction):
    """
    Return an offset line at a given distance and side from an input line. Useful for computing parallel
    lines about a center line

    see https://postgis.net/docs/ST_OffsetCurve.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_PointOnSurface(GenericFunction):
    """
    Returns a POINT guaranteed to lie on the surface.

    see https://postgis.net/docs/ST_PointOnSurface.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_RemoveRepeatedPoints(GenericFunction):
    """
    Returns a version of the given geometry with duplicated points removed.

    see https://postgis.net/docs/ST_RemoveRepeatedPoints.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_SharedPaths(GenericFunction):
    """
    Returns a collection containing paths shared by the two input linestrings/multilinestrings.

    see https://postgis.net/docs/ST_SharedPaths.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ShiftLongitude(GenericFunction):
    """
    Toggle geometry coordinates between -180..180 and 0..360 ranges.

    see https://postgis.net/docs/ST_Shift_Longitude.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_WrapX(GenericFunction):
    """
    Wrap a geometry around an X value.

    see https://postgis.net/docs/ST_WrapX.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Simplify(GenericFunction):
    """
    Returns a "simplified" version of the given geometry using the Douglas-Peucker algorithm.

    see https://postgis.net/docs/ST_Simplify.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_SimplifyPreserveTopology(GenericFunction):
    """
    Returns a "simplified" version of the given geometry using the Douglas-Peucker algorithm. Will avoid
    creating derived geometries (polygons in particular) that are invalid.

    see https://postgis.net/docs/ST_SimplifyPreserveTopology.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_SimplifyVW(GenericFunction):
    """
    Returns a "simplified" version of the given geometry using the Visvalingam-Whyatt algorithm

    see https://postgis.net/docs/ST_SimplifyVW.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ChaikinSmoothing(GenericFunction):
    """
    Returns a "smoothed" version of the given geometry using the Chaikin algorithm

    see https://postgis.net/docs/ST_ChaikinSmoothing.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_FilterByM(GenericFunction):
    """
    Filters vertex points based on their m-value

    see https://postgis.net/docs/ST_FilterByM.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_SetEffectiveArea(GenericFunction):
    """
    Sets the effective area for each vertex, storing the value in the M ordinate. A simplified geometry
    can then be generated by filtering on the M ordinate.

    see https://postgis.net/docs/ST_SetEffectiveArea.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Split(GenericFunction):
    """
    Returns a collection of geometries resulting by splitting a geometry.

    see https://postgis.net/docs/ST_Split.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_SymDifference(GenericFunction):
    """
    Returns a geometry that represents the portions of A and B that do not intersect. It is called a
    symmetric difference because ST_SymDifference(A,B) = ST_SymDifference(B,A).

    see https://postgis.net/docs/ST_SymDifference.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Subdivide(GenericFunction):
    """
    Returns a set of geometry where no geometry in the set has more than the specified number of
    vertices.

    see https://postgis.net/docs/ST_Subdivide.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Union(GenericFunction):
    """
    [geometry] Returns a geometry that represents the point set union of the Geometries.
    OR
    [raster] Returns the union of a set of raster tiles into a single raster composed of 1 or more
    bands.

    see https://postgis.net/docs/ST_Union.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_UnaryUnion(GenericFunction):
    """
    Like ST_Union, but working at the geometry component level.

    see https://postgis.net/docs/ST_UnaryUnion.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_VoronoiLines(GenericFunction):
    """
    Returns the boundaries between the cells of the Voronoi diagram constructed from the vertices of a
    geometry.

    see https://postgis.net/docs/ST_VoronoiLines.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_VoronoiPolygons(GenericFunction):
    """
    Returns the cells of the Voronoi diagram constructed from the vertices of a geometry.

    see https://postgis.net/docs/ST_VoronoiPolygons.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Affine(GenericFunction):
    """
    Apply a 3D affine transformation to a geometry.

    see https://postgis.net/docs/ST_Affine.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Rotate(GenericFunction):
    """
    Rotates a geometry about an origin point.

    see https://postgis.net/docs/ST_Rotate.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_RotateX(GenericFunction):
    """
    Rotates a geometry about the X axis.

    see https://postgis.net/docs/ST_RotateX.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_RotateY(GenericFunction):
    """
    Rotates a geometry about the Y axis.

    see https://postgis.net/docs/ST_RotateY.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_RotateZ(GenericFunction):
    """
    Rotates a geometry about the Z axis.

    see https://postgis.net/docs/ST_RotateZ.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Scale(GenericFunction):
    """
    Scales a geometry by given factors.

    see https://postgis.net/docs/ST_Scale.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Translate(GenericFunction):
    """
    Translates a geometry by given offsets.

    see https://postgis.net/docs/ST_Translate.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_TransScale(GenericFunction):
    """
    Translates and scales a geometry by given offsets and factors.

    see https://postgis.net/docs/ST_TransScale.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ClusterDBSCAN(GenericFunction):
    """
    Window function that returns a cluster id for each input geometry using the DBSCAN algorithm.

    see https://postgis.net/docs/ST_ClusterDBSCAN.html
    """

class ST_ClusterIntersecting(GenericFunction):
    """
    Aggregate function that clusters the input geometries into connected sets.

    see https://postgis.net/docs/ST_ClusterIntersecting.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ClusterKMeans(GenericFunction):
    """
    Window function that returns a cluster id for each input geometry using the K-means algorithm.

    see https://postgis.net/docs/ST_ClusterKMeans.html
    """

class ST_ClusterWithin(GenericFunction):
    """
    Aggregate function that clusters the input geometries by separation distance.

    see https://postgis.net/docs/ST_ClusterWithin.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class Box2D(GenericFunction):
    """
    Returns a BOX2D representing the 2D extent of the geometry.

    see https://postgis.net/docs/Box2D_type.html
    """

class Box3D(GenericFunction):
    """
    [geometry] Returns a BOX3D representing the 3D extent of the geometry.
    OR
    [raster] Returns the box 3d representation of the enclosing box of the raster.

    see https://postgis.net/docs/Box3D_type.html
    """

class ST_EstimatedExtent(GenericFunction):
    """
    Return the 'estimated' extent of a spatial table.

    see https://postgis.net/docs/ST_EstimatedExtent.html
    """

class ST_Expand(GenericFunction):
    """
    Returns a bounding box expanded from another bounding box or a geometry.

    see https://postgis.net/docs/ST_Expand.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Extent(GenericFunction):
    """
    an aggregate function that returns the bounding box that bounds rows of geometries.

    see https://postgis.net/docs/ST_Extent.html
    """

class ST_3DExtent(GenericFunction):
    """
    an aggregate function that returns the 3D bounding box that bounds rows of geometries.

    see https://postgis.net/docs/ST_3DExtent.html
    """

class ST_MakeBox2D(GenericFunction):
    """
    Creates a BOX2D defined by two 2D point geometries.

    see https://postgis.net/docs/ST_MakeBox2D.html
    """

class ST_3DMakeBox(GenericFunction):
    """
    Creates a BOX3D defined by two 3D point geometries.

    see https://postgis.net/docs/ST_3DMakeBox.html
    """

class ST_XMax(GenericFunction):
    """
    Returns the X maxima of a 2D or 3D bounding box or a geometry.

    see https://postgis.net/docs/ST_XMax.html
    """

class ST_XMin(GenericFunction):
    """
    Returns the X minima of a 2D or 3D bounding box or a geometry.

    see https://postgis.net/docs/ST_XMin.html
    """

class ST_YMax(GenericFunction):
    """
    Returns the Y maxima of a 2D or 3D bounding box or a geometry.

    see https://postgis.net/docs/ST_YMax.html
    """

class ST_YMin(GenericFunction):
    """
    Returns the Y minima of a 2D or 3D bounding box or a geometry.

    see https://postgis.net/docs/ST_YMin.html
    """

class ST_ZMax(GenericFunction):
    """
    Returns the Z maxima of a 2D or 3D bounding box or a geometry.

    see https://postgis.net/docs/ST_ZMax.html
    """

class ST_ZMin(GenericFunction):
    """
    Returns the Z minima of a 2D or 3D bounding box or a geometry.

    see https://postgis.net/docs/ST_ZMin.html
    """

class ST_LineInterpolatePoint(GenericFunction):
    """
    Returns a point interpolated along a line. Second argument is a float8 between 0 and 1 representing
    fraction of total length of linestring the point has to be located.

    see https://postgis.net/docs/ST_LineInterpolatePoint.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_3DLineInterpolatePoint(GenericFunction):
    """
    Returns a point interpolated along a line in 3D. Second argument is a float8 between 0 and 1
    representing fraction of total length of linestring the point has to be located.

    see https://postgis.net/docs/ST_3DLineInterpolatePoint.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_LineInterpolatePoints(GenericFunction):
    """
    Returns one or more points interpolated along a line.

    see https://postgis.net/docs/ST_LineInterpolatePoints.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_LineLocatePoint(GenericFunction):
    """
    Returns a float between 0 and 1 representing the location of the closest point on LineString to the
    given Point, as a fraction of total 2d line length.

    see https://postgis.net/docs/ST_LineLocatePoint.html
    """

class ST_LineSubstring(GenericFunction):
    """
    Return a linestring being a substring of the input one starting and ending at the given fractions of
    total 2d length. Second and third arguments are float8 values between 0 and 1.

    see https://postgis.net/docs/ST_LineSubstring.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_LocateAlong(GenericFunction):
    """
    Return a derived geometry collection value with elements that match the specified measure. Polygonal
    elements are not supported.

    see https://postgis.net/docs/ST_LocateAlong.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_LocateBetween(GenericFunction):
    """
    Return a derived geometry collection value with elements that match the specified range of measures
    inclusively.

    see https://postgis.net/docs/ST_LocateBetween.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_LocateBetweenElevations(GenericFunction):
    """
    Return a derived geometry (collection) value with elements that intersect the specified range of
    elevations inclusively.

    see https://postgis.net/docs/ST_LocateBetweenElevations.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_InterpolatePoint(GenericFunction):
    """
    Return the value of the measure dimension of a geometry at the point closed to the provided point.

    see https://postgis.net/docs/ST_InterpolatePoint.html
    """

class ST_AddMeasure(GenericFunction):
    """
    Return a derived geometry with measure elements linearly interpolated between the start and end
    points.

    see https://postgis.net/docs/ST_AddMeasure.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_IsValidTrajectory(GenericFunction):
    """
    Returns true if the geometry is a valid trajectory.

    see https://postgis.net/docs/ST_IsValidTrajectory.html
    """

class ST_ClosestPointOfApproach(GenericFunction):
    """
    Returns the measure at which points interpolated along two trajectories are closest.

    see https://postgis.net/docs/ST_ClosestPointOfApproach.html
    """

class ST_DistanceCPA(GenericFunction):
    """
    Returns the distance between the closest point of approach of two trajectories.

    see https://postgis.net/docs/ST_DistanceCPA.html
    """

class ST_CPAWithin(GenericFunction):
    """
    Returns true if the closest point of approach of two trajectories is within the specified distance.

    see https://postgis.net/docs/ST_CPAWithin.html
    """

class postgis_sfcgal_version(GenericFunction):
    """
    Returns the version of SFCGAL in use

    see https://postgis.net/docs/postgis_sfcgal_version.html
    """

class ST_Extrude(GenericFunction):
    """
    Extrude a surface to a related volume

    see https://postgis.net/docs/ST_Extrude.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_StraightSkeleton(GenericFunction):
    """
    Compute a straight skeleton from a geometry

    see https://postgis.net/docs/ST_StraightSkeleton.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ApproximateMedialAxis(GenericFunction):
    """
    Compute the approximate medial axis of an areal geometry.

    see https://postgis.net/docs/ST_ApproximateMedialAxis.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_IsPlanar(GenericFunction):
    """
    Check if a surface is or not planar

    see https://postgis.net/docs/ST_IsPlanar.html
    """

class ST_Orientation(GenericFunction):
    """
    Determine surface orientation

    see https://postgis.net/docs/ST_Orientation.html
    """

class ST_ForceLHR(GenericFunction):
    """
    Force LHR orientation

    see https://postgis.net/docs/ST_ForceLHR.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_MinkowskiSum(GenericFunction):
    """
    Performs Minkowski sum

    see https://postgis.net/docs/ST_MinkowskiSum.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_ConstrainedDelaunayTriangles(GenericFunction):
    """
    Return a constrained Delaunay triangulation around the given input geometry.

    see https://postgis.net/docs/ST_ConstrainedDelaunayTriangles.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_3DIntersection(GenericFunction):
    """
    Perform 3D intersection

    see https://postgis.net/docs/ST_3DIntersection.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_3DDifference(GenericFunction):
    """
    Perform 3D difference

    see https://postgis.net/docs/ST_3DDifference.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_3DUnion(GenericFunction):
    """
    Perform 3D union

    see https://postgis.net/docs/ST_3DUnion.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_3DArea(GenericFunction):
    """
    Computes area of 3D surface geometries. Will return 0 for solids.

    see https://postgis.net/docs/ST_3DArea.html
    """

class ST_Tesselate(GenericFunction):
    """
    Perform surface Tessellation of a polygon or polyhedralsurface and returns as a TIN or collection of
    TINS

    see https://postgis.net/docs/ST_Tesselate.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_Volume(GenericFunction):
    """
    Computes the volume of a 3D solid. If applied to surface (even closed) geometries will return 0.

    see https://postgis.net/docs/ST_Volume.html
    """

class ST_MakeSolid(GenericFunction):
    """
    Cast the geometry into a solid. No check is performed. To obtain a valid solid, the input geometry
    must be a closed Polyhedral Surface or a closed TIN.

    see https://postgis.net/docs/ST_MakeSolid.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_IsSolid(GenericFunction):
    """
    Test if the geometry is a solid. No validity check is performed.

    see https://postgis.net/docs/ST_IsSolid.html
    """

class AddAuth(GenericFunction):
    """
    Adds an authorization token to be used in the current transaction.

    see https://postgis.net/docs/AddAuth.html
    """

class CheckAuth(GenericFunction):
    """
    Creates a trigger on a table to prevent/allow updates and deletes of rows based on authorization
    token.

    see https://postgis.net/docs/CheckAuth.html
    """

class DisableLongTransactions(GenericFunction):
    """
    Disables long transaction support.

    see https://postgis.net/docs/DisableLongTransactions.html
    """

class EnableLongTransactions(GenericFunction):
    """
    Enables long transaction support.

    see https://postgis.net/docs/EnableLongTransactions.html
    """

class LockRow(GenericFunction):
    """
    Sets lock/authorization for a row in a table.

    see https://postgis.net/docs/LockRow.html
    """

class UnlockRows(GenericFunction):
    """
    Removes all locks held by an authorization token.

    see https://postgis.net/docs/UnlockRows.html
    """

class PostGIS_Extensions_Upgrade(GenericFunction):
    """
    Packages and upgrades postgis extensions (e.g. postgis_raster, postgis_topology, postgis_sfcgal) to
    latest available version.

    see https://postgis.net/docs/PostGIS_Extensions_Upgrade.html
    """

class PostGIS_Full_Version(GenericFunction):
    """
    Reports full postgis version and build configuration infos.

    see https://postgis.net/docs/PostGIS_Full_Version.html
    """

class PostGIS_GEOS_Version(GenericFunction):
    """
    Returns the version number of the GEOS library.

    see https://postgis.net/docs/PostGIS_GEOS_Version.html
    """

class PostGIS_Liblwgeom_Version(GenericFunction):
    """
    Returns the version number of the liblwgeom library. This should match the version of PostGIS.

    see https://postgis.net/docs/PostGIS_Liblwgeom_Version.html
    """

class PostGIS_LibXML_Version(GenericFunction):
    """
    Returns the version number of the libxml2 library.

    see https://postgis.net/docs/PostGIS_LibXML_Version.html
    """

class PostGIS_Lib_Build_Date(GenericFunction):
    """
    Returns build date of the PostGIS library.

    see https://postgis.net/docs/PostGIS_Lib_Build_Date.html
    """

class PostGIS_Lib_Version(GenericFunction):
    """
    Returns the version number of the PostGIS library.

    see https://postgis.net/docs/PostGIS_Lib_Version.html
    """

class PostGIS_PROJ_Version(GenericFunction):
    """
    Returns the version number of the PROJ4 library.

    see https://postgis.net/docs/PostGIS_PROJ_Version.html
    """

class PostGIS_Wagyu_Version(GenericFunction):
    """
    Returns the version number of the internal Wagyu library.

    see https://postgis.net/docs/PostGIS_Wagyu_Version.html
    """

class PostGIS_Scripts_Build_Date(GenericFunction):
    """
    Returns build date of the PostGIS scripts.

    see https://postgis.net/docs/PostGIS_Scripts_Build_Date.html
    """

class PostGIS_Scripts_Installed(GenericFunction):
    """
    Returns version of the postgis scripts installed in this database.

    see https://postgis.net/docs/PostGIS_Scripts_Installed.html
    """

class PostGIS_Scripts_Released(GenericFunction):
    """
    Returns the version number of the postgis.sql script released with the installed postgis lib.

    see https://postgis.net/docs/PostGIS_Scripts_Released.html
    """

class PostGIS_Version(GenericFunction):
    """
    Returns PostGIS version number and compile-time options.

    see https://postgis.net/docs/PostGIS_Version.html
    """

class PostGIS_AddBBox(GenericFunction):
    """
    Add bounding box to the geometry.

    see https://postgis.net/docs/PostGIS_AddBBox.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class PostGIS_DropBBox(GenericFunction):
    """
    Drop the bounding box cache from the geometry.

    see https://postgis.net/docs/PostGIS_DropBBox.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class PostGIS_HasBBox(GenericFunction):
    """
    Returns TRUE if the bbox of this geometry is cached, FALSE otherwise.

    see https://postgis.net/docs/PostGIS_HasBBox.html
    """

class ST_AddBand(GenericFunction):
    """
    Returns a raster with the new band(s) of given type added with given initial value in the given
    index location. If no index is specified, the band is added to the end.

    see https://postgis.net/docs/RT_ST_AddBand.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_AsRaster(GenericFunction):
    """
    Converts a PostGIS geometry to a PostGIS raster.

    see https://postgis.net/docs/RT_ST_AsRaster.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_Band(GenericFunction):
    """
    Returns one or more bands of an existing raster as a new raster. Useful for building new rasters
    from existing rasters.

    see https://postgis.net/docs/RT_ST_Band.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_MakeEmptyCoverage(GenericFunction):
    """
    Cover georeferenced area with a grid of empty raster tiles.

    see https://postgis.net/docs/RT_ST_MakeEmptyCoverage.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_MakeEmptyRaster(GenericFunction):
    """
    Returns an empty raster (having no bands) of given dimensions (width & height), upperleft X and Y,
    pixel size and rotation (scalex, scaley, skewx & skewy) and reference system (srid). If a raster is
    passed in, returns a new raster with the same size, alignment and SRID. If srid is left out, the
    spatial ref is set to unknown (0).

    see https://postgis.net/docs/RT_ST_MakeEmptyRaster.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_Tile(GenericFunction):
    """
    Returns a set of rasters resulting from the split of the input raster based upon the desired
    dimensions of the output rasters.

    see https://postgis.net/docs/RT_ST_Tile.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_Retile(GenericFunction):
    """
    Return a set of configured tiles from an arbitrarily tiled raster coverage.

    see https://postgis.net/docs/RT_ST_Retile.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_FromGDALRaster(GenericFunction):
    """
    Returns a raster from a supported GDAL raster file.

    see https://postgis.net/docs/RT_ST_FromGDALRaster.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_GeoReference(GenericFunction):
    """
    Returns the georeference meta data in GDAL or ESRI format as commonly seen in a world file. Default
    is GDAL.

    see https://postgis.net/docs/RT_ST_GeoReference.html
    """

class ST_Height(GenericFunction):
    """
    Returns the height of the raster in pixels.

    see https://postgis.net/docs/RT_ST_Height.html
    """

class ST_MetaData(GenericFunction):
    """
    Returns basic meta data about a raster object such as pixel size, rotation (skew), upper, lower
    left, etc.

    see https://postgis.net/docs/RT_ST_MetaData.html
    """

class ST_NumBands(GenericFunction):
    """
    Returns the number of bands in the raster object.

    see https://postgis.net/docs/RT_ST_NumBands.html
    """

class ST_PixelHeight(GenericFunction):
    """
    Returns the pixel height in geometric units of the spatial reference system.

    see https://postgis.net/docs/RT_ST_PixelHeight.html
    """

class ST_PixelWidth(GenericFunction):
    """
    Returns the pixel width in geometric units of the spatial reference system.

    see https://postgis.net/docs/RT_ST_PixelWidth.html
    """

class ST_ScaleX(GenericFunction):
    """
    Returns the X component of the pixel width in units of coordinate reference system.

    see https://postgis.net/docs/RT_ST_ScaleX.html
    """

class ST_ScaleY(GenericFunction):
    """
    Returns the Y component of the pixel height in units of coordinate reference system.

    see https://postgis.net/docs/RT_ST_ScaleY.html
    """

class ST_RasterToWorldCoord(GenericFunction):
    """
    Returns the raster's upper left corner as geometric X and Y (longitude and latitude) given a column
    and row. Column and row starts at 1.

    see https://postgis.net/docs/RT_ST_RasterToWorldCoord.html
    """

class ST_RasterToWorldCoordX(GenericFunction):
    """
    Returns the geometric X coordinate upper left of a raster, column and row. Numbering of columns and
    rows starts at 1.

    see https://postgis.net/docs/RT_ST_RasterToWorldCoordX.html
    """

class ST_RasterToWorldCoordY(GenericFunction):
    """
    Returns the geometric Y coordinate upper left corner of a raster, column and row. Numbering of
    columns and rows starts at 1.

    see https://postgis.net/docs/RT_ST_RasterToWorldCoordY.html
    """

class ST_Rotation(GenericFunction):
    """
    Returns the rotation of the raster in radian.

    see https://postgis.net/docs/RT_ST_Rotation.html
    """

class ST_SkewX(GenericFunction):
    """
    Returns the georeference X skew (or rotation parameter).

    see https://postgis.net/docs/RT_ST_SkewX.html
    """

class ST_SkewY(GenericFunction):
    """
    Returns the georeference Y skew (or rotation parameter).

    see https://postgis.net/docs/RT_ST_SkewY.html
    """

class ST_UpperLeftX(GenericFunction):
    """
    Returns the upper left X coordinate of raster in projected spatial ref.

    see https://postgis.net/docs/RT_ST_UpperLeftX.html
    """

class ST_UpperLeftY(GenericFunction):
    """
    Returns the upper left Y coordinate of raster in projected spatial ref.

    see https://postgis.net/docs/RT_ST_UpperLeftY.html
    """

class ST_Width(GenericFunction):
    """
    Returns the width of the raster in pixels.

    see https://postgis.net/docs/RT_ST_Width.html
    """

class ST_WorldToRasterCoord(GenericFunction):
    """
    Returns the upper left corner as column and row given geometric X and Y (longitude and latitude) or
    a point geometry expressed in the spatial reference coordinate system of the raster.

    see https://postgis.net/docs/RT_ST_WorldToRasterCoord.html
    """

class ST_WorldToRasterCoordX(GenericFunction):
    """
    Returns the column in the raster of the point geometry (pt) or a X and Y world coordinate (xw, yw)
    represented in world spatial reference system of raster.

    see https://postgis.net/docs/RT_ST_WorldToRasterCoordX.html
    """

class ST_WorldToRasterCoordY(GenericFunction):
    """
    Returns the row in the raster of the point geometry (pt) or a X and Y world coordinate (xw, yw)
    represented in world spatial reference system of raster.

    see https://postgis.net/docs/RT_ST_WorldToRasterCoordY.html
    """

class ST_BandMetaData(GenericFunction):
    """
    Returns basic meta data for a specific raster band. band num 1 is assumed if none-specified.

    see https://postgis.net/docs/RT_ST_BandMetaData.html
    """

class ST_BandNoDataValue(GenericFunction):
    """
    Returns the value in a given band that represents no data. If no band num 1 is assumed.

    see https://postgis.net/docs/RT_ST_BandNoDataValue.html
    """

class ST_BandIsNoData(GenericFunction):
    """
    Returns true if the band is filled with only nodata values.

    see https://postgis.net/docs/RT_ST_BandIsNoData.html
    """

class ST_BandPath(GenericFunction):
    """
    Returns system file path to a band stored in file system. If no bandnum specified, 1 is assumed.

    see https://postgis.net/docs/RT_ST_BandPath.html
    """

class ST_BandFileSize(GenericFunction):
    """
    Returns the file size of a band stored in file system. If no bandnum specified, 1 is assumed.

    see https://postgis.net/docs/RT_ST_BandFileSize.html
    """

class ST_BandFileTimestamp(GenericFunction):
    """
    Returns the file timestamp of a band stored in file system. If no bandnum specified, 1 is assumed.

    see https://postgis.net/docs/RT_ST_BandFileTimestamp.html
    """

class ST_BandPixelType(GenericFunction):
    """
    Returns the type of pixel for given band. If no bandnum specified, 1 is assumed.

    see https://postgis.net/docs/RT_ST_BandPixelType.html
    """

class ST_MinPossibleValue(GenericFunction):
    """
    Returns the minimum value this pixeltype can store.

    see https://postgis.net/docs/ST_MinPossibleValue.html
    """

class ST_HasNoBand(GenericFunction):
    """
    Returns true if there is no band with given band number. If no band number is specified, then band
    number 1 is assumed.

    see https://postgis.net/docs/RT_ST_HasNoBand.html
    """

class ST_PixelAsPolygon(GenericFunction):
    """
    Returns the polygon geometry that bounds the pixel for a particular row and column.

    see https://postgis.net/docs/RT_ST_PixelAsPolygon.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_PixelAsPolygons(GenericFunction):
    """
    Returns the polygon geometry that bounds every pixel of a raster band along with the value, the X
    and the Y raster coordinates of each pixel.

    see https://postgis.net/docs/RT_ST_PixelAsPolygons.html
    """

class ST_PixelAsPoint(GenericFunction):
    """
    Returns a point geometry of the pixel's upper-left corner.

    see https://postgis.net/docs/RT_ST_PixelAsPoint.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_PixelAsPoints(GenericFunction):
    """
    Returns a point geometry for each pixel of a raster band along with the value, the X and the Y
    raster coordinates of each pixel. The coordinates of the point geometry are of the pixel's upper-
    left corner.

    see https://postgis.net/docs/RT_ST_PixelAsPoints.html
    """

class ST_PixelAsCentroid(GenericFunction):
    """
    Returns the centroid (point geometry) of the area represented by a pixel.

    see https://postgis.net/docs/RT_ST_PixelAsCentroid.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_PixelAsCentroids(GenericFunction):
    """
    Returns the centroid (point geometry) for each pixel of a raster band along with the value, the X
    and the Y raster coordinates of each pixel. The point geometry is the centroid of the area
    represented by a pixel.

    see https://postgis.net/docs/RT_ST_PixelAsCentroids.html
    """

class ST_Value(GenericFunction):
    """
    Returns the value of a given band in a given columnx, rowy pixel or at a particular geometric point.
    Band numbers start at 1 and assumed to be 1 if not specified. If exclude_nodata_value is set to
    false, then all pixels include nodata pixels are considered to intersect and return value. If
    exclude_nodata_value is not passed in then reads it from metadata of raster.

    see https://postgis.net/docs/RT_ST_Value.html
    """

class ST_NearestValue(GenericFunction):
    """
    Returns the nearest non-NODATA value of a given band's pixel specified by a columnx and rowy or a
    geometric point expressed in the same spatial reference coordinate system as the raster.

    see https://postgis.net/docs/RT_ST_NearestValue.html
    """

class ST_Neighborhood(GenericFunction):
    """
    Returns a 2-D double precision array of the non-NODATA values around a given band's pixel specified
    by either a columnX and rowY or a geometric point expressed in the same spatial reference coordinate
    system as the raster.

    see https://postgis.net/docs/RT_ST_Neighborhood.html
    """

class ST_SetValue(GenericFunction):
    """
    Returns modified raster resulting from setting the value of a given band in a given columnx, rowy
    pixel or the pixels that intersect a particular geometry. Band numbers start at 1 and assumed to be
    1 if not specified.

    see https://postgis.net/docs/RT_ST_SetValue.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_SetValues(GenericFunction):
    """
    Returns modified raster resulting from setting the values of a given band.

    see https://postgis.net/docs/RT_ST_SetValues.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_DumpValues(GenericFunction):
    """
    Get the values of the specified band as a 2-dimension array.

    see https://postgis.net/docs/RT_ST_DumpValues.html
    """

class ST_PixelOfValue(GenericFunction):
    """
    Get the columnx, rowy coordinates of the pixel whose value equals the search value.

    see https://postgis.net/docs/RT_ST_PixelOfValue.html
    """

class ST_SetGeoReference(GenericFunction):
    """
    Set Georeference 6 georeference parameters in a single call. Numbers should be separated by white
    space. Accepts inputs in GDAL or ESRI format. Default is GDAL.

    see https://postgis.net/docs/RT_ST_SetGeoReference.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_SetRotation(GenericFunction):
    """
    Set the rotation of the raster in radian.

    see https://postgis.net/docs/RT_ST_SetRotation.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_SetScale(GenericFunction):
    """
    Sets the X and Y size of pixels in units of coordinate reference system. Number units/pixel
    width/height.

    see https://postgis.net/docs/RT_ST_SetScale.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_SetSkew(GenericFunction):
    """
    Sets the georeference X and Y skew (or rotation parameter). If only one is passed in, sets X and Y
    to the same value.

    see https://postgis.net/docs/RT_ST_SetSkew.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_SetUpperLeft(GenericFunction):
    """
    Sets the value of the upper left corner of the pixel of the raster to projected X and Y coordinates.

    see https://postgis.net/docs/RT_ST_SetUpperLeft.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_Resample(GenericFunction):
    """
    Resample a raster using a specified resampling algorithm, new dimensions, an arbitrary grid corner
    and a set of raster georeferencing attributes defined or borrowed from another raster.

    see https://postgis.net/docs/RT_ST_Resample.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_Rescale(GenericFunction):
    """
    Resample a raster by adjusting only its scale (or pixel size). New pixel values are computed using
    the NearestNeighbor (english or american spelling), Bilinear, Cubic, CubicSpline or Lanczos
    resampling algorithm. Default is NearestNeighbor.

    see https://postgis.net/docs/RT_ST_Rescale.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_Reskew(GenericFunction):
    """
    Resample a raster by adjusting only its skew (or rotation parameters). New pixel values are computed
    using the NearestNeighbor (english or american spelling), Bilinear, Cubic, CubicSpline or Lanczos
    resampling algorithm. Default is NearestNeighbor.

    see https://postgis.net/docs/RT_ST_Reskew.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_Resize(GenericFunction):
    """
    Resize a raster to a new width/height

    see https://postgis.net/docs/RT_ST_Resize.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_SetBandNoDataValue(GenericFunction):
    """
    Sets the value for the given band that represents no data. Band 1 is assumed if no band is
    specified. To mark a band as having no nodata value, set the nodata value = NULL.

    see https://postgis.net/docs/RT_ST_SetBandNoDataValue.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_SetBandIsNoData(GenericFunction):
    """
    Sets the isnodata flag of the band to TRUE.

    see https://postgis.net/docs/RT_ST_SetBandIsNoData.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_SetBandPath(GenericFunction):
    """
    Update the external path and band number of an out-db band

    see https://postgis.net/docs/RT_ST_SetBandPath.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_SetBandIndex(GenericFunction):
    """
    Update the external band number of an out-db band

    see https://postgis.net/docs/RT_ST_SetBandIndex.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_Count(GenericFunction):
    """
    Returns the number of pixels in a given band of a raster or raster coverage. If no band is specified
    defaults to band 1. If exclude_nodata_value is set to true, will only count pixels that are not
    equal to the nodata value.

    see https://postgis.net/docs/RT_ST_Count.html
    """

class ST_CountAgg(GenericFunction):
    """
    Aggregate. Returns the number of pixels in a given band of a set of rasters. If no band is specified
    defaults to band 1. If exclude_nodata_value is set to true, will only count pixels that are not
    equal to the NODATA value.

    see https://postgis.net/docs/RT_ST_CountAgg.html
    """

class ST_Histogram(GenericFunction):
    """
    Returns a set of record summarizing a raster or raster coverage data distribution separate bin
    ranges. Number of bins are autocomputed if not specified.

    see https://postgis.net/docs/RT_ST_Histogram.html
    """

class ST_Quantile(GenericFunction):
    """
    Compute quantiles for a raster or raster table coverage in the context of the sample or population.
    Thus, a value could be examined to be at the raster's 25%, 50%, 75% percentile.

    see https://postgis.net/docs/RT_ST_Quantile.html
    """

class ST_SummaryStats(GenericFunction):
    """
    Returns summarystats consisting of count, sum, mean, stddev, min, max for a given raster band of a
    raster or raster coverage. Band 1 is assumed is no band is specified.

    see https://postgis.net/docs/RT_ST_SummaryStats.html
    """

class ST_SummaryStatsAgg(GenericFunction):
    """
    Aggregate. Returns summarystats consisting of count, sum, mean, stddev, min, max for a given raster
    band of a set of raster. Band 1 is assumed is no band is specified.

    see https://postgis.net/docs/RT_ST_SummaryStatsAgg.html

    Return type: :class:`geoalchemy2.types.SummaryStats`.
    """

    type = geoalchemy2.types.SummaryStats()

class ST_ValueCount(GenericFunction):
    """
    Returns a set of records containing a pixel band value and count of the number of pixels in a given
    band of a raster (or a raster coverage) that have a given set of values. If no band is specified
    defaults to band 1. By default nodata value pixels are not counted. and all other values in the
    pixel are output and pixel band values are rounded to the nearest integer.

    see https://postgis.net/docs/RT_ST_ValueCount.html
    """

class ST_RastFromWKB(GenericFunction):
    """
    Return a raster value from a Well-Known Binary (WKB) raster.

    see https://postgis.net/docs/RT_ST_RastFromWKB.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_RastFromHexWKB(GenericFunction):
    """
    Return a raster value from a Hex representation of Well-Known Binary (WKB) raster.

    see https://postgis.net/docs/RT_ST_RastFromHexWKB.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_AsWKB(GenericFunction):
    """
    Return the Well-Known Binary (WKB) representation of the raster.

    see https://postgis.net/docs/RT_ST_AsBinary.html
    """

class ST_AsHexWKB(GenericFunction):
    """
    Return the Well-Known Binary (WKB) in Hex representation of the raster.

    see https://postgis.net/docs/RT_ST_AsHexWKB.html
    """

class ST_AsGDALRaster(GenericFunction):
    """
    Return the raster tile in the designated GDAL Raster format. Raster formats are one of those
    supported by your compiled library. Use ST_GDALDrivers() to get a list of formats supported by your
    library.

    see https://postgis.net/docs/RT_ST_AsGDALRaster.html
    """

class ST_AsJPEG(GenericFunction):
    """
    Return the raster tile selected bands as a single Joint Photographic Exports Group (JPEG) image
    (byte array). If no band is specified and 1 or more than 3 bands, then only the first band is used.
    If only 3 bands then all 3 bands are used and mapped to RGB.

    see https://postgis.net/docs/RT_ST_AsJPEG.html
    """

class ST_AsPNG(GenericFunction):
    """
    Return the raster tile selected bands as a single portable network graphics (PNG) image (byte
    array). If 1, 3, or 4 bands in raster and no bands are specified, then all bands are used. If more 2
    or more than 4 bands and no bands specified, then only band 1 is used. Bands are mapped to RGB or
    RGBA space.

    see https://postgis.net/docs/RT_ST_AsPNG.html
    """

class ST_AsTIFF(GenericFunction):
    """
    Return the raster selected bands as a single TIFF image (byte array). If no band is specified or any
    of specified bands does not exist in the raster, then will try to use all bands.

    see https://postgis.net/docs/RT_ST_AsTIFF.html
    """

class ST_Clip(GenericFunction):
    """
    Returns the raster clipped by the input geometry. If band number not is specified, all bands are
    processed. If crop is not specified or TRUE, the output raster is cropped.

    see https://postgis.net/docs/RT_ST_Clip.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_ColorMap(GenericFunction):
    """
    Creates a new raster of up to four 8BUI bands (grayscale, RGB, RGBA) from the source raster and a
    specified band. Band 1 is assumed if not specified.

    see https://postgis.net/docs/RT_ST_ColorMap.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_Grayscale(GenericFunction):
    """
    Creates a new one-8BUI band raster from the source raster and specified bands representing Red,
    Green and Blue

    see https://postgis.net/docs/RT_ST_Grayscale.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_MapAlgebra(GenericFunction):
    """
    [raster] Callback function version - Returns a one-band raster given one or more input rasters, band
    indexes and one user-specified callback function.
    OR
    [raster] Expression version - Returns a one-band raster given one or two input rasters, band indexes
    and one or more user-specified SQL expressions.

    see https://postgis.net/docs/RT_ST_MapAlgebra.html
    """

class ST_MapAlgebraExpr(GenericFunction):
    """
    [raster] 1 raster band version: Creates a new one band raster formed by applying a valid PostgreSQL
    algebraic operation on the input raster band and of pixeltype provided. Band 1 is assumed if no band
    is specified.
    OR
    [raster] 2 raster band version: Creates a new one band raster formed by applying a valid PostgreSQL
    algebraic operation on the two input raster bands and of pixeltype provided. band 1 of each raster
    is assumed if no band numbers are specified. The resulting raster will be aligned (scale, skew and
    pixel corners) on the grid defined by the first raster and have its extent defined by the
    "extenttype" parameter. Values for "extenttype" can be: INTERSECTION, UNION, FIRST, SECOND.

    see https://postgis.net/docs/RT_ST_MapAlgebraExpr.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_MapAlgebraFct(GenericFunction):
    """
    [raster] 1 band version - Creates a new one band raster formed by applying a valid PostgreSQL
    function on the input raster band and of pixeltype provided. Band 1 is assumed if no band is
    specified.
    OR
    [raster] 2 band version - Creates a new one band raster formed by applying a valid PostgreSQL
    function on the 2 input raster bands and of pixeltype provided. Band 1 is assumed if no band is
    specified. Extent type defaults to INTERSECTION if not specified.

    see https://postgis.net/docs/RT_ST_MapAlgebraFct.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_MapAlgebraFctNgb(GenericFunction):
    """
    1-band version: Map Algebra Nearest Neighbor using user-defined PostgreSQL function. Return a raster
    which values are the result of a PLPGSQL user function involving a neighborhood of values from the
    input raster band.

    see https://postgis.net/docs/RT_ST_MapAlgebraFctNgb.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_Reclass(GenericFunction):
    """
    Creates a new raster composed of band types reclassified from original. The nband is the band to be
    changed. If nband is not specified assumed to be 1. All other bands are returned unchanged. Use
    case: convert a 16BUI band to a 8BUI and so forth for simpler rendering as viewable formats.

    see https://postgis.net/docs/RT_ST_Reclass.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_Distinct4ma(GenericFunction):
    """
    Raster processing function that calculates the number of unique pixel values in a neighborhood.

    see https://postgis.net/docs/RT_ST_Distinct4ma.html
    """

class ST_InvDistWeight4ma(GenericFunction):
    """
    Raster processing function that interpolates a pixel's value from the pixel's neighborhood.

    see https://postgis.net/docs/RT_ST_InvDistWeight4ma.html
    """

class ST_Max4ma(GenericFunction):
    """
    Raster processing function that calculates the maximum pixel value in a neighborhood.

    see https://postgis.net/docs/RT_ST_Max4ma.html
    """

class ST_Mean4ma(GenericFunction):
    """
    Raster processing function that calculates the mean pixel value in a neighborhood.

    see https://postgis.net/docs/RT_ST_Mean4ma.html
    """

class ST_Min4ma(GenericFunction):
    """
    Raster processing function that calculates the minimum pixel value in a neighborhood.

    see https://postgis.net/docs/RT_ST_Min4ma.html
    """

class ST_MinDist4ma(GenericFunction):
    """
    Raster processing function that returns the minimum distance (in number of pixels) between the pixel
    of interest and a neighboring pixel with value.

    see https://postgis.net/docs/RT_ST_MinDist4ma.html
    """

class ST_Range4ma(GenericFunction):
    """
    Raster processing function that calculates the range of pixel values in a neighborhood.

    see https://postgis.net/docs/RT_ST_Range4ma.html
    """

class ST_StdDev4ma(GenericFunction):
    """
    Raster processing function that calculates the standard deviation of pixel values in a neighborhood.

    see https://postgis.net/docs/RT_ST_StdDev4ma.html
    """

class ST_Sum4ma(GenericFunction):
    """
    Raster processing function that calculates the sum of all pixel values in a neighborhood.

    see https://postgis.net/docs/RT_ST_Sum4ma.html
    """

class ST_Aspect(GenericFunction):
    """
    Returns the aspect (in degrees by default) of an elevation raster band. Useful for analyzing
    terrain.

    see https://postgis.net/docs/RT_ST_Aspect.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_HillShade(GenericFunction):
    """
    Returns the hypothetical illumination of an elevation raster band using provided azimuth, altitude,
    brightness and scale inputs.

    see https://postgis.net/docs/RT_ST_HillShade.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_Roughness(GenericFunction):
    """
    Returns a raster with the calculated "roughness" of a DEM.

    see https://postgis.net/docs/RT_ST_Roughness.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_Slope(GenericFunction):
    """
    Returns the slope (in degrees by default) of an elevation raster band. Useful for analyzing terrain.

    see https://postgis.net/docs/RT_ST_Slope.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_TPI(GenericFunction):
    """
    Returns a raster with the calculated Topographic Position Index.

    see https://postgis.net/docs/RT_ST_TPI.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_TRI(GenericFunction):
    """
    Returns a raster with the calculated Terrain Ruggedness Index.

    see https://postgis.net/docs/RT_ST_TRI.html

    Return type: :class:`geoalchemy2.types.Raster`.
    """

    type = geoalchemy2.types.Raster()

class ST_DumpAsPolygons(GenericFunction):
    """
    Returns a set of geomval (geom,val) rows, from a given raster band. If no band number is specified,
    band num defaults to 1.

    see https://postgis.net/docs/RT_ST_DumpAsPolygons.html
    """

class ST_MinConvexHull(GenericFunction):
    """
    Return the convex hull geometry of the raster excluding NODATA pixels.

    see https://postgis.net/docs/RT_ST_MinConvexHull.html

    Return type: :class:`geoalchemy2.types.Geometry`.
    """

    type = geoalchemy2.types.Geometry()

class ST_SameAlignment(GenericFunction):
    """
    Returns true if rasters have same skew, scale, spatial ref, and offset (pixels can be put on same
    grid without cutting into pixels) and false if they don't with notice detailing issue.

    see https://postgis.net/docs/RT_ST_SameAlignment.html
    """

class ST_NotSameAlignmentReason(GenericFunction):
    """
    Returns text stating if rasters are aligned and if not aligned, a reason why.

    see https://postgis.net/docs/RT_ST_NotSameAlignmentReason.html
    """

class ST_Distance_Sphere(GenericFunction):
    """
    Returns minimum distance in meters between two lon/lat geometries. Uses a spherical earth and radius
    of 6370986 meters. Faster than ``ST_Distance_Spheroid``, but less accurate. PostGIS versions prior
    to 1.5 only implemented for points.

    see https://postgis.net/docs/ST_Distance_Sphere.html
    """
