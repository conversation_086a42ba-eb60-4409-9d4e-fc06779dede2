r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Frontline
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional
from twilio.base.version import Version
from twilio.base.domain import Domain
from twilio.rest.frontline_api.v1.user import UserList


class V1(Version):

    def __init__(self, domain: Domain):
        """
        Initialize the V1 version of FrontlineApi

        :param domain: The Twilio.frontline_api domain
        """
        super().__init__(domain, "v1")
        self._users: Optional[UserList] = None

    @property
    def users(self) -> UserList:
        if self._users is None:
            self._users = UserList(self)
        return self._users

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.FrontlineApi.V1>"
