r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Api
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.api.v2010.account.recording.add_on_result.payload.data import DataList


class PayloadInstance(InstanceResource):
    """
    :ivar sid: The unique string that that we created to identify the Recording AddOnResult Payload resource.
    :ivar add_on_result_sid: The SID of the AddOnResult to which the payload belongs.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Recording AddOnResult Payload resource.
    :ivar label: The string provided by the vendor that describes the payload.
    :ivar add_on_sid: The SID of the Add-on to which the result belongs.
    :ivar add_on_configuration_sid: The SID of the Add-on configuration.
    :ivar content_type: The MIME type of the payload.
    :ivar date_created: The date and time in GMT that the resource was created specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar date_updated: The date and time in GMT that the resource was last updated specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar reference_sid: The SID of the recording to which the AddOnResult resource that contains the payload belongs.
    :ivar subresource_uris: A list of related resources identified by their relative URIs.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        account_sid: str,
        reference_sid: str,
        add_on_result_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.add_on_result_sid: Optional[str] = payload.get("add_on_result_sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.label: Optional[str] = payload.get("label")
        self.add_on_sid: Optional[str] = payload.get("add_on_sid")
        self.add_on_configuration_sid: Optional[str] = payload.get(
            "add_on_configuration_sid"
        )
        self.content_type: Optional[str] = payload.get("content_type")
        self.date_created: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_updated")
        )
        self.reference_sid: Optional[str] = payload.get("reference_sid")
        self.subresource_uris: Optional[Dict[str, object]] = payload.get(
            "subresource_uris"
        )

        self._solution = {
            "account_sid": account_sid,
            "reference_sid": reference_sid,
            "add_on_result_sid": add_on_result_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[PayloadContext] = None

    @property
    def _proxy(self) -> "PayloadContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: PayloadContext for this PayloadInstance
        """
        if self._context is None:
            self._context = PayloadContext(
                self._version,
                account_sid=self._solution["account_sid"],
                reference_sid=self._solution["reference_sid"],
                add_on_result_sid=self._solution["add_on_result_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the PayloadInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the PayloadInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "PayloadInstance":
        """
        Fetch the PayloadInstance


        :returns: The fetched PayloadInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "PayloadInstance":
        """
        Asynchronous coroutine to fetch the PayloadInstance


        :returns: The fetched PayloadInstance
        """
        return await self._proxy.fetch_async()

    @property
    def data(self) -> DataList:
        """
        Access the data
        """
        return self._proxy.data

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.PayloadInstance {}>".format(context)


class PayloadContext(InstanceContext):

    def __init__(
        self,
        version: Version,
        account_sid: str,
        reference_sid: str,
        add_on_result_sid: str,
        sid: str,
    ):
        """
        Initialize the PayloadContext

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Recording AddOnResult Payload resource to fetch.
        :param reference_sid: The SID of the recording to which the AddOnResult resource that contains the payload to fetch belongs.
        :param add_on_result_sid: The SID of the AddOnResult to which the payload to fetch belongs.
        :param sid: The Twilio-provided string that uniquely identifies the Recording AddOnResult Payload resource to fetch.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "reference_sid": reference_sid,
            "add_on_result_sid": add_on_result_sid,
            "sid": sid,
        }
        self._uri = "/Accounts/{account_sid}/Recordings/{reference_sid}/AddOnResults/{add_on_result_sid}/Payloads/{sid}.json".format(
            **self._solution
        )

        self._data: Optional[DataList] = None

    def delete(self) -> bool:
        """
        Deletes the PayloadInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the PayloadInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> PayloadInstance:
        """
        Fetch the PayloadInstance


        :returns: The fetched PayloadInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return PayloadInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            reference_sid=self._solution["reference_sid"],
            add_on_result_sid=self._solution["add_on_result_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> PayloadInstance:
        """
        Asynchronous coroutine to fetch the PayloadInstance


        :returns: The fetched PayloadInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return PayloadInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            reference_sid=self._solution["reference_sid"],
            add_on_result_sid=self._solution["add_on_result_sid"],
            sid=self._solution["sid"],
        )

    @property
    def data(self) -> DataList:
        """
        Access the data
        """
        if self._data is None:
            self._data = DataList(
                self._version,
                self._solution["account_sid"],
                self._solution["reference_sid"],
                self._solution["add_on_result_sid"],
                self._solution["sid"],
            )
        return self._data

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.PayloadContext {}>".format(context)


class PayloadPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> PayloadInstance:
        """
        Build an instance of PayloadInstance

        :param payload: Payload response from the API
        """
        return PayloadInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            reference_sid=self._solution["reference_sid"],
            add_on_result_sid=self._solution["add_on_result_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.PayloadPage>"


class PayloadList(ListResource):

    def __init__(
        self,
        version: Version,
        account_sid: str,
        reference_sid: str,
        add_on_result_sid: str,
    ):
        """
        Initialize the PayloadList

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Recording AddOnResult Payload resources to read.
        :param reference_sid: The SID of the recording to which the AddOnResult resource that contains the payloads to read belongs.
        :param add_on_result_sid: The SID of the AddOnResult to which the payloads to read belongs.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "reference_sid": reference_sid,
            "add_on_result_sid": add_on_result_sid,
        }
        self._uri = "/Accounts/{account_sid}/Recordings/{reference_sid}/AddOnResults/{add_on_result_sid}/Payloads.json".format(
            **self._solution
        )

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[PayloadInstance]:
        """
        Streams PayloadInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[PayloadInstance]:
        """
        Asynchronously streams PayloadInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[PayloadInstance]:
        """
        Lists PayloadInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[PayloadInstance]:
        """
        Asynchronously lists PayloadInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> PayloadPage:
        """
        Retrieve a single page of PayloadInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of PayloadInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return PayloadPage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> PayloadPage:
        """
        Asynchronously retrieve a single page of PayloadInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of PayloadInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return PayloadPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> PayloadPage:
        """
        Retrieve a specific page of PayloadInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of PayloadInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return PayloadPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> PayloadPage:
        """
        Asynchronously retrieve a specific page of PayloadInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of PayloadInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return PayloadPage(self._version, response, self._solution)

    def get(self, sid: str) -> PayloadContext:
        """
        Constructs a PayloadContext

        :param sid: The Twilio-provided string that uniquely identifies the Recording AddOnResult Payload resource to fetch.
        """
        return PayloadContext(
            self._version,
            account_sid=self._solution["account_sid"],
            reference_sid=self._solution["reference_sid"],
            add_on_result_sid=self._solution["add_on_result_sid"],
            sid=sid,
        )

    def __call__(self, sid: str) -> PayloadContext:
        """
        Constructs a PayloadContext

        :param sid: The Twilio-provided string that uniquely identifies the Recording AddOnResult Payload resource to fetch.
        """
        return PayloadContext(
            self._version,
            account_sid=self._solution["account_sid"],
            reference_sid=self._solution["reference_sid"],
            add_on_result_sid=self._solution["add_on_result_sid"],
            sid=sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.PayloadList>"
