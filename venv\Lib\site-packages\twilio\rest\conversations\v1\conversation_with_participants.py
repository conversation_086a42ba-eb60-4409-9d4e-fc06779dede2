r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Conversations
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from twilio.base import deserialize, serialize, values

from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class ConversationWithParticipantsInstance(InstanceResource):

    class State(object):
        INACTIVE = "inactive"
        ACTIVE = "active"
        CLOSED = "closed"

    class WebhookEnabledType(object):
        TRUE = "true"
        FALSE = "false"

    """
    :ivar account_sid: The unique ID of the [Account](https://www.twilio.com/docs/iam/api/account) responsible for this conversation.
    :ivar chat_service_sid: The unique ID of the [Conversation Service](https://www.twilio.com/docs/conversations/api/service-resource) this conversation belongs to.
    :ivar messaging_service_sid: The unique ID of the [Messaging Service](https://www.twilio.com/docs/messaging/api/service-resource) this conversation belongs to.
    :ivar sid: A 34 character string that uniquely identifies this resource.
    :ivar friendly_name: The human-readable name of this conversation, limited to 256 characters. Optional.
    :ivar unique_name: An application-defined string that uniquely identifies the resource. It can be used to address the resource in place of the resource's `sid` in the URL.
    :ivar attributes: An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \"{}\" will be returned.
    :ivar state: 
    :ivar date_created: The date that this resource was created.
    :ivar date_updated: The date that this resource was last updated.
    :ivar timers: Timer date values representing state update for this conversation.
    :ivar links: Contains absolute URLs to access the [participants](https://www.twilio.com/docs/conversations/api/conversation-participant-resource), [messages](https://www.twilio.com/docs/conversations/api/conversation-message-resource) and [webhooks](https://www.twilio.com/docs/conversations/api/conversation-scoped-webhook-resource) of this conversation.
    :ivar bindings: 
    :ivar url: An absolute API resource URL for this conversation.
    """

    def __init__(self, version: Version, payload: Dict[str, Any]):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.chat_service_sid: Optional[str] = payload.get("chat_service_sid")
        self.messaging_service_sid: Optional[str] = payload.get("messaging_service_sid")
        self.sid: Optional[str] = payload.get("sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.unique_name: Optional[str] = payload.get("unique_name")
        self.attributes: Optional[str] = payload.get("attributes")
        self.state: Optional["ConversationWithParticipantsInstance.State"] = (
            payload.get("state")
        )
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.timers: Optional[Dict[str, object]] = payload.get("timers")
        self.links: Optional[Dict[str, object]] = payload.get("links")
        self.bindings: Optional[Dict[str, object]] = payload.get("bindings")
        self.url: Optional[str] = payload.get("url")

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Conversations.V1.ConversationWithParticipantsInstance>"


class ConversationWithParticipantsList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the ConversationWithParticipantsList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/ConversationWithParticipants"

    def create(
        self,
        x_twilio_webhook_enabled: Union[
            "ConversationWithParticipantsInstance.WebhookEnabledType", object
        ] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        unique_name: Union[str, object] = values.unset,
        date_created: Union[datetime, object] = values.unset,
        date_updated: Union[datetime, object] = values.unset,
        messaging_service_sid: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        state: Union[
            "ConversationWithParticipantsInstance.State", object
        ] = values.unset,
        timers_inactive: Union[str, object] = values.unset,
        timers_closed: Union[str, object] = values.unset,
        bindings_email_address: Union[str, object] = values.unset,
        bindings_email_name: Union[str, object] = values.unset,
        participant: Union[List[str], object] = values.unset,
    ) -> ConversationWithParticipantsInstance:
        """
        Create the ConversationWithParticipantsInstance

        :param x_twilio_webhook_enabled: The X-Twilio-Webhook-Enabled HTTP request header
        :param friendly_name: The human-readable name of this conversation, limited to 256 characters. Optional.
        :param unique_name: An application-defined string that uniquely identifies the resource. It can be used to address the resource in place of the resource's `sid` in the URL.
        :param date_created: The date that this resource was created.
        :param date_updated: The date that this resource was last updated.
        :param messaging_service_sid: The unique ID of the [Messaging Service](https://www.twilio.com/docs/messaging/api/service-resource) this conversation belongs to.
        :param attributes: An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \\\"{}\\\" will be returned.
        :param state:
        :param timers_inactive: ISO8601 duration when conversation will be switched to `inactive` state. Minimum value for this timer is 1 minute.
        :param timers_closed: ISO8601 duration when conversation will be switched to `closed` state. Minimum value for this timer is 10 minutes.
        :param bindings_email_address: The default email address that will be used when sending outbound emails in this conversation.
        :param bindings_email_name: The default name that will be used when sending outbound emails in this conversation.
        :param participant: The participant to be added to the conversation in JSON format. The JSON object attributes are as parameters in [Participant Resource](https://www.twilio.com/docs/conversations/api/conversation-participant-resource). The maximum number of participants that can be added in a single request is 10.

        :returns: The created ConversationWithParticipantsInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "UniqueName": unique_name,
                "DateCreated": serialize.iso8601_datetime(date_created),
                "DateUpdated": serialize.iso8601_datetime(date_updated),
                "MessagingServiceSid": messaging_service_sid,
                "Attributes": attributes,
                "State": state,
                "Timers.Inactive": timers_inactive,
                "Timers.Closed": timers_closed,
                "Bindings.Email.Address": bindings_email_address,
                "Bindings.Email.Name": bindings_email_name,
                "Participant": serialize.map(participant, lambda e: e),
            }
        )
        headers = values.of(
            {
                "X-Twilio-Webhook-Enabled": x_twilio_webhook_enabled,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ConversationWithParticipantsInstance(self._version, payload)

    async def create_async(
        self,
        x_twilio_webhook_enabled: Union[
            "ConversationWithParticipantsInstance.WebhookEnabledType", object
        ] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        unique_name: Union[str, object] = values.unset,
        date_created: Union[datetime, object] = values.unset,
        date_updated: Union[datetime, object] = values.unset,
        messaging_service_sid: Union[str, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        state: Union[
            "ConversationWithParticipantsInstance.State", object
        ] = values.unset,
        timers_inactive: Union[str, object] = values.unset,
        timers_closed: Union[str, object] = values.unset,
        bindings_email_address: Union[str, object] = values.unset,
        bindings_email_name: Union[str, object] = values.unset,
        participant: Union[List[str], object] = values.unset,
    ) -> ConversationWithParticipantsInstance:
        """
        Asynchronously create the ConversationWithParticipantsInstance

        :param x_twilio_webhook_enabled: The X-Twilio-Webhook-Enabled HTTP request header
        :param friendly_name: The human-readable name of this conversation, limited to 256 characters. Optional.
        :param unique_name: An application-defined string that uniquely identifies the resource. It can be used to address the resource in place of the resource's `sid` in the URL.
        :param date_created: The date that this resource was created.
        :param date_updated: The date that this resource was last updated.
        :param messaging_service_sid: The unique ID of the [Messaging Service](https://www.twilio.com/docs/messaging/api/service-resource) this conversation belongs to.
        :param attributes: An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \\\"{}\\\" will be returned.
        :param state:
        :param timers_inactive: ISO8601 duration when conversation will be switched to `inactive` state. Minimum value for this timer is 1 minute.
        :param timers_closed: ISO8601 duration when conversation will be switched to `closed` state. Minimum value for this timer is 10 minutes.
        :param bindings_email_address: The default email address that will be used when sending outbound emails in this conversation.
        :param bindings_email_name: The default name that will be used when sending outbound emails in this conversation.
        :param participant: The participant to be added to the conversation in JSON format. The JSON object attributes are as parameters in [Participant Resource](https://www.twilio.com/docs/conversations/api/conversation-participant-resource). The maximum number of participants that can be added in a single request is 10.

        :returns: The created ConversationWithParticipantsInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "UniqueName": unique_name,
                "DateCreated": serialize.iso8601_datetime(date_created),
                "DateUpdated": serialize.iso8601_datetime(date_updated),
                "MessagingServiceSid": messaging_service_sid,
                "Attributes": attributes,
                "State": state,
                "Timers.Inactive": timers_inactive,
                "Timers.Closed": timers_closed,
                "Bindings.Email.Address": bindings_email_address,
                "Bindings.Email.Name": bindings_email_name,
                "Participant": serialize.map(participant, lambda e: e),
            }
        )
        headers = values.of(
            {
                "X-Twilio-Webhook-Enabled": x_twilio_webhook_enabled,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ConversationWithParticipantsInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Conversations.V1.ConversationWithParticipantsList>"
