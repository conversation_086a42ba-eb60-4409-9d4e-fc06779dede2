from fastapi import APIRouter, Depends, HTTPException, Form
from sqlalchemy.orm import Session
from datetime import datetime
from app.models.database import get_db
from app.models.feedback import Feedback

router = APIRouter()

@router.post("/contact-us")
async def submit_feedback(
    email: str = Form(...),
    title: str = Form(...),
    comment: str = Form(...),
    db: Session = Depends(get_db)
):
    """Endpoint to handle user feedback submissions"""
    try:
        feedback = Feedback(
            email=email,
            title=title,
            comment=comment,
            submitted_at=datetime.utcnow()
        )
        db.add(feedback)
        db.commit()
        return {"message": "Thank you for your feedback!"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to submit feedback.")
