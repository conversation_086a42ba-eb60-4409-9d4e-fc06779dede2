r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Flex
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class PluginReleaseInstance(InstanceResource):
    """
    :ivar sid: The unique string that we created to identify the Plugin Release resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Plugin Release resource and owns this resource.
    :ivar configuration_sid: The SID of the Plugin Configuration resource to release.
    :ivar date_created: The date and time in GMT when the Flex Plugin Release was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The absolute URL of the Plugin Release resource.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.configuration_sid: Optional[str] = payload.get("configuration_sid")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[PluginReleaseContext] = None

    @property
    def _proxy(self) -> "PluginReleaseContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: PluginReleaseContext for this PluginReleaseInstance
        """
        if self._context is None:
            self._context = PluginReleaseContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def fetch(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> "PluginReleaseInstance":
        """
        Fetch the PluginReleaseInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The fetched PluginReleaseInstance
        """
        return self._proxy.fetch(
            flex_metadata=flex_metadata,
        )

    async def fetch_async(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> "PluginReleaseInstance":
        """
        Asynchronous coroutine to fetch the PluginReleaseInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The fetched PluginReleaseInstance
        """
        return await self._proxy.fetch_async(
            flex_metadata=flex_metadata,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.PluginReleaseInstance {}>".format(context)


class PluginReleaseContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the PluginReleaseContext

        :param version: Version that contains the resource
        :param sid: The SID of the Flex Plugin Release resource to fetch.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/PluginService/Releases/{sid}".format(**self._solution)

    def fetch(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> PluginReleaseInstance:
        """
        Fetch the PluginReleaseInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The fetched PluginReleaseInstance
        """

        data = values.of(
            {
                "Flex-Metadata": flex_metadata,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return PluginReleaseInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> PluginReleaseInstance:
        """
        Asynchronous coroutine to fetch the PluginReleaseInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The fetched PluginReleaseInstance
        """

        data = values.of(
            {
                "Flex-Metadata": flex_metadata,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return PluginReleaseInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.PluginReleaseContext {}>".format(context)


class PluginReleasePage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> PluginReleaseInstance:
        """
        Build an instance of PluginReleaseInstance

        :param payload: Payload response from the API
        """
        return PluginReleaseInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V1.PluginReleasePage>"


class PluginReleaseList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the PluginReleaseList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/PluginService/Releases"

    def create(
        self, configuration_id: str, flex_metadata: Union[str, object] = values.unset
    ) -> PluginReleaseInstance:
        """
        Create the PluginReleaseInstance

        :param configuration_id: The SID or the Version of the Flex Plugin Configuration to release.
        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The created PluginReleaseInstance
        """

        data = values.of(
            {
                "ConfigurationId": configuration_id,
            }
        )
        headers = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return PluginReleaseInstance(self._version, payload)

    async def create_async(
        self, configuration_id: str, flex_metadata: Union[str, object] = values.unset
    ) -> PluginReleaseInstance:
        """
        Asynchronously create the PluginReleaseInstance

        :param configuration_id: The SID or the Version of the Flex Plugin Configuration to release.
        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The created PluginReleaseInstance
        """

        data = values.of(
            {
                "ConfigurationId": configuration_id,
            }
        )
        headers = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return PluginReleaseInstance(self._version, payload)

    def stream(
        self,
        flex_metadata: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[PluginReleaseInstance]:
        """
        Streams PluginReleaseInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str flex_metadata: The Flex-Metadata HTTP request header
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(flex_metadata=flex_metadata, page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        flex_metadata: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[PluginReleaseInstance]:
        """
        Asynchronously streams PluginReleaseInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str flex_metadata: The Flex-Metadata HTTP request header
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            flex_metadata=flex_metadata, page_size=limits["page_size"]
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        flex_metadata: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[PluginReleaseInstance]:
        """
        Lists PluginReleaseInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str flex_metadata: The Flex-Metadata HTTP request header
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                flex_metadata=flex_metadata,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        flex_metadata: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[PluginReleaseInstance]:
        """
        Asynchronously lists PluginReleaseInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str flex_metadata: The Flex-Metadata HTTP request header
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                flex_metadata=flex_metadata,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        flex_metadata: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> PluginReleasePage:
        """
        Retrieve a single page of PluginReleaseInstance records from the API.
        Request is executed immediately

        :param flex_metadata: The Flex-Metadata HTTP request header
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of PluginReleaseInstance
        """
        data = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return PluginReleasePage(self._version, response)

    async def page_async(
        self,
        flex_metadata: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> PluginReleasePage:
        """
        Asynchronously retrieve a single page of PluginReleaseInstance records from the API.
        Request is executed immediately

        :param flex_metadata: The Flex-Metadata HTTP request header
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of PluginReleaseInstance
        """
        data = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return PluginReleasePage(self._version, response)

    def get_page(self, target_url: str) -> PluginReleasePage:
        """
        Retrieve a specific page of PluginReleaseInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of PluginReleaseInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return PluginReleasePage(self._version, response)

    async def get_page_async(self, target_url: str) -> PluginReleasePage:
        """
        Asynchronously retrieve a specific page of PluginReleaseInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of PluginReleaseInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return PluginReleasePage(self._version, response)

    def get(self, sid: str) -> PluginReleaseContext:
        """
        Constructs a PluginReleaseContext

        :param sid: The SID of the Flex Plugin Release resource to fetch.
        """
        return PluginReleaseContext(self._version, sid=sid)

    def __call__(self, sid: str) -> PluginReleaseContext:
        """
        Constructs a PluginReleaseContext

        :param sid: The SID of the Flex Plugin Release resource to fetch.
        """
        return PluginReleaseContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V1.PluginReleaseList>"
