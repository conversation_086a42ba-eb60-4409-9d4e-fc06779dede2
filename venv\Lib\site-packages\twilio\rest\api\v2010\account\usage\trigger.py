r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Api
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class TriggerInstance(InstanceResource):

    class Recurring(object):
        DAILY = "daily"
        MONTHLY = "monthly"
        YEARLY = "yearly"
        ALLTIME = "alltime"

    class TriggerField(object):
        COUNT = "count"
        USAGE = "usage"
        PRICE = "price"

    class UsageCategory(object):
        A2P_REGISTRATION_FEES = "a2p-registration-fees"
        AGENT_CONFERENCE = "agent-conference"
        AMAZON_POLLY = "amazon-polly"
        ANSWERING_MACHINE_DETECTION = "answering-machine-detection"
        AUTHY_AUTHENTICATIONS = "authy-authentications"
        AUTHY_CALLS_OUTBOUND = "authy-calls-outbound"
        AUTHY_MONTHLY_FEES = "authy-monthly-fees"
        AUTHY_PHONE_INTELLIGENCE = "authy-phone-intelligence"
        AUTHY_PHONE_VERIFICATIONS = "authy-phone-verifications"
        AUTHY_SMS_OUTBOUND = "authy-sms-outbound"
        CALL_PROGESS_EVENTS = "call-progess-events"
        CALLERIDLOOKUPS = "calleridlookups"
        CALLS = "calls"
        CALLS_CLIENT = "calls-client"
        CALLS_GLOBALCONFERENCE = "calls-globalconference"
        CALLS_INBOUND = "calls-inbound"
        CALLS_INBOUND_LOCAL = "calls-inbound-local"
        CALLS_INBOUND_MOBILE = "calls-inbound-mobile"
        CALLS_INBOUND_TOLLFREE = "calls-inbound-tollfree"
        CALLS_OUTBOUND = "calls-outbound"
        CALLS_PAY_VERB_TRANSACTIONS = "calls-pay-verb-transactions"
        CALLS_RECORDINGS = "calls-recordings"
        CALLS_SIP = "calls-sip"
        CALLS_SIP_INBOUND = "calls-sip-inbound"
        CALLS_SIP_OUTBOUND = "calls-sip-outbound"
        CALLS_TRANSFERS = "calls-transfers"
        CARRIER_LOOKUPS = "carrier-lookups"
        CONVERSATIONS = "conversations"
        CONVERSATIONS_API_REQUESTS = "conversations-api-requests"
        CONVERSATIONS_CONVERSATION_EVENTS = "conversations-conversation-events"
        CONVERSATIONS_ENDPOINT_CONNECTIVITY = "conversations-endpoint-connectivity"
        CONVERSATIONS_EVENTS = "conversations-events"
        CONVERSATIONS_PARTICIPANT_EVENTS = "conversations-participant-events"
        CONVERSATIONS_PARTICIPANTS = "conversations-participants"
        CPS = "cps"
        FLEX_USAGE = "flex-usage"
        FRAUD_LOOKUPS = "fraud-lookups"
        GROUP_ROOMS = "group-rooms"
        GROUP_ROOMS_DATA_TRACK = "group-rooms-data-track"
        GROUP_ROOMS_ENCRYPTED_MEDIA_RECORDED = "group-rooms-encrypted-media-recorded"
        GROUP_ROOMS_MEDIA_DOWNLOADED = "group-rooms-media-downloaded"
        GROUP_ROOMS_MEDIA_RECORDED = "group-rooms-media-recorded"
        GROUP_ROOMS_MEDIA_ROUTED = "group-rooms-media-routed"
        GROUP_ROOMS_MEDIA_STORED = "group-rooms-media-stored"
        GROUP_ROOMS_PARTICIPANT_MINUTES = "group-rooms-participant-minutes"
        GROUP_ROOMS_RECORDED_MINUTES = "group-rooms-recorded-minutes"
        IMP_V1_USAGE = "imp-v1-usage"
        IVR_VIRTUAL_AGENT_CUSTOM_VOICES = "ivr-virtual-agent-custom-voices"
        IVR_VIRTUAL_AGENT_GENAI = "ivr-virtual-agent-genai"
        LOOKUPS = "lookups"
        MARKETPLACE = "marketplace"
        MARKETPLACE_ALGORITHMIA_NAMED_ENTITY_RECOGNITION = (
            "marketplace-algorithmia-named-entity-recognition"
        )
        MARKETPLACE_CADENCE_TRANSCRIPTION = "marketplace-cadence-transcription"
        MARKETPLACE_CADENCE_TRANSLATION = "marketplace-cadence-translation"
        MARKETPLACE_CAPIO_SPEECH_TO_TEXT = "marketplace-capio-speech-to-text"
        MARKETPLACE_CONVRIZA_ABABA = "marketplace-convriza-ababa"
        MARKETPLACE_DEEPGRAM_PHRASE_DETECTOR = "marketplace-deepgram-phrase-detector"
        MARKETPLACE_DIGITAL_SEGMENT_BUSINESS_INFO = (
            "marketplace-digital-segment-business-info"
        )
        MARKETPLACE_FACEBOOK_OFFLINE_CONVERSIONS = (
            "marketplace-facebook-offline-conversions"
        )
        MARKETPLACE_GOOGLE_SPEECH_TO_TEXT = "marketplace-google-speech-to-text"
        MARKETPLACE_IBM_WATSON_MESSAGE_INSIGHTS = (
            "marketplace-ibm-watson-message-insights"
        )
        MARKETPLACE_IBM_WATSON_MESSAGE_SENTIMENT = (
            "marketplace-ibm-watson-message-sentiment"
        )
        MARKETPLACE_IBM_WATSON_RECORDING_ANALYSIS = (
            "marketplace-ibm-watson-recording-analysis"
        )
        MARKETPLACE_IBM_WATSON_TONE_ANALYZER = "marketplace-ibm-watson-tone-analyzer"
        MARKETPLACE_ICEHOOK_SYSTEMS_SCOUT = "marketplace-icehook-systems-scout"
        MARKETPLACE_INFOGROUP_DATAAXLE_BIZINFO = (
            "marketplace-infogroup-dataaxle-bizinfo"
        )
        MARKETPLACE_KEEN_IO_CONTACT_CENTER_ANALYTICS = (
            "marketplace-keen-io-contact-center-analytics"
        )
        MARKETPLACE_MARCHEX_CLEANCALL = "marketplace-marchex-cleancall"
        MARKETPLACE_MARCHEX_SENTIMENT_ANALYSIS_FOR_SMS = (
            "marketplace-marchex-sentiment-analysis-for-sms"
        )
        MARKETPLACE_MARKETPLACE_NEXTCALLER_SOCIAL_ID = (
            "marketplace-marketplace-nextcaller-social-id"
        )
        MARKETPLACE_MOBILE_COMMONS_OPT_OUT_CLASSIFIER = (
            "marketplace-mobile-commons-opt-out-classifier"
        )
        MARKETPLACE_NEXIWAVE_VOICEMAIL_TO_TEXT = (
            "marketplace-nexiwave-voicemail-to-text"
        )
        MARKETPLACE_NEXTCALLER_ADVANCED_CALLER_IDENTIFICATION = (
            "marketplace-nextcaller-advanced-caller-identification"
        )
        MARKETPLACE_NOMOROBO_SPAM_SCORE = "marketplace-nomorobo-spam-score"
        MARKETPLACE_PAYFONE_TCPA_COMPLIANCE = "marketplace-payfone-tcpa-compliance"
        MARKETPLACE_REMEETING_AUTOMATIC_SPEECH_RECOGNITION = (
            "marketplace-remeeting-automatic-speech-recognition"
        )
        MARKETPLACE_TCPA_DEFENSE_SOLUTIONS_BLACKLIST_FEED = (
            "marketplace-tcpa-defense-solutions-blacklist-feed"
        )
        MARKETPLACE_TELO_OPENCNAM = "marketplace-telo-opencnam"
        MARKETPLACE_TRUECNAM_TRUE_SPAM = "marketplace-truecnam-true-spam"
        MARKETPLACE_TWILIO_CALLER_NAME_LOOKUP_US = (
            "marketplace-twilio-caller-name-lookup-us"
        )
        MARKETPLACE_TWILIO_CARRIER_INFORMATION_LOOKUP = (
            "marketplace-twilio-carrier-information-lookup"
        )
        MARKETPLACE_VOICEBASE_PCI = "marketplace-voicebase-pci"
        MARKETPLACE_VOICEBASE_TRANSCRIPTION = "marketplace-voicebase-transcription"
        MARKETPLACE_VOICEBASE_TRANSCRIPTION_CUSTOM_VOCABULARY = (
            "marketplace-voicebase-transcription-custom-vocabulary"
        )
        MARKETPLACE_WHITEPAGES_PRO_CALLER_IDENTIFICATION = (
            "marketplace-whitepages-pro-caller-identification"
        )
        MARKETPLACE_WHITEPAGES_PRO_PHONE_INTELLIGENCE = (
            "marketplace-whitepages-pro-phone-intelligence"
        )
        MARKETPLACE_WHITEPAGES_PRO_PHONE_REPUTATION = (
            "marketplace-whitepages-pro-phone-reputation"
        )
        MARKETPLACE_WOLFARM_SPOKEN_RESULTS = "marketplace-wolfarm-spoken-results"
        MARKETPLACE_WOLFRAM_SHORT_ANSWER = "marketplace-wolfram-short-answer"
        MARKETPLACE_YTICA_CONTACT_CENTER_REPORTING_ANALYTICS = (
            "marketplace-ytica-contact-center-reporting-analytics"
        )
        MEDIASTORAGE = "mediastorage"
        MMS = "mms"
        MMS_INBOUND = "mms-inbound"
        MMS_INBOUND_LONGCODE = "mms-inbound-longcode"
        MMS_INBOUND_SHORTCODE = "mms-inbound-shortcode"
        MMS_MESSAGES_CARRIERFEES = "mms-messages-carrierfees"
        MMS_OUTBOUND = "mms-outbound"
        MMS_OUTBOUND_LONGCODE = "mms-outbound-longcode"
        MMS_OUTBOUND_SHORTCODE = "mms-outbound-shortcode"
        MONITOR_READS = "monitor-reads"
        MONITOR_STORAGE = "monitor-storage"
        MONITOR_WRITES = "monitor-writes"
        NOTIFY = "notify"
        NOTIFY_ACTIONS_ATTEMPTS = "notify-actions-attempts"
        NOTIFY_CHANNELS = "notify-channels"
        NUMBER_FORMAT_LOOKUPS = "number-format-lookups"
        PCHAT = "pchat"
        PCHAT_USERS = "pchat-users"
        PEER_TO_PEER_ROOMS_PARTICIPANT_MINUTES = (
            "peer-to-peer-rooms-participant-minutes"
        )
        PFAX = "pfax"
        PFAX_MINUTES = "pfax-minutes"
        PFAX_MINUTES_INBOUND = "pfax-minutes-inbound"
        PFAX_MINUTES_OUTBOUND = "pfax-minutes-outbound"
        PFAX_PAGES = "pfax-pages"
        PHONENUMBERS = "phonenumbers"
        PHONENUMBERS_CPS = "phonenumbers-cps"
        PHONENUMBERS_EMERGENCY = "phonenumbers-emergency"
        PHONENUMBERS_LOCAL = "phonenumbers-local"
        PHONENUMBERS_MOBILE = "phonenumbers-mobile"
        PHONENUMBERS_SETUPS = "phonenumbers-setups"
        PHONENUMBERS_TOLLFREE = "phonenumbers-tollfree"
        PREMIUMSUPPORT = "premiumsupport"
        PROXY = "proxy"
        PROXY_ACTIVE_SESSIONS = "proxy-active-sessions"
        PSTNCONNECTIVITY = "pstnconnectivity"
        PV = "pv"
        PV_COMPOSITION_MEDIA_DOWNLOADED = "pv-composition-media-downloaded"
        PV_COMPOSITION_MEDIA_ENCRYPTED = "pv-composition-media-encrypted"
        PV_COMPOSITION_MEDIA_STORED = "pv-composition-media-stored"
        PV_COMPOSITION_MINUTES = "pv-composition-minutes"
        PV_RECORDING_COMPOSITIONS = "pv-recording-compositions"
        PV_ROOM_PARTICIPANTS = "pv-room-participants"
        PV_ROOM_PARTICIPANTS_AU1 = "pv-room-participants-au1"
        PV_ROOM_PARTICIPANTS_BR1 = "pv-room-participants-br1"
        PV_ROOM_PARTICIPANTS_IE1 = "pv-room-participants-ie1"
        PV_ROOM_PARTICIPANTS_JP1 = "pv-room-participants-jp1"
        PV_ROOM_PARTICIPANTS_SG1 = "pv-room-participants-sg1"
        PV_ROOM_PARTICIPANTS_US1 = "pv-room-participants-us1"
        PV_ROOM_PARTICIPANTS_US2 = "pv-room-participants-us2"
        PV_ROOMS = "pv-rooms"
        PV_SIP_ENDPOINT_REGISTRATIONS = "pv-sip-endpoint-registrations"
        RECORDINGS = "recordings"
        RECORDINGSTORAGE = "recordingstorage"
        ROOMS_GROUP_BANDWIDTH = "rooms-group-bandwidth"
        ROOMS_GROUP_MINUTES = "rooms-group-minutes"
        ROOMS_PEER_TO_PEER_MINUTES = "rooms-peer-to-peer-minutes"
        SHORTCODES = "shortcodes"
        SHORTCODES_CUSTOMEROWNED = "shortcodes-customerowned"
        SHORTCODES_MMS_ENABLEMENT = "shortcodes-mms-enablement"
        SHORTCODES_MPS = "shortcodes-mps"
        SHORTCODES_RANDOM = "shortcodes-random"
        SHORTCODES_UK = "shortcodes-uk"
        SHORTCODES_VANITY = "shortcodes-vanity"
        SMALL_GROUP_ROOMS = "small-group-rooms"
        SMALL_GROUP_ROOMS_DATA_TRACK = "small-group-rooms-data-track"
        SMALL_GROUP_ROOMS_PARTICIPANT_MINUTES = "small-group-rooms-participant-minutes"
        SMS = "sms"
        SMS_INBOUND = "sms-inbound"
        SMS_INBOUND_LONGCODE = "sms-inbound-longcode"
        SMS_INBOUND_SHORTCODE = "sms-inbound-shortcode"
        SMS_MESSAGES_CARRIERFEES = "sms-messages-carrierfees"
        SMS_MESSAGES_FEATURES = "sms-messages-features"
        SMS_MESSAGES_FEATURES_SENDERID = "sms-messages-features-senderid"
        SMS_OUTBOUND = "sms-outbound"
        SMS_OUTBOUND_CONTENT_INSPECTION = "sms-outbound-content-inspection"
        SMS_OUTBOUND_LONGCODE = "sms-outbound-longcode"
        SMS_OUTBOUND_SHORTCODE = "sms-outbound-shortcode"
        SPEECH_RECOGNITION = "speech-recognition"
        STUDIO_ENGAGEMENTS = "studio-engagements"
        SYNC = "sync"
        SYNC_ACTIONS = "sync-actions"
        SYNC_ENDPOINT_HOURS = "sync-endpoint-hours"
        SYNC_ENDPOINT_HOURS_ABOVE_DAILY_CAP = "sync-endpoint-hours-above-daily-cap"
        TASKROUTER_TASKS = "taskrouter-tasks"
        TOTALPRICE = "totalprice"
        TRANSCRIPTIONS = "transcriptions"
        TRUNKING_CPS = "trunking-cps"
        TRUNKING_EMERGENCY_CALLS = "trunking-emergency-calls"
        TRUNKING_ORIGINATION = "trunking-origination"
        TRUNKING_ORIGINATION_LOCAL = "trunking-origination-local"
        TRUNKING_ORIGINATION_MOBILE = "trunking-origination-mobile"
        TRUNKING_ORIGINATION_TOLLFREE = "trunking-origination-tollfree"
        TRUNKING_RECORDINGS = "trunking-recordings"
        TRUNKING_SECURE = "trunking-secure"
        TRUNKING_TERMINATION = "trunking-termination"
        TTS_GOOGLE = "tts-google"
        TURNMEGABYTES = "turnmegabytes"
        TURNMEGABYTES_AUSTRALIA = "turnmegabytes-australia"
        TURNMEGABYTES_BRASIL = "turnmegabytes-brasil"
        TURNMEGABYTES_GERMANY = "turnmegabytes-germany"
        TURNMEGABYTES_INDIA = "turnmegabytes-india"
        TURNMEGABYTES_IRELAND = "turnmegabytes-ireland"
        TURNMEGABYTES_JAPAN = "turnmegabytes-japan"
        TURNMEGABYTES_SINGAPORE = "turnmegabytes-singapore"
        TURNMEGABYTES_USEAST = "turnmegabytes-useast"
        TURNMEGABYTES_USWEST = "turnmegabytes-uswest"
        TWILIO_INTERCONNECT = "twilio-interconnect"
        VERIFY_PUSH = "verify-push"
        VERIFY_TOTP = "verify-totp"
        VERIFY_WHATSAPP_CONVERSATIONS_BUSINESS_INITIATED = (
            "verify-whatsapp-conversations-business-initiated"
        )
        VIDEO_RECORDINGS = "video-recordings"
        VIRTUAL_AGENT = "virtual-agent"
        VOICE_INSIGHTS = "voice-insights"
        VOICE_INSIGHTS_CLIENT_INSIGHTS_ON_DEMAND_MINUTE = (
            "voice-insights-client-insights-on-demand-minute"
        )
        VOICE_INSIGHTS_PTSN_INSIGHTS_ON_DEMAND_MINUTE = (
            "voice-insights-ptsn-insights-on-demand-minute"
        )
        VOICE_INSIGHTS_SIP_INTERFACE_INSIGHTS_ON_DEMAND_MINUTE = (
            "voice-insights-sip-interface-insights-on-demand-minute"
        )
        VOICE_INSIGHTS_SIP_TRUNKING_INSIGHTS_ON_DEMAND_MINUTE = (
            "voice-insights-sip-trunking-insights-on-demand-minute"
        )
        VOICE_INTELLIGENCE = "voice-intelligence"
        VOICE_INTELLIGENCE_TRANSCRIPTION = "voice-intelligence-transcription"
        VOICE_INTELLIGENCE_OPERATORS = "voice-intelligence-operators"
        WIRELESS = "wireless"
        WIRELESS_ORDERS = "wireless-orders"
        WIRELESS_ORDERS_ARTWORK = "wireless-orders-artwork"
        WIRELESS_ORDERS_BULK = "wireless-orders-bulk"
        WIRELESS_ORDERS_ESIM = "wireless-orders-esim"
        WIRELESS_ORDERS_STARTER = "wireless-orders-starter"
        WIRELESS_USAGE = "wireless-usage"
        WIRELESS_USAGE_COMMANDS = "wireless-usage-commands"
        WIRELESS_USAGE_COMMANDS_AFRICA = "wireless-usage-commands-africa"
        WIRELESS_USAGE_COMMANDS_ASIA = "wireless-usage-commands-asia"
        WIRELESS_USAGE_COMMANDS_CENTRALANDSOUTHAMERICA = (
            "wireless-usage-commands-centralandsouthamerica"
        )
        WIRELESS_USAGE_COMMANDS_EUROPE = "wireless-usage-commands-europe"
        WIRELESS_USAGE_COMMANDS_HOME = "wireless-usage-commands-home"
        WIRELESS_USAGE_COMMANDS_NORTHAMERICA = "wireless-usage-commands-northamerica"
        WIRELESS_USAGE_COMMANDS_OCEANIA = "wireless-usage-commands-oceania"
        WIRELESS_USAGE_COMMANDS_ROAMING = "wireless-usage-commands-roaming"
        WIRELESS_USAGE_DATA = "wireless-usage-data"
        WIRELESS_USAGE_DATA_AFRICA = "wireless-usage-data-africa"
        WIRELESS_USAGE_DATA_ASIA = "wireless-usage-data-asia"
        WIRELESS_USAGE_DATA_CENTRALANDSOUTHAMERICA = (
            "wireless-usage-data-centralandsouthamerica"
        )
        WIRELESS_USAGE_DATA_CUSTOM_ADDITIONALMB = (
            "wireless-usage-data-custom-additionalmb"
        )
        WIRELESS_USAGE_DATA_CUSTOM_FIRST5MB = "wireless-usage-data-custom-first5mb"
        WIRELESS_USAGE_DATA_DOMESTIC_ROAMING = "wireless-usage-data-domestic-roaming"
        WIRELESS_USAGE_DATA_EUROPE = "wireless-usage-data-europe"
        WIRELESS_USAGE_DATA_INDIVIDUAL_ADDITIONALGB = (
            "wireless-usage-data-individual-additionalgb"
        )
        WIRELESS_USAGE_DATA_INDIVIDUAL_FIRSTGB = (
            "wireless-usage-data-individual-firstgb"
        )
        WIRELESS_USAGE_DATA_INTERNATIONAL_ROAMING_CANADA = (
            "wireless-usage-data-international-roaming-canada"
        )
        WIRELESS_USAGE_DATA_INTERNATIONAL_ROAMING_INDIA = (
            "wireless-usage-data-international-roaming-india"
        )
        WIRELESS_USAGE_DATA_INTERNATIONAL_ROAMING_MEXICO = (
            "wireless-usage-data-international-roaming-mexico"
        )
        WIRELESS_USAGE_DATA_NORTHAMERICA = "wireless-usage-data-northamerica"
        WIRELESS_USAGE_DATA_OCEANIA = "wireless-usage-data-oceania"
        WIRELESS_USAGE_DATA_POOLED = "wireless-usage-data-pooled"
        WIRELESS_USAGE_DATA_POOLED_DOWNLINK = "wireless-usage-data-pooled-downlink"
        WIRELESS_USAGE_DATA_POOLED_UPLINK = "wireless-usage-data-pooled-uplink"
        WIRELESS_USAGE_MRC = "wireless-usage-mrc"
        WIRELESS_USAGE_MRC_CUSTOM = "wireless-usage-mrc-custom"
        WIRELESS_USAGE_MRC_INDIVIDUAL = "wireless-usage-mrc-individual"
        WIRELESS_USAGE_MRC_POOLED = "wireless-usage-mrc-pooled"
        WIRELESS_USAGE_MRC_SUSPENDED = "wireless-usage-mrc-suspended"
        WIRELESS_USAGE_SMS = "wireless-usage-sms"
        WIRELESS_USAGE_VOICE = "wireless-usage-voice"

    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that the trigger monitors.
    :ivar api_version: The API version used to create the resource.
    :ivar callback_method: The HTTP method we use to call `callback_url`. Can be: `GET` or `POST`.
    :ivar callback_url: The URL we call using the `callback_method` when the trigger fires.
    :ivar current_value: The current value of the field the trigger is watching.
    :ivar date_created: The date and time in GMT that the resource was created specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar date_fired: The date and time in GMT that the trigger was last fired specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar date_updated: The date and time in GMT that the resource was last updated specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar friendly_name: The string that you assigned to describe the trigger.
    :ivar recurring: 
    :ivar sid: The unique string that that we created to identify the UsageTrigger resource.
    :ivar trigger_by: 
    :ivar trigger_value: The value at which the trigger will fire.  Must be a positive, numeric value.
    :ivar uri: The URI of the resource, relative to `https://api.twilio.com`.
    :ivar usage_category: 
    :ivar usage_record_uri: The URI of the [UsageRecord](https://www.twilio.com/docs/usage/api/usage-record) resource this trigger watches, relative to `https://api.twilio.com`.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        account_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.api_version: Optional[str] = payload.get("api_version")
        self.callback_method: Optional[str] = payload.get("callback_method")
        self.callback_url: Optional[str] = payload.get("callback_url")
        self.current_value: Optional[str] = payload.get("current_value")
        self.date_created: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_created")
        )
        self.date_fired: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_fired")
        )
        self.date_updated: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_updated")
        )
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.recurring: Optional["TriggerInstance.Recurring"] = payload.get("recurring")
        self.sid: Optional[str] = payload.get("sid")
        self.trigger_by: Optional["TriggerInstance.TriggerField"] = payload.get(
            "trigger_by"
        )
        self.trigger_value: Optional[str] = payload.get("trigger_value")
        self.uri: Optional[str] = payload.get("uri")
        self.usage_category: Optional["TriggerInstance.UsageCategory"] = payload.get(
            "usage_category"
        )
        self.usage_record_uri: Optional[str] = payload.get("usage_record_uri")

        self._solution = {
            "account_sid": account_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[TriggerContext] = None

    @property
    def _proxy(self) -> "TriggerContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: TriggerContext for this TriggerInstance
        """
        if self._context is None:
            self._context = TriggerContext(
                self._version,
                account_sid=self._solution["account_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the TriggerInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the TriggerInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "TriggerInstance":
        """
        Fetch the TriggerInstance


        :returns: The fetched TriggerInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "TriggerInstance":
        """
        Asynchronous coroutine to fetch the TriggerInstance


        :returns: The fetched TriggerInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        callback_method: Union[str, object] = values.unset,
        callback_url: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
    ) -> "TriggerInstance":
        """
        Update the TriggerInstance

        :param callback_method: The HTTP method we should use to call `callback_url`. Can be: `GET` or `POST` and the default is `POST`.
        :param callback_url: The URL we should call using `callback_method` when the trigger fires.
        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.

        :returns: The updated TriggerInstance
        """
        return self._proxy.update(
            callback_method=callback_method,
            callback_url=callback_url,
            friendly_name=friendly_name,
        )

    async def update_async(
        self,
        callback_method: Union[str, object] = values.unset,
        callback_url: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
    ) -> "TriggerInstance":
        """
        Asynchronous coroutine to update the TriggerInstance

        :param callback_method: The HTTP method we should use to call `callback_url`. Can be: `GET` or `POST` and the default is `POST`.
        :param callback_url: The URL we should call using `callback_method` when the trigger fires.
        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.

        :returns: The updated TriggerInstance
        """
        return await self._proxy.update_async(
            callback_method=callback_method,
            callback_url=callback_url,
            friendly_name=friendly_name,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.TriggerInstance {}>".format(context)


class TriggerContext(InstanceContext):

    def __init__(self, version: Version, account_sid: str, sid: str):
        """
        Initialize the TriggerContext

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the UsageTrigger resources to update.
        :param sid: The Twilio-provided string that uniquely identifies the UsageTrigger resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "sid": sid,
        }
        self._uri = "/Accounts/{account_sid}/Usage/Triggers/{sid}.json".format(
            **self._solution
        )

    def delete(self) -> bool:
        """
        Deletes the TriggerInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the TriggerInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> TriggerInstance:
        """
        Fetch the TriggerInstance


        :returns: The fetched TriggerInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return TriggerInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> TriggerInstance:
        """
        Asynchronous coroutine to fetch the TriggerInstance


        :returns: The fetched TriggerInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return TriggerInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    def update(
        self,
        callback_method: Union[str, object] = values.unset,
        callback_url: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
    ) -> TriggerInstance:
        """
        Update the TriggerInstance

        :param callback_method: The HTTP method we should use to call `callback_url`. Can be: `GET` or `POST` and the default is `POST`.
        :param callback_url: The URL we should call using `callback_method` when the trigger fires.
        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.

        :returns: The updated TriggerInstance
        """

        data = values.of(
            {
                "CallbackMethod": callback_method,
                "CallbackUrl": callback_url,
                "FriendlyName": friendly_name,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return TriggerInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self,
        callback_method: Union[str, object] = values.unset,
        callback_url: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
    ) -> TriggerInstance:
        """
        Asynchronous coroutine to update the TriggerInstance

        :param callback_method: The HTTP method we should use to call `callback_url`. Can be: `GET` or `POST` and the default is `POST`.
        :param callback_url: The URL we should call using `callback_method` when the trigger fires.
        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.

        :returns: The updated TriggerInstance
        """

        data = values.of(
            {
                "CallbackMethod": callback_method,
                "CallbackUrl": callback_url,
                "FriendlyName": friendly_name,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return TriggerInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.TriggerContext {}>".format(context)


class TriggerPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> TriggerInstance:
        """
        Build an instance of TriggerInstance

        :param payload: Payload response from the API
        """
        return TriggerInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.TriggerPage>"


class TriggerList(ListResource):

    def __init__(self, version: Version, account_sid: str):
        """
        Initialize the TriggerList

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the UsageTrigger resources to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
        }
        self._uri = "/Accounts/{account_sid}/Usage/Triggers.json".format(
            **self._solution
        )

    def create(
        self,
        callback_url: str,
        trigger_value: str,
        usage_category: "TriggerInstance.UsageCategory",
        callback_method: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        recurring: Union["TriggerInstance.Recurring", object] = values.unset,
        trigger_by: Union["TriggerInstance.TriggerField", object] = values.unset,
    ) -> TriggerInstance:
        """
        Create the TriggerInstance

        :param callback_url: The URL we should call using `callback_method` when the trigger fires.
        :param trigger_value: The usage value at which the trigger should fire.  For convenience, you can use an offset value such as `+30` to specify a trigger_value that is 30 units more than the current usage value. Be sure to urlencode a `+` as `%2B`.
        :param usage_category:
        :param callback_method: The HTTP method we should use to call `callback_url`. Can be: `GET` or `POST` and the default is `POST`.
        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.
        :param recurring:
        :param trigger_by:

        :returns: The created TriggerInstance
        """

        data = values.of(
            {
                "CallbackUrl": callback_url,
                "TriggerValue": trigger_value,
                "UsageCategory": usage_category,
                "CallbackMethod": callback_method,
                "FriendlyName": friendly_name,
                "Recurring": recurring,
                "TriggerBy": trigger_by,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return TriggerInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    async def create_async(
        self,
        callback_url: str,
        trigger_value: str,
        usage_category: "TriggerInstance.UsageCategory",
        callback_method: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        recurring: Union["TriggerInstance.Recurring", object] = values.unset,
        trigger_by: Union["TriggerInstance.TriggerField", object] = values.unset,
    ) -> TriggerInstance:
        """
        Asynchronously create the TriggerInstance

        :param callback_url: The URL we should call using `callback_method` when the trigger fires.
        :param trigger_value: The usage value at which the trigger should fire.  For convenience, you can use an offset value such as `+30` to specify a trigger_value that is 30 units more than the current usage value. Be sure to urlencode a `+` as `%2B`.
        :param usage_category:
        :param callback_method: The HTTP method we should use to call `callback_url`. Can be: `GET` or `POST` and the default is `POST`.
        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.
        :param recurring:
        :param trigger_by:

        :returns: The created TriggerInstance
        """

        data = values.of(
            {
                "CallbackUrl": callback_url,
                "TriggerValue": trigger_value,
                "UsageCategory": usage_category,
                "CallbackMethod": callback_method,
                "FriendlyName": friendly_name,
                "Recurring": recurring,
                "TriggerBy": trigger_by,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return TriggerInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    def stream(
        self,
        recurring: Union["TriggerInstance.Recurring", object] = values.unset,
        trigger_by: Union["TriggerInstance.TriggerField", object] = values.unset,
        usage_category: Union["TriggerInstance.UsageCategory", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[TriggerInstance]:
        """
        Streams TriggerInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param &quot;TriggerInstance.Recurring&quot; recurring: The frequency of recurring UsageTriggers to read. Can be: `daily`, `monthly`, or `yearly` to read recurring UsageTriggers. An empty value or a value of `alltime` reads non-recurring UsageTriggers.
        :param &quot;TriggerInstance.TriggerField&quot; trigger_by: The trigger field of the UsageTriggers to read.  Can be: `count`, `usage`, or `price` as described in the [UsageRecords documentation](https://www.twilio.com/docs/usage/api/usage-record#usage-count-price).
        :param &quot;TriggerInstance.UsageCategory&quot; usage_category: The usage category of the UsageTriggers to read. Must be a supported [usage categories](https://www.twilio.com/docs/usage/api/usage-record#usage-categories).
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            recurring=recurring,
            trigger_by=trigger_by,
            usage_category=usage_category,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        recurring: Union["TriggerInstance.Recurring", object] = values.unset,
        trigger_by: Union["TriggerInstance.TriggerField", object] = values.unset,
        usage_category: Union["TriggerInstance.UsageCategory", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[TriggerInstance]:
        """
        Asynchronously streams TriggerInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param &quot;TriggerInstance.Recurring&quot; recurring: The frequency of recurring UsageTriggers to read. Can be: `daily`, `monthly`, or `yearly` to read recurring UsageTriggers. An empty value or a value of `alltime` reads non-recurring UsageTriggers.
        :param &quot;TriggerInstance.TriggerField&quot; trigger_by: The trigger field of the UsageTriggers to read.  Can be: `count`, `usage`, or `price` as described in the [UsageRecords documentation](https://www.twilio.com/docs/usage/api/usage-record#usage-count-price).
        :param &quot;TriggerInstance.UsageCategory&quot; usage_category: The usage category of the UsageTriggers to read. Must be a supported [usage categories](https://www.twilio.com/docs/usage/api/usage-record#usage-categories).
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            recurring=recurring,
            trigger_by=trigger_by,
            usage_category=usage_category,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        recurring: Union["TriggerInstance.Recurring", object] = values.unset,
        trigger_by: Union["TriggerInstance.TriggerField", object] = values.unset,
        usage_category: Union["TriggerInstance.UsageCategory", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[TriggerInstance]:
        """
        Lists TriggerInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param &quot;TriggerInstance.Recurring&quot; recurring: The frequency of recurring UsageTriggers to read. Can be: `daily`, `monthly`, or `yearly` to read recurring UsageTriggers. An empty value or a value of `alltime` reads non-recurring UsageTriggers.
        :param &quot;TriggerInstance.TriggerField&quot; trigger_by: The trigger field of the UsageTriggers to read.  Can be: `count`, `usage`, or `price` as described in the [UsageRecords documentation](https://www.twilio.com/docs/usage/api/usage-record#usage-count-price).
        :param &quot;TriggerInstance.UsageCategory&quot; usage_category: The usage category of the UsageTriggers to read. Must be a supported [usage categories](https://www.twilio.com/docs/usage/api/usage-record#usage-categories).
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                recurring=recurring,
                trigger_by=trigger_by,
                usage_category=usage_category,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        recurring: Union["TriggerInstance.Recurring", object] = values.unset,
        trigger_by: Union["TriggerInstance.TriggerField", object] = values.unset,
        usage_category: Union["TriggerInstance.UsageCategory", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[TriggerInstance]:
        """
        Asynchronously lists TriggerInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param &quot;TriggerInstance.Recurring&quot; recurring: The frequency of recurring UsageTriggers to read. Can be: `daily`, `monthly`, or `yearly` to read recurring UsageTriggers. An empty value or a value of `alltime` reads non-recurring UsageTriggers.
        :param &quot;TriggerInstance.TriggerField&quot; trigger_by: The trigger field of the UsageTriggers to read.  Can be: `count`, `usage`, or `price` as described in the [UsageRecords documentation](https://www.twilio.com/docs/usage/api/usage-record#usage-count-price).
        :param &quot;TriggerInstance.UsageCategory&quot; usage_category: The usage category of the UsageTriggers to read. Must be a supported [usage categories](https://www.twilio.com/docs/usage/api/usage-record#usage-categories).
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                recurring=recurring,
                trigger_by=trigger_by,
                usage_category=usage_category,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        recurring: Union["TriggerInstance.Recurring", object] = values.unset,
        trigger_by: Union["TriggerInstance.TriggerField", object] = values.unset,
        usage_category: Union["TriggerInstance.UsageCategory", object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> TriggerPage:
        """
        Retrieve a single page of TriggerInstance records from the API.
        Request is executed immediately

        :param recurring: The frequency of recurring UsageTriggers to read. Can be: `daily`, `monthly`, or `yearly` to read recurring UsageTriggers. An empty value or a value of `alltime` reads non-recurring UsageTriggers.
        :param trigger_by: The trigger field of the UsageTriggers to read.  Can be: `count`, `usage`, or `price` as described in the [UsageRecords documentation](https://www.twilio.com/docs/usage/api/usage-record#usage-count-price).
        :param usage_category: The usage category of the UsageTriggers to read. Must be a supported [usage categories](https://www.twilio.com/docs/usage/api/usage-record#usage-categories).
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of TriggerInstance
        """
        data = values.of(
            {
                "Recurring": recurring,
                "TriggerBy": trigger_by,
                "UsageCategory": usage_category,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return TriggerPage(self._version, response, self._solution)

    async def page_async(
        self,
        recurring: Union["TriggerInstance.Recurring", object] = values.unset,
        trigger_by: Union["TriggerInstance.TriggerField", object] = values.unset,
        usage_category: Union["TriggerInstance.UsageCategory", object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> TriggerPage:
        """
        Asynchronously retrieve a single page of TriggerInstance records from the API.
        Request is executed immediately

        :param recurring: The frequency of recurring UsageTriggers to read. Can be: `daily`, `monthly`, or `yearly` to read recurring UsageTriggers. An empty value or a value of `alltime` reads non-recurring UsageTriggers.
        :param trigger_by: The trigger field of the UsageTriggers to read.  Can be: `count`, `usage`, or `price` as described in the [UsageRecords documentation](https://www.twilio.com/docs/usage/api/usage-record#usage-count-price).
        :param usage_category: The usage category of the UsageTriggers to read. Must be a supported [usage categories](https://www.twilio.com/docs/usage/api/usage-record#usage-categories).
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of TriggerInstance
        """
        data = values.of(
            {
                "Recurring": recurring,
                "TriggerBy": trigger_by,
                "UsageCategory": usage_category,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return TriggerPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> TriggerPage:
        """
        Retrieve a specific page of TriggerInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of TriggerInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return TriggerPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> TriggerPage:
        """
        Asynchronously retrieve a specific page of TriggerInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of TriggerInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return TriggerPage(self._version, response, self._solution)

    def get(self, sid: str) -> TriggerContext:
        """
        Constructs a TriggerContext

        :param sid: The Twilio-provided string that uniquely identifies the UsageTrigger resource to update.
        """
        return TriggerContext(
            self._version, account_sid=self._solution["account_sid"], sid=sid
        )

    def __call__(self, sid: str) -> TriggerContext:
        """
        Constructs a TriggerContext

        :param sid: The Twilio-provided string that uniquely identifies the UsageTrigger resource to update.
        """
        return TriggerContext(
            self._version, account_sid=self._solution["account_sid"], sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.TriggerList>"
