r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Api
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class ApplicationInstance(InstanceResource):
    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Application resource.
    :ivar api_version: The API version used to start a new TwiML session.
    :ivar date_created: The date and time in GMT that the resource was created specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar date_updated: The date and time in GMT that the resource was last updated specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar friendly_name: The string that you assigned to describe the resource.
    :ivar message_status_callback: The URL we call using a POST method to send message status information to your application.
    :ivar sid: The unique string that that we created to identify the Application resource.
    :ivar sms_fallback_method: The HTTP method we use to call `sms_fallback_url`. Can be: `GET` or `POST`.
    :ivar sms_fallback_url: The URL that we call when an error occurs while retrieving or executing the TwiML from `sms_url`.
    :ivar sms_method: The HTTP method we use to call `sms_url`. Can be: `GET` or `POST`.
    :ivar sms_status_callback: The URL we call using a POST method to send status information to your application about SMS messages that refer to the application.
    :ivar sms_url: The URL we call when the phone number receives an incoming SMS message.
    :ivar status_callback: The URL we call using the `status_callback_method` to send status information to your application.
    :ivar status_callback_method: The HTTP method we use to call `status_callback`. Can be: `GET` or `POST`.
    :ivar uri: The URI of the resource, relative to `https://api.twilio.com`.
    :ivar voice_caller_id_lookup: Whether we look up the caller's caller-ID name from the CNAM database (additional charges apply). Can be: `true` or `false`.
    :ivar voice_fallback_method: The HTTP method we use to call `voice_fallback_url`. Can be: `GET` or `POST`.
    :ivar voice_fallback_url: The URL that we call when an error occurs retrieving or executing the TwiML requested by `url`.
    :ivar voice_method: The HTTP method we use to call `voice_url`. Can be: `GET` or `POST`.
    :ivar voice_url: The URL we call when the phone number assigned to this application receives a call.
    :ivar public_application_connect_enabled: Whether to allow other Twilio accounts to dial this applicaton using Dial verb. Can be: `true` or `false`.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        account_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.api_version: Optional[str] = payload.get("api_version")
        self.date_created: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_updated")
        )
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.message_status_callback: Optional[str] = payload.get(
            "message_status_callback"
        )
        self.sid: Optional[str] = payload.get("sid")
        self.sms_fallback_method: Optional[str] = payload.get("sms_fallback_method")
        self.sms_fallback_url: Optional[str] = payload.get("sms_fallback_url")
        self.sms_method: Optional[str] = payload.get("sms_method")
        self.sms_status_callback: Optional[str] = payload.get("sms_status_callback")
        self.sms_url: Optional[str] = payload.get("sms_url")
        self.status_callback: Optional[str] = payload.get("status_callback")
        self.status_callback_method: Optional[str] = payload.get(
            "status_callback_method"
        )
        self.uri: Optional[str] = payload.get("uri")
        self.voice_caller_id_lookup: Optional[bool] = payload.get(
            "voice_caller_id_lookup"
        )
        self.voice_fallback_method: Optional[str] = payload.get("voice_fallback_method")
        self.voice_fallback_url: Optional[str] = payload.get("voice_fallback_url")
        self.voice_method: Optional[str] = payload.get("voice_method")
        self.voice_url: Optional[str] = payload.get("voice_url")
        self.public_application_connect_enabled: Optional[bool] = payload.get(
            "public_application_connect_enabled"
        )

        self._solution = {
            "account_sid": account_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[ApplicationContext] = None

    @property
    def _proxy(self) -> "ApplicationContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ApplicationContext for this ApplicationInstance
        """
        if self._context is None:
            self._context = ApplicationContext(
                self._version,
                account_sid=self._solution["account_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the ApplicationInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ApplicationInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "ApplicationInstance":
        """
        Fetch the ApplicationInstance


        :returns: The fetched ApplicationInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ApplicationInstance":
        """
        Asynchronous coroutine to fetch the ApplicationInstance


        :returns: The fetched ApplicationInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        api_version: Union[str, object] = values.unset,
        voice_url: Union[str, object] = values.unset,
        voice_method: Union[str, object] = values.unset,
        voice_fallback_url: Union[str, object] = values.unset,
        voice_fallback_method: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        voice_caller_id_lookup: Union[bool, object] = values.unset,
        sms_url: Union[str, object] = values.unset,
        sms_method: Union[str, object] = values.unset,
        sms_fallback_url: Union[str, object] = values.unset,
        sms_fallback_method: Union[str, object] = values.unset,
        sms_status_callback: Union[str, object] = values.unset,
        message_status_callback: Union[str, object] = values.unset,
        public_application_connect_enabled: Union[bool, object] = values.unset,
    ) -> "ApplicationInstance":
        """
        Update the ApplicationInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.
        :param api_version: The API version to use to start a new TwiML session. Can be: `2010-04-01` or `2008-08-01`. The default value is your account's default API version.
        :param voice_url: The URL we should call when the phone number assigned to this application receives a call.
        :param voice_method: The HTTP method we should use to call `voice_url`. Can be: `GET` or `POST`.
        :param voice_fallback_url: The URL that we should call when an error occurs retrieving or executing the TwiML requested by `url`.
        :param voice_fallback_method: The HTTP method we should use to call `voice_fallback_url`. Can be: `GET` or `POST`.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application.
        :param status_callback_method: The HTTP method we should use to call `status_callback`. Can be: `GET` or `POST`.
        :param voice_caller_id_lookup: Whether we should look up the caller's caller-ID name from the CNAM database (additional charges apply). Can be: `true` or `false`.
        :param sms_url: The URL we should call when the phone number receives an incoming SMS message.
        :param sms_method: The HTTP method we should use to call `sms_url`. Can be: `GET` or `POST`.
        :param sms_fallback_url: The URL that we should call when an error occurs while retrieving or executing the TwiML from `sms_url`.
        :param sms_fallback_method: The HTTP method we should use to call `sms_fallback_url`. Can be: `GET` or `POST`.
        :param sms_status_callback: Same as message_status_callback: The URL we should call using a POST method to send status information about SMS messages sent by the application. Deprecated, included for backwards compatibility.
        :param message_status_callback: The URL we should call using a POST method to send message status information to your application.
        :param public_application_connect_enabled: Whether to allow other Twilio accounts to dial this applicaton using Dial verb. Can be: `true` or `false`.

        :returns: The updated ApplicationInstance
        """
        return self._proxy.update(
            friendly_name=friendly_name,
            api_version=api_version,
            voice_url=voice_url,
            voice_method=voice_method,
            voice_fallback_url=voice_fallback_url,
            voice_fallback_method=voice_fallback_method,
            status_callback=status_callback,
            status_callback_method=status_callback_method,
            voice_caller_id_lookup=voice_caller_id_lookup,
            sms_url=sms_url,
            sms_method=sms_method,
            sms_fallback_url=sms_fallback_url,
            sms_fallback_method=sms_fallback_method,
            sms_status_callback=sms_status_callback,
            message_status_callback=message_status_callback,
            public_application_connect_enabled=public_application_connect_enabled,
        )

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        api_version: Union[str, object] = values.unset,
        voice_url: Union[str, object] = values.unset,
        voice_method: Union[str, object] = values.unset,
        voice_fallback_url: Union[str, object] = values.unset,
        voice_fallback_method: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        voice_caller_id_lookup: Union[bool, object] = values.unset,
        sms_url: Union[str, object] = values.unset,
        sms_method: Union[str, object] = values.unset,
        sms_fallback_url: Union[str, object] = values.unset,
        sms_fallback_method: Union[str, object] = values.unset,
        sms_status_callback: Union[str, object] = values.unset,
        message_status_callback: Union[str, object] = values.unset,
        public_application_connect_enabled: Union[bool, object] = values.unset,
    ) -> "ApplicationInstance":
        """
        Asynchronous coroutine to update the ApplicationInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.
        :param api_version: The API version to use to start a new TwiML session. Can be: `2010-04-01` or `2008-08-01`. The default value is your account's default API version.
        :param voice_url: The URL we should call when the phone number assigned to this application receives a call.
        :param voice_method: The HTTP method we should use to call `voice_url`. Can be: `GET` or `POST`.
        :param voice_fallback_url: The URL that we should call when an error occurs retrieving or executing the TwiML requested by `url`.
        :param voice_fallback_method: The HTTP method we should use to call `voice_fallback_url`. Can be: `GET` or `POST`.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application.
        :param status_callback_method: The HTTP method we should use to call `status_callback`. Can be: `GET` or `POST`.
        :param voice_caller_id_lookup: Whether we should look up the caller's caller-ID name from the CNAM database (additional charges apply). Can be: `true` or `false`.
        :param sms_url: The URL we should call when the phone number receives an incoming SMS message.
        :param sms_method: The HTTP method we should use to call `sms_url`. Can be: `GET` or `POST`.
        :param sms_fallback_url: The URL that we should call when an error occurs while retrieving or executing the TwiML from `sms_url`.
        :param sms_fallback_method: The HTTP method we should use to call `sms_fallback_url`. Can be: `GET` or `POST`.
        :param sms_status_callback: Same as message_status_callback: The URL we should call using a POST method to send status information about SMS messages sent by the application. Deprecated, included for backwards compatibility.
        :param message_status_callback: The URL we should call using a POST method to send message status information to your application.
        :param public_application_connect_enabled: Whether to allow other Twilio accounts to dial this applicaton using Dial verb. Can be: `true` or `false`.

        :returns: The updated ApplicationInstance
        """
        return await self._proxy.update_async(
            friendly_name=friendly_name,
            api_version=api_version,
            voice_url=voice_url,
            voice_method=voice_method,
            voice_fallback_url=voice_fallback_url,
            voice_fallback_method=voice_fallback_method,
            status_callback=status_callback,
            status_callback_method=status_callback_method,
            voice_caller_id_lookup=voice_caller_id_lookup,
            sms_url=sms_url,
            sms_method=sms_method,
            sms_fallback_url=sms_fallback_url,
            sms_fallback_method=sms_fallback_method,
            sms_status_callback=sms_status_callback,
            message_status_callback=message_status_callback,
            public_application_connect_enabled=public_application_connect_enabled,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.ApplicationInstance {}>".format(context)


class ApplicationContext(InstanceContext):

    def __init__(self, version: Version, account_sid: str, sid: str):
        """
        Initialize the ApplicationContext

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Application resources to update.
        :param sid: The Twilio-provided string that uniquely identifies the Application resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "sid": sid,
        }
        self._uri = "/Accounts/{account_sid}/Applications/{sid}.json".format(
            **self._solution
        )

    def delete(self) -> bool:
        """
        Deletes the ApplicationInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ApplicationInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> ApplicationInstance:
        """
        Fetch the ApplicationInstance


        :returns: The fetched ApplicationInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ApplicationInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> ApplicationInstance:
        """
        Asynchronous coroutine to fetch the ApplicationInstance


        :returns: The fetched ApplicationInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ApplicationInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        api_version: Union[str, object] = values.unset,
        voice_url: Union[str, object] = values.unset,
        voice_method: Union[str, object] = values.unset,
        voice_fallback_url: Union[str, object] = values.unset,
        voice_fallback_method: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        voice_caller_id_lookup: Union[bool, object] = values.unset,
        sms_url: Union[str, object] = values.unset,
        sms_method: Union[str, object] = values.unset,
        sms_fallback_url: Union[str, object] = values.unset,
        sms_fallback_method: Union[str, object] = values.unset,
        sms_status_callback: Union[str, object] = values.unset,
        message_status_callback: Union[str, object] = values.unset,
        public_application_connect_enabled: Union[bool, object] = values.unset,
    ) -> ApplicationInstance:
        """
        Update the ApplicationInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.
        :param api_version: The API version to use to start a new TwiML session. Can be: `2010-04-01` or `2008-08-01`. The default value is your account's default API version.
        :param voice_url: The URL we should call when the phone number assigned to this application receives a call.
        :param voice_method: The HTTP method we should use to call `voice_url`. Can be: `GET` or `POST`.
        :param voice_fallback_url: The URL that we should call when an error occurs retrieving or executing the TwiML requested by `url`.
        :param voice_fallback_method: The HTTP method we should use to call `voice_fallback_url`. Can be: `GET` or `POST`.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application.
        :param status_callback_method: The HTTP method we should use to call `status_callback`. Can be: `GET` or `POST`.
        :param voice_caller_id_lookup: Whether we should look up the caller's caller-ID name from the CNAM database (additional charges apply). Can be: `true` or `false`.
        :param sms_url: The URL we should call when the phone number receives an incoming SMS message.
        :param sms_method: The HTTP method we should use to call `sms_url`. Can be: `GET` or `POST`.
        :param sms_fallback_url: The URL that we should call when an error occurs while retrieving or executing the TwiML from `sms_url`.
        :param sms_fallback_method: The HTTP method we should use to call `sms_fallback_url`. Can be: `GET` or `POST`.
        :param sms_status_callback: Same as message_status_callback: The URL we should call using a POST method to send status information about SMS messages sent by the application. Deprecated, included for backwards compatibility.
        :param message_status_callback: The URL we should call using a POST method to send message status information to your application.
        :param public_application_connect_enabled: Whether to allow other Twilio accounts to dial this applicaton using Dial verb. Can be: `true` or `false`.

        :returns: The updated ApplicationInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "ApiVersion": api_version,
                "VoiceUrl": voice_url,
                "VoiceMethod": voice_method,
                "VoiceFallbackUrl": voice_fallback_url,
                "VoiceFallbackMethod": voice_fallback_method,
                "StatusCallback": status_callback,
                "StatusCallbackMethod": status_callback_method,
                "VoiceCallerIdLookup": serialize.boolean_to_string(
                    voice_caller_id_lookup
                ),
                "SmsUrl": sms_url,
                "SmsMethod": sms_method,
                "SmsFallbackUrl": sms_fallback_url,
                "SmsFallbackMethod": sms_fallback_method,
                "SmsStatusCallback": sms_status_callback,
                "MessageStatusCallback": message_status_callback,
                "PublicApplicationConnectEnabled": serialize.boolean_to_string(
                    public_application_connect_enabled
                ),
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ApplicationInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        api_version: Union[str, object] = values.unset,
        voice_url: Union[str, object] = values.unset,
        voice_method: Union[str, object] = values.unset,
        voice_fallback_url: Union[str, object] = values.unset,
        voice_fallback_method: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        voice_caller_id_lookup: Union[bool, object] = values.unset,
        sms_url: Union[str, object] = values.unset,
        sms_method: Union[str, object] = values.unset,
        sms_fallback_url: Union[str, object] = values.unset,
        sms_fallback_method: Union[str, object] = values.unset,
        sms_status_callback: Union[str, object] = values.unset,
        message_status_callback: Union[str, object] = values.unset,
        public_application_connect_enabled: Union[bool, object] = values.unset,
    ) -> ApplicationInstance:
        """
        Asynchronous coroutine to update the ApplicationInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.
        :param api_version: The API version to use to start a new TwiML session. Can be: `2010-04-01` or `2008-08-01`. The default value is your account's default API version.
        :param voice_url: The URL we should call when the phone number assigned to this application receives a call.
        :param voice_method: The HTTP method we should use to call `voice_url`. Can be: `GET` or `POST`.
        :param voice_fallback_url: The URL that we should call when an error occurs retrieving or executing the TwiML requested by `url`.
        :param voice_fallback_method: The HTTP method we should use to call `voice_fallback_url`. Can be: `GET` or `POST`.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application.
        :param status_callback_method: The HTTP method we should use to call `status_callback`. Can be: `GET` or `POST`.
        :param voice_caller_id_lookup: Whether we should look up the caller's caller-ID name from the CNAM database (additional charges apply). Can be: `true` or `false`.
        :param sms_url: The URL we should call when the phone number receives an incoming SMS message.
        :param sms_method: The HTTP method we should use to call `sms_url`. Can be: `GET` or `POST`.
        :param sms_fallback_url: The URL that we should call when an error occurs while retrieving or executing the TwiML from `sms_url`.
        :param sms_fallback_method: The HTTP method we should use to call `sms_fallback_url`. Can be: `GET` or `POST`.
        :param sms_status_callback: Same as message_status_callback: The URL we should call using a POST method to send status information about SMS messages sent by the application. Deprecated, included for backwards compatibility.
        :param message_status_callback: The URL we should call using a POST method to send message status information to your application.
        :param public_application_connect_enabled: Whether to allow other Twilio accounts to dial this applicaton using Dial verb. Can be: `true` or `false`.

        :returns: The updated ApplicationInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "ApiVersion": api_version,
                "VoiceUrl": voice_url,
                "VoiceMethod": voice_method,
                "VoiceFallbackUrl": voice_fallback_url,
                "VoiceFallbackMethod": voice_fallback_method,
                "StatusCallback": status_callback,
                "StatusCallbackMethod": status_callback_method,
                "VoiceCallerIdLookup": serialize.boolean_to_string(
                    voice_caller_id_lookup
                ),
                "SmsUrl": sms_url,
                "SmsMethod": sms_method,
                "SmsFallbackUrl": sms_fallback_url,
                "SmsFallbackMethod": sms_fallback_method,
                "SmsStatusCallback": sms_status_callback,
                "MessageStatusCallback": message_status_callback,
                "PublicApplicationConnectEnabled": serialize.boolean_to_string(
                    public_application_connect_enabled
                ),
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ApplicationInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.ApplicationContext {}>".format(context)


class ApplicationPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> ApplicationInstance:
        """
        Build an instance of ApplicationInstance

        :param payload: Payload response from the API
        """
        return ApplicationInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.ApplicationPage>"


class ApplicationList(ListResource):

    def __init__(self, version: Version, account_sid: str):
        """
        Initialize the ApplicationList

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Application resources to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
        }
        self._uri = "/Accounts/{account_sid}/Applications.json".format(**self._solution)

    def create(
        self,
        api_version: Union[str, object] = values.unset,
        voice_url: Union[str, object] = values.unset,
        voice_method: Union[str, object] = values.unset,
        voice_fallback_url: Union[str, object] = values.unset,
        voice_fallback_method: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        voice_caller_id_lookup: Union[bool, object] = values.unset,
        sms_url: Union[str, object] = values.unset,
        sms_method: Union[str, object] = values.unset,
        sms_fallback_url: Union[str, object] = values.unset,
        sms_fallback_method: Union[str, object] = values.unset,
        sms_status_callback: Union[str, object] = values.unset,
        message_status_callback: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        public_application_connect_enabled: Union[bool, object] = values.unset,
    ) -> ApplicationInstance:
        """
        Create the ApplicationInstance

        :param api_version: The API version to use to start a new TwiML session. Can be: `2010-04-01` or `2008-08-01`. The default value is the account's default API version.
        :param voice_url: The URL we should call when the phone number assigned to this application receives a call.
        :param voice_method: The HTTP method we should use to call `voice_url`. Can be: `GET` or `POST`.
        :param voice_fallback_url: The URL that we should call when an error occurs retrieving or executing the TwiML requested by `url`.
        :param voice_fallback_method: The HTTP method we should use to call `voice_fallback_url`. Can be: `GET` or `POST`.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application.
        :param status_callback_method: The HTTP method we should use to call `status_callback`. Can be: `GET` or `POST`.
        :param voice_caller_id_lookup: Whether we should look up the caller's caller-ID name from the CNAM database (additional charges apply). Can be: `true` or `false`.
        :param sms_url: The URL we should call when the phone number receives an incoming SMS message.
        :param sms_method: The HTTP method we should use to call `sms_url`. Can be: `GET` or `POST`.
        :param sms_fallback_url: The URL that we should call when an error occurs while retrieving or executing the TwiML from `sms_url`.
        :param sms_fallback_method: The HTTP method we should use to call `sms_fallback_url`. Can be: `GET` or `POST`.
        :param sms_status_callback: The URL we should call using a POST method to send status information about SMS messages sent by the application.
        :param message_status_callback: The URL we should call using a POST method to send message status information to your application.
        :param friendly_name: A descriptive string that you create to describe the new application. It can be up to 64 characters long.
        :param public_application_connect_enabled: Whether to allow other Twilio accounts to dial this applicaton using Dial verb. Can be: `true` or `false`.

        :returns: The created ApplicationInstance
        """

        data = values.of(
            {
                "ApiVersion": api_version,
                "VoiceUrl": voice_url,
                "VoiceMethod": voice_method,
                "VoiceFallbackUrl": voice_fallback_url,
                "VoiceFallbackMethod": voice_fallback_method,
                "StatusCallback": status_callback,
                "StatusCallbackMethod": status_callback_method,
                "VoiceCallerIdLookup": serialize.boolean_to_string(
                    voice_caller_id_lookup
                ),
                "SmsUrl": sms_url,
                "SmsMethod": sms_method,
                "SmsFallbackUrl": sms_fallback_url,
                "SmsFallbackMethod": sms_fallback_method,
                "SmsStatusCallback": sms_status_callback,
                "MessageStatusCallback": message_status_callback,
                "FriendlyName": friendly_name,
                "PublicApplicationConnectEnabled": serialize.boolean_to_string(
                    public_application_connect_enabled
                ),
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ApplicationInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    async def create_async(
        self,
        api_version: Union[str, object] = values.unset,
        voice_url: Union[str, object] = values.unset,
        voice_method: Union[str, object] = values.unset,
        voice_fallback_url: Union[str, object] = values.unset,
        voice_fallback_method: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        voice_caller_id_lookup: Union[bool, object] = values.unset,
        sms_url: Union[str, object] = values.unset,
        sms_method: Union[str, object] = values.unset,
        sms_fallback_url: Union[str, object] = values.unset,
        sms_fallback_method: Union[str, object] = values.unset,
        sms_status_callback: Union[str, object] = values.unset,
        message_status_callback: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        public_application_connect_enabled: Union[bool, object] = values.unset,
    ) -> ApplicationInstance:
        """
        Asynchronously create the ApplicationInstance

        :param api_version: The API version to use to start a new TwiML session. Can be: `2010-04-01` or `2008-08-01`. The default value is the account's default API version.
        :param voice_url: The URL we should call when the phone number assigned to this application receives a call.
        :param voice_method: The HTTP method we should use to call `voice_url`. Can be: `GET` or `POST`.
        :param voice_fallback_url: The URL that we should call when an error occurs retrieving or executing the TwiML requested by `url`.
        :param voice_fallback_method: The HTTP method we should use to call `voice_fallback_url`. Can be: `GET` or `POST`.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application.
        :param status_callback_method: The HTTP method we should use to call `status_callback`. Can be: `GET` or `POST`.
        :param voice_caller_id_lookup: Whether we should look up the caller's caller-ID name from the CNAM database (additional charges apply). Can be: `true` or `false`.
        :param sms_url: The URL we should call when the phone number receives an incoming SMS message.
        :param sms_method: The HTTP method we should use to call `sms_url`. Can be: `GET` or `POST`.
        :param sms_fallback_url: The URL that we should call when an error occurs while retrieving or executing the TwiML from `sms_url`.
        :param sms_fallback_method: The HTTP method we should use to call `sms_fallback_url`. Can be: `GET` or `POST`.
        :param sms_status_callback: The URL we should call using a POST method to send status information about SMS messages sent by the application.
        :param message_status_callback: The URL we should call using a POST method to send message status information to your application.
        :param friendly_name: A descriptive string that you create to describe the new application. It can be up to 64 characters long.
        :param public_application_connect_enabled: Whether to allow other Twilio accounts to dial this applicaton using Dial verb. Can be: `true` or `false`.

        :returns: The created ApplicationInstance
        """

        data = values.of(
            {
                "ApiVersion": api_version,
                "VoiceUrl": voice_url,
                "VoiceMethod": voice_method,
                "VoiceFallbackUrl": voice_fallback_url,
                "VoiceFallbackMethod": voice_fallback_method,
                "StatusCallback": status_callback,
                "StatusCallbackMethod": status_callback_method,
                "VoiceCallerIdLookup": serialize.boolean_to_string(
                    voice_caller_id_lookup
                ),
                "SmsUrl": sms_url,
                "SmsMethod": sms_method,
                "SmsFallbackUrl": sms_fallback_url,
                "SmsFallbackMethod": sms_fallback_method,
                "SmsStatusCallback": sms_status_callback,
                "MessageStatusCallback": message_status_callback,
                "FriendlyName": friendly_name,
                "PublicApplicationConnectEnabled": serialize.boolean_to_string(
                    public_application_connect_enabled
                ),
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ApplicationInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    def stream(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ApplicationInstance]:
        """
        Streams ApplicationInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str friendly_name: The string that identifies the Application resources to read.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(friendly_name=friendly_name, page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ApplicationInstance]:
        """
        Asynchronously streams ApplicationInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str friendly_name: The string that identifies the Application resources to read.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            friendly_name=friendly_name, page_size=limits["page_size"]
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ApplicationInstance]:
        """
        Lists ApplicationInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str friendly_name: The string that identifies the Application resources to read.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                friendly_name=friendly_name,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ApplicationInstance]:
        """
        Asynchronously lists ApplicationInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str friendly_name: The string that identifies the Application resources to read.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                friendly_name=friendly_name,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        friendly_name: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ApplicationPage:
        """
        Retrieve a single page of ApplicationInstance records from the API.
        Request is executed immediately

        :param friendly_name: The string that identifies the Application resources to read.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ApplicationInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ApplicationPage(self._version, response, self._solution)

    async def page_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ApplicationPage:
        """
        Asynchronously retrieve a single page of ApplicationInstance records from the API.
        Request is executed immediately

        :param friendly_name: The string that identifies the Application resources to read.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ApplicationInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ApplicationPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> ApplicationPage:
        """
        Retrieve a specific page of ApplicationInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ApplicationInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ApplicationPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> ApplicationPage:
        """
        Asynchronously retrieve a specific page of ApplicationInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ApplicationInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ApplicationPage(self._version, response, self._solution)

    def get(self, sid: str) -> ApplicationContext:
        """
        Constructs a ApplicationContext

        :param sid: The Twilio-provided string that uniquely identifies the Application resource to update.
        """
        return ApplicationContext(
            self._version, account_sid=self._solution["account_sid"], sid=sid
        )

    def __call__(self, sid: str) -> ApplicationContext:
        """
        Constructs a ApplicationContext

        :param sid: The Twilio-provided string that uniquely identifies the Application resource to update.
        """
        return ApplicationContext(
            self._version, account_sid=self._solution["account_sid"], sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.ApplicationList>"
