r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Api
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import date
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values

from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class ThisMonthInstance(InstanceResource):

    class Category(object):
        A2P_REGISTRATION_FEES = "a2p-registration-fees"
        AGENT_CONFERENCE = "agent-conference"
        AMAZON_POLLY = "amazon-polly"
        ANSWERING_MACHINE_DETECTION = "answering-machine-detection"
        AUTHY_AUTHENTICATIONS = "authy-authentications"
        AUTHY_CALLS_OUTBOUND = "authy-calls-outbound"
        AUTHY_MONTHLY_FEES = "authy-monthly-fees"
        AUTHY_PHONE_INTELLIGENCE = "authy-phone-intelligence"
        AUTHY_PHONE_VERIFICATIONS = "authy-phone-verifications"
        AUTHY_SMS_OUTBOUND = "authy-sms-outbound"
        CALL_PROGESS_EVENTS = "call-progess-events"
        CALLERIDLOOKUPS = "calleridlookups"
        CALLS = "calls"
        CALLS_CLIENT = "calls-client"
        CALLS_GLOBALCONFERENCE = "calls-globalconference"
        CALLS_INBOUND = "calls-inbound"
        CALLS_INBOUND_LOCAL = "calls-inbound-local"
        CALLS_INBOUND_MOBILE = "calls-inbound-mobile"
        CALLS_INBOUND_TOLLFREE = "calls-inbound-tollfree"
        CALLS_OUTBOUND = "calls-outbound"
        CALLS_PAY_VERB_TRANSACTIONS = "calls-pay-verb-transactions"
        CALLS_RECORDINGS = "calls-recordings"
        CALLS_SIP = "calls-sip"
        CALLS_SIP_INBOUND = "calls-sip-inbound"
        CALLS_SIP_OUTBOUND = "calls-sip-outbound"
        CALLS_TRANSFERS = "calls-transfers"
        CARRIER_LOOKUPS = "carrier-lookups"
        CONVERSATIONS = "conversations"
        CONVERSATIONS_API_REQUESTS = "conversations-api-requests"
        CONVERSATIONS_CONVERSATION_EVENTS = "conversations-conversation-events"
        CONVERSATIONS_ENDPOINT_CONNECTIVITY = "conversations-endpoint-connectivity"
        CONVERSATIONS_EVENTS = "conversations-events"
        CONVERSATIONS_PARTICIPANT_EVENTS = "conversations-participant-events"
        CONVERSATIONS_PARTICIPANTS = "conversations-participants"
        CPS = "cps"
        FLEX_USAGE = "flex-usage"
        FRAUD_LOOKUPS = "fraud-lookups"
        GROUP_ROOMS = "group-rooms"
        GROUP_ROOMS_DATA_TRACK = "group-rooms-data-track"
        GROUP_ROOMS_ENCRYPTED_MEDIA_RECORDED = "group-rooms-encrypted-media-recorded"
        GROUP_ROOMS_MEDIA_DOWNLOADED = "group-rooms-media-downloaded"
        GROUP_ROOMS_MEDIA_RECORDED = "group-rooms-media-recorded"
        GROUP_ROOMS_MEDIA_ROUTED = "group-rooms-media-routed"
        GROUP_ROOMS_MEDIA_STORED = "group-rooms-media-stored"
        GROUP_ROOMS_PARTICIPANT_MINUTES = "group-rooms-participant-minutes"
        GROUP_ROOMS_RECORDED_MINUTES = "group-rooms-recorded-minutes"
        IMP_V1_USAGE = "imp-v1-usage"
        IVR_VIRTUAL_AGENT_CUSTOM_VOICES = "ivr-virtual-agent-custom-voices"
        IVR_VIRTUAL_AGENT_GENAI = "ivr-virtual-agent-genai"
        LOOKUPS = "lookups"
        MARKETPLACE = "marketplace"
        MARKETPLACE_ALGORITHMIA_NAMED_ENTITY_RECOGNITION = (
            "marketplace-algorithmia-named-entity-recognition"
        )
        MARKETPLACE_CADENCE_TRANSCRIPTION = "marketplace-cadence-transcription"
        MARKETPLACE_CADENCE_TRANSLATION = "marketplace-cadence-translation"
        MARKETPLACE_CAPIO_SPEECH_TO_TEXT = "marketplace-capio-speech-to-text"
        MARKETPLACE_CONVRIZA_ABABA = "marketplace-convriza-ababa"
        MARKETPLACE_DEEPGRAM_PHRASE_DETECTOR = "marketplace-deepgram-phrase-detector"
        MARKETPLACE_DIGITAL_SEGMENT_BUSINESS_INFO = (
            "marketplace-digital-segment-business-info"
        )
        MARKETPLACE_FACEBOOK_OFFLINE_CONVERSIONS = (
            "marketplace-facebook-offline-conversions"
        )
        MARKETPLACE_GOOGLE_SPEECH_TO_TEXT = "marketplace-google-speech-to-text"
        MARKETPLACE_IBM_WATSON_MESSAGE_INSIGHTS = (
            "marketplace-ibm-watson-message-insights"
        )
        MARKETPLACE_IBM_WATSON_MESSAGE_SENTIMENT = (
            "marketplace-ibm-watson-message-sentiment"
        )
        MARKETPLACE_IBM_WATSON_RECORDING_ANALYSIS = (
            "marketplace-ibm-watson-recording-analysis"
        )
        MARKETPLACE_IBM_WATSON_TONE_ANALYZER = "marketplace-ibm-watson-tone-analyzer"
        MARKETPLACE_ICEHOOK_SYSTEMS_SCOUT = "marketplace-icehook-systems-scout"
        MARKETPLACE_INFOGROUP_DATAAXLE_BIZINFO = (
            "marketplace-infogroup-dataaxle-bizinfo"
        )
        MARKETPLACE_KEEN_IO_CONTACT_CENTER_ANALYTICS = (
            "marketplace-keen-io-contact-center-analytics"
        )
        MARKETPLACE_MARCHEX_CLEANCALL = "marketplace-marchex-cleancall"
        MARKETPLACE_MARCHEX_SENTIMENT_ANALYSIS_FOR_SMS = (
            "marketplace-marchex-sentiment-analysis-for-sms"
        )
        MARKETPLACE_MARKETPLACE_NEXTCALLER_SOCIAL_ID = (
            "marketplace-marketplace-nextcaller-social-id"
        )
        MARKETPLACE_MOBILE_COMMONS_OPT_OUT_CLASSIFIER = (
            "marketplace-mobile-commons-opt-out-classifier"
        )
        MARKETPLACE_NEXIWAVE_VOICEMAIL_TO_TEXT = (
            "marketplace-nexiwave-voicemail-to-text"
        )
        MARKETPLACE_NEXTCALLER_ADVANCED_CALLER_IDENTIFICATION = (
            "marketplace-nextcaller-advanced-caller-identification"
        )
        MARKETPLACE_NOMOROBO_SPAM_SCORE = "marketplace-nomorobo-spam-score"
        MARKETPLACE_PAYFONE_TCPA_COMPLIANCE = "marketplace-payfone-tcpa-compliance"
        MARKETPLACE_REMEETING_AUTOMATIC_SPEECH_RECOGNITION = (
            "marketplace-remeeting-automatic-speech-recognition"
        )
        MARKETPLACE_TCPA_DEFENSE_SOLUTIONS_BLACKLIST_FEED = (
            "marketplace-tcpa-defense-solutions-blacklist-feed"
        )
        MARKETPLACE_TELO_OPENCNAM = "marketplace-telo-opencnam"
        MARKETPLACE_TRUECNAM_TRUE_SPAM = "marketplace-truecnam-true-spam"
        MARKETPLACE_TWILIO_CALLER_NAME_LOOKUP_US = (
            "marketplace-twilio-caller-name-lookup-us"
        )
        MARKETPLACE_TWILIO_CARRIER_INFORMATION_LOOKUP = (
            "marketplace-twilio-carrier-information-lookup"
        )
        MARKETPLACE_VOICEBASE_PCI = "marketplace-voicebase-pci"
        MARKETPLACE_VOICEBASE_TRANSCRIPTION = "marketplace-voicebase-transcription"
        MARKETPLACE_VOICEBASE_TRANSCRIPTION_CUSTOM_VOCABULARY = (
            "marketplace-voicebase-transcription-custom-vocabulary"
        )
        MARKETPLACE_WHITEPAGES_PRO_CALLER_IDENTIFICATION = (
            "marketplace-whitepages-pro-caller-identification"
        )
        MARKETPLACE_WHITEPAGES_PRO_PHONE_INTELLIGENCE = (
            "marketplace-whitepages-pro-phone-intelligence"
        )
        MARKETPLACE_WHITEPAGES_PRO_PHONE_REPUTATION = (
            "marketplace-whitepages-pro-phone-reputation"
        )
        MARKETPLACE_WOLFARM_SPOKEN_RESULTS = "marketplace-wolfarm-spoken-results"
        MARKETPLACE_WOLFRAM_SHORT_ANSWER = "marketplace-wolfram-short-answer"
        MARKETPLACE_YTICA_CONTACT_CENTER_REPORTING_ANALYTICS = (
            "marketplace-ytica-contact-center-reporting-analytics"
        )
        MEDIASTORAGE = "mediastorage"
        MMS = "mms"
        MMS_INBOUND = "mms-inbound"
        MMS_INBOUND_LONGCODE = "mms-inbound-longcode"
        MMS_INBOUND_SHORTCODE = "mms-inbound-shortcode"
        MMS_MESSAGES_CARRIERFEES = "mms-messages-carrierfees"
        MMS_OUTBOUND = "mms-outbound"
        MMS_OUTBOUND_LONGCODE = "mms-outbound-longcode"
        MMS_OUTBOUND_SHORTCODE = "mms-outbound-shortcode"
        MONITOR_READS = "monitor-reads"
        MONITOR_STORAGE = "monitor-storage"
        MONITOR_WRITES = "monitor-writes"
        NOTIFY = "notify"
        NOTIFY_ACTIONS_ATTEMPTS = "notify-actions-attempts"
        NOTIFY_CHANNELS = "notify-channels"
        NUMBER_FORMAT_LOOKUPS = "number-format-lookups"
        PCHAT = "pchat"
        PCHAT_USERS = "pchat-users"
        PEER_TO_PEER_ROOMS_PARTICIPANT_MINUTES = (
            "peer-to-peer-rooms-participant-minutes"
        )
        PFAX = "pfax"
        PFAX_MINUTES = "pfax-minutes"
        PFAX_MINUTES_INBOUND = "pfax-minutes-inbound"
        PFAX_MINUTES_OUTBOUND = "pfax-minutes-outbound"
        PFAX_PAGES = "pfax-pages"
        PHONENUMBERS = "phonenumbers"
        PHONENUMBERS_CPS = "phonenumbers-cps"
        PHONENUMBERS_EMERGENCY = "phonenumbers-emergency"
        PHONENUMBERS_LOCAL = "phonenumbers-local"
        PHONENUMBERS_MOBILE = "phonenumbers-mobile"
        PHONENUMBERS_SETUPS = "phonenumbers-setups"
        PHONENUMBERS_TOLLFREE = "phonenumbers-tollfree"
        PREMIUMSUPPORT = "premiumsupport"
        PROXY = "proxy"
        PROXY_ACTIVE_SESSIONS = "proxy-active-sessions"
        PSTNCONNECTIVITY = "pstnconnectivity"
        PV = "pv"
        PV_COMPOSITION_MEDIA_DOWNLOADED = "pv-composition-media-downloaded"
        PV_COMPOSITION_MEDIA_ENCRYPTED = "pv-composition-media-encrypted"
        PV_COMPOSITION_MEDIA_STORED = "pv-composition-media-stored"
        PV_COMPOSITION_MINUTES = "pv-composition-minutes"
        PV_RECORDING_COMPOSITIONS = "pv-recording-compositions"
        PV_ROOM_PARTICIPANTS = "pv-room-participants"
        PV_ROOM_PARTICIPANTS_AU1 = "pv-room-participants-au1"
        PV_ROOM_PARTICIPANTS_BR1 = "pv-room-participants-br1"
        PV_ROOM_PARTICIPANTS_IE1 = "pv-room-participants-ie1"
        PV_ROOM_PARTICIPANTS_JP1 = "pv-room-participants-jp1"
        PV_ROOM_PARTICIPANTS_SG1 = "pv-room-participants-sg1"
        PV_ROOM_PARTICIPANTS_US1 = "pv-room-participants-us1"
        PV_ROOM_PARTICIPANTS_US2 = "pv-room-participants-us2"
        PV_ROOMS = "pv-rooms"
        PV_SIP_ENDPOINT_REGISTRATIONS = "pv-sip-endpoint-registrations"
        RECORDINGS = "recordings"
        RECORDINGSTORAGE = "recordingstorage"
        ROOMS_GROUP_BANDWIDTH = "rooms-group-bandwidth"
        ROOMS_GROUP_MINUTES = "rooms-group-minutes"
        ROOMS_PEER_TO_PEER_MINUTES = "rooms-peer-to-peer-minutes"
        SHORTCODES = "shortcodes"
        SHORTCODES_CUSTOMEROWNED = "shortcodes-customerowned"
        SHORTCODES_MMS_ENABLEMENT = "shortcodes-mms-enablement"
        SHORTCODES_MPS = "shortcodes-mps"
        SHORTCODES_RANDOM = "shortcodes-random"
        SHORTCODES_UK = "shortcodes-uk"
        SHORTCODES_VANITY = "shortcodes-vanity"
        SMALL_GROUP_ROOMS = "small-group-rooms"
        SMALL_GROUP_ROOMS_DATA_TRACK = "small-group-rooms-data-track"
        SMALL_GROUP_ROOMS_PARTICIPANT_MINUTES = "small-group-rooms-participant-minutes"
        SMS = "sms"
        SMS_INBOUND = "sms-inbound"
        SMS_INBOUND_LONGCODE = "sms-inbound-longcode"
        SMS_INBOUND_SHORTCODE = "sms-inbound-shortcode"
        SMS_MESSAGES_CARRIERFEES = "sms-messages-carrierfees"
        SMS_MESSAGES_FEATURES = "sms-messages-features"
        SMS_MESSAGES_FEATURES_SENDERID = "sms-messages-features-senderid"
        SMS_OUTBOUND = "sms-outbound"
        SMS_OUTBOUND_CONTENT_INSPECTION = "sms-outbound-content-inspection"
        SMS_OUTBOUND_LONGCODE = "sms-outbound-longcode"
        SMS_OUTBOUND_SHORTCODE = "sms-outbound-shortcode"
        SPEECH_RECOGNITION = "speech-recognition"
        STUDIO_ENGAGEMENTS = "studio-engagements"
        SYNC = "sync"
        SYNC_ACTIONS = "sync-actions"
        SYNC_ENDPOINT_HOURS = "sync-endpoint-hours"
        SYNC_ENDPOINT_HOURS_ABOVE_DAILY_CAP = "sync-endpoint-hours-above-daily-cap"
        TASKROUTER_TASKS = "taskrouter-tasks"
        TOTALPRICE = "totalprice"
        TRANSCRIPTIONS = "transcriptions"
        TRUNKING_CPS = "trunking-cps"
        TRUNKING_EMERGENCY_CALLS = "trunking-emergency-calls"
        TRUNKING_ORIGINATION = "trunking-origination"
        TRUNKING_ORIGINATION_LOCAL = "trunking-origination-local"
        TRUNKING_ORIGINATION_MOBILE = "trunking-origination-mobile"
        TRUNKING_ORIGINATION_TOLLFREE = "trunking-origination-tollfree"
        TRUNKING_RECORDINGS = "trunking-recordings"
        TRUNKING_SECURE = "trunking-secure"
        TRUNKING_TERMINATION = "trunking-termination"
        TTS_GOOGLE = "tts-google"
        TURNMEGABYTES = "turnmegabytes"
        TURNMEGABYTES_AUSTRALIA = "turnmegabytes-australia"
        TURNMEGABYTES_BRASIL = "turnmegabytes-brasil"
        TURNMEGABYTES_GERMANY = "turnmegabytes-germany"
        TURNMEGABYTES_INDIA = "turnmegabytes-india"
        TURNMEGABYTES_IRELAND = "turnmegabytes-ireland"
        TURNMEGABYTES_JAPAN = "turnmegabytes-japan"
        TURNMEGABYTES_SINGAPORE = "turnmegabytes-singapore"
        TURNMEGABYTES_USEAST = "turnmegabytes-useast"
        TURNMEGABYTES_USWEST = "turnmegabytes-uswest"
        TWILIO_INTERCONNECT = "twilio-interconnect"
        VERIFY_PUSH = "verify-push"
        VERIFY_TOTP = "verify-totp"
        VERIFY_WHATSAPP_CONVERSATIONS_BUSINESS_INITIATED = (
            "verify-whatsapp-conversations-business-initiated"
        )
        VIDEO_RECORDINGS = "video-recordings"
        VIRTUAL_AGENT = "virtual-agent"
        VOICE_INSIGHTS = "voice-insights"
        VOICE_INSIGHTS_CLIENT_INSIGHTS_ON_DEMAND_MINUTE = (
            "voice-insights-client-insights-on-demand-minute"
        )
        VOICE_INSIGHTS_PTSN_INSIGHTS_ON_DEMAND_MINUTE = (
            "voice-insights-ptsn-insights-on-demand-minute"
        )
        VOICE_INSIGHTS_SIP_INTERFACE_INSIGHTS_ON_DEMAND_MINUTE = (
            "voice-insights-sip-interface-insights-on-demand-minute"
        )
        VOICE_INSIGHTS_SIP_TRUNKING_INSIGHTS_ON_DEMAND_MINUTE = (
            "voice-insights-sip-trunking-insights-on-demand-minute"
        )
        VOICE_INTELLIGENCE = "voice-intelligence"
        VOICE_INTELLIGENCE_TRANSCRIPTION = "voice-intelligence-transcription"
        VOICE_INTELLIGENCE_OPERATORS = "voice-intelligence-operators"
        WIRELESS = "wireless"
        WIRELESS_ORDERS = "wireless-orders"
        WIRELESS_ORDERS_ARTWORK = "wireless-orders-artwork"
        WIRELESS_ORDERS_BULK = "wireless-orders-bulk"
        WIRELESS_ORDERS_ESIM = "wireless-orders-esim"
        WIRELESS_ORDERS_STARTER = "wireless-orders-starter"
        WIRELESS_USAGE = "wireless-usage"
        WIRELESS_USAGE_COMMANDS = "wireless-usage-commands"
        WIRELESS_USAGE_COMMANDS_AFRICA = "wireless-usage-commands-africa"
        WIRELESS_USAGE_COMMANDS_ASIA = "wireless-usage-commands-asia"
        WIRELESS_USAGE_COMMANDS_CENTRALANDSOUTHAMERICA = (
            "wireless-usage-commands-centralandsouthamerica"
        )
        WIRELESS_USAGE_COMMANDS_EUROPE = "wireless-usage-commands-europe"
        WIRELESS_USAGE_COMMANDS_HOME = "wireless-usage-commands-home"
        WIRELESS_USAGE_COMMANDS_NORTHAMERICA = "wireless-usage-commands-northamerica"
        WIRELESS_USAGE_COMMANDS_OCEANIA = "wireless-usage-commands-oceania"
        WIRELESS_USAGE_COMMANDS_ROAMING = "wireless-usage-commands-roaming"
        WIRELESS_USAGE_DATA = "wireless-usage-data"
        WIRELESS_USAGE_DATA_AFRICA = "wireless-usage-data-africa"
        WIRELESS_USAGE_DATA_ASIA = "wireless-usage-data-asia"
        WIRELESS_USAGE_DATA_CENTRALANDSOUTHAMERICA = (
            "wireless-usage-data-centralandsouthamerica"
        )
        WIRELESS_USAGE_DATA_CUSTOM_ADDITIONALMB = (
            "wireless-usage-data-custom-additionalmb"
        )
        WIRELESS_USAGE_DATA_CUSTOM_FIRST5MB = "wireless-usage-data-custom-first5mb"
        WIRELESS_USAGE_DATA_DOMESTIC_ROAMING = "wireless-usage-data-domestic-roaming"
        WIRELESS_USAGE_DATA_EUROPE = "wireless-usage-data-europe"
        WIRELESS_USAGE_DATA_INDIVIDUAL_ADDITIONALGB = (
            "wireless-usage-data-individual-additionalgb"
        )
        WIRELESS_USAGE_DATA_INDIVIDUAL_FIRSTGB = (
            "wireless-usage-data-individual-firstgb"
        )
        WIRELESS_USAGE_DATA_INTERNATIONAL_ROAMING_CANADA = (
            "wireless-usage-data-international-roaming-canada"
        )
        WIRELESS_USAGE_DATA_INTERNATIONAL_ROAMING_INDIA = (
            "wireless-usage-data-international-roaming-india"
        )
        WIRELESS_USAGE_DATA_INTERNATIONAL_ROAMING_MEXICO = (
            "wireless-usage-data-international-roaming-mexico"
        )
        WIRELESS_USAGE_DATA_NORTHAMERICA = "wireless-usage-data-northamerica"
        WIRELESS_USAGE_DATA_OCEANIA = "wireless-usage-data-oceania"
        WIRELESS_USAGE_DATA_POOLED = "wireless-usage-data-pooled"
        WIRELESS_USAGE_DATA_POOLED_DOWNLINK = "wireless-usage-data-pooled-downlink"
        WIRELESS_USAGE_DATA_POOLED_UPLINK = "wireless-usage-data-pooled-uplink"
        WIRELESS_USAGE_MRC = "wireless-usage-mrc"
        WIRELESS_USAGE_MRC_CUSTOM = "wireless-usage-mrc-custom"
        WIRELESS_USAGE_MRC_INDIVIDUAL = "wireless-usage-mrc-individual"
        WIRELESS_USAGE_MRC_POOLED = "wireless-usage-mrc-pooled"
        WIRELESS_USAGE_MRC_SUSPENDED = "wireless-usage-mrc-suspended"
        WIRELESS_USAGE_SMS = "wireless-usage-sms"
        WIRELESS_USAGE_VOICE = "wireless-usage-voice"

    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that accrued the usage.
    :ivar api_version: The API version used to create the resource.
    :ivar as_of: Usage records up to date as of this timestamp, formatted as YYYY-MM-DDTHH:MM:SS+00:00. All timestamps are in GMT
    :ivar category: 
    :ivar count: The number of usage events, such as the number of calls.
    :ivar count_unit: The units in which `count` is measured, such as `calls` for calls or `messages` for SMS.
    :ivar description: A plain-language description of the usage category.
    :ivar end_date: The last date for which usage is included in the UsageRecord. The date is specified in GMT and formatted as `YYYY-MM-DD`.
    :ivar price: The total price of the usage in the currency specified in `price_unit` and associated with the account.
    :ivar price_unit: The currency in which `price` is measured, in [ISO 4127](https://www.iso.org/iso/home/<USER>/currency_codes.htm) format, such as `usd`, `eur`, and `jpy`.
    :ivar start_date: The first date for which usage is included in this UsageRecord. The date is specified in GMT and formatted as `YYYY-MM-DD`.
    :ivar subresource_uris: A list of related resources identified by their URIs. For more information, see [List Subresources](https://www.twilio.com/docs/usage/api/usage-record#list-subresources).
    :ivar uri: The URI of the resource, relative to `https://api.twilio.com`.
    :ivar usage: The amount used to bill usage and measured in units described in `usage_unit`.
    :ivar usage_unit: The units in which `usage` is measured, such as `minutes` for calls or `messages` for SMS.
    """

    def __init__(self, version: Version, payload: Dict[str, Any], account_sid: str):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.api_version: Optional[str] = payload.get("api_version")
        self.as_of: Optional[str] = payload.get("as_of")
        self.category: Optional["ThisMonthInstance.Category"] = payload.get("category")
        self.count: Optional[str] = payload.get("count")
        self.count_unit: Optional[str] = payload.get("count_unit")
        self.description: Optional[str] = payload.get("description")
        self.end_date: Optional[date] = deserialize.iso8601_date(
            payload.get("end_date")
        )
        self.price: Optional[float] = deserialize.decimal(payload.get("price"))
        self.price_unit: Optional[str] = payload.get("price_unit")
        self.start_date: Optional[date] = deserialize.iso8601_date(
            payload.get("start_date")
        )
        self.subresource_uris: Optional[Dict[str, object]] = payload.get(
            "subresource_uris"
        )
        self.uri: Optional[str] = payload.get("uri")
        self.usage: Optional[str] = payload.get("usage")
        self.usage_unit: Optional[str] = payload.get("usage_unit")

        self._solution = {
            "account_sid": account_sid,
        }

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.ThisMonthInstance {}>".format(context)


class ThisMonthPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> ThisMonthInstance:
        """
        Build an instance of ThisMonthInstance

        :param payload: Payload response from the API
        """
        return ThisMonthInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.ThisMonthPage>"


class ThisMonthList(ListResource):

    def __init__(self, version: Version, account_sid: str):
        """
        Initialize the ThisMonthList

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the UsageRecord resources to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
        }
        self._uri = "/Accounts/{account_sid}/Usage/Records/ThisMonth.json".format(
            **self._solution
        )

    def stream(
        self,
        category: Union["ThisMonthInstance.Category", object] = values.unset,
        start_date: Union[date, object] = values.unset,
        end_date: Union[date, object] = values.unset,
        include_subaccounts: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ThisMonthInstance]:
        """
        Streams ThisMonthInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param &quot;ThisMonthInstance.Category&quot; category: The [usage category](https://www.twilio.com/docs/usage/api/usage-record#usage-categories) of the UsageRecord resources to read. Only UsageRecord resources in the specified category are retrieved.
        :param date start_date: Only include usage that has occurred on or after this date. Specify the date in GMT and format as `YYYY-MM-DD`. You can also specify offsets from the current date, such as: `-30days`, which will set the start date to be 30 days before the current date.
        :param date end_date: Only include usage that occurred on or before this date. Specify the date in GMT and format as `YYYY-MM-DD`.  You can also specify offsets from the current date, such as: `+30days`, which will set the end date to 30 days from the current date.
        :param bool include_subaccounts: Whether to include usage from the master account and all its subaccounts. Can be: `true` (the default) to include usage from the master account and all subaccounts or `false` to retrieve usage from only the specified account.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            category=category,
            start_date=start_date,
            end_date=end_date,
            include_subaccounts=include_subaccounts,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        category: Union["ThisMonthInstance.Category", object] = values.unset,
        start_date: Union[date, object] = values.unset,
        end_date: Union[date, object] = values.unset,
        include_subaccounts: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ThisMonthInstance]:
        """
        Asynchronously streams ThisMonthInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param &quot;ThisMonthInstance.Category&quot; category: The [usage category](https://www.twilio.com/docs/usage/api/usage-record#usage-categories) of the UsageRecord resources to read. Only UsageRecord resources in the specified category are retrieved.
        :param date start_date: Only include usage that has occurred on or after this date. Specify the date in GMT and format as `YYYY-MM-DD`. You can also specify offsets from the current date, such as: `-30days`, which will set the start date to be 30 days before the current date.
        :param date end_date: Only include usage that occurred on or before this date. Specify the date in GMT and format as `YYYY-MM-DD`.  You can also specify offsets from the current date, such as: `+30days`, which will set the end date to 30 days from the current date.
        :param bool include_subaccounts: Whether to include usage from the master account and all its subaccounts. Can be: `true` (the default) to include usage from the master account and all subaccounts or `false` to retrieve usage from only the specified account.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            category=category,
            start_date=start_date,
            end_date=end_date,
            include_subaccounts=include_subaccounts,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        category: Union["ThisMonthInstance.Category", object] = values.unset,
        start_date: Union[date, object] = values.unset,
        end_date: Union[date, object] = values.unset,
        include_subaccounts: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ThisMonthInstance]:
        """
        Lists ThisMonthInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param &quot;ThisMonthInstance.Category&quot; category: The [usage category](https://www.twilio.com/docs/usage/api/usage-record#usage-categories) of the UsageRecord resources to read. Only UsageRecord resources in the specified category are retrieved.
        :param date start_date: Only include usage that has occurred on or after this date. Specify the date in GMT and format as `YYYY-MM-DD`. You can also specify offsets from the current date, such as: `-30days`, which will set the start date to be 30 days before the current date.
        :param date end_date: Only include usage that occurred on or before this date. Specify the date in GMT and format as `YYYY-MM-DD`.  You can also specify offsets from the current date, such as: `+30days`, which will set the end date to 30 days from the current date.
        :param bool include_subaccounts: Whether to include usage from the master account and all its subaccounts. Can be: `true` (the default) to include usage from the master account and all subaccounts or `false` to retrieve usage from only the specified account.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                category=category,
                start_date=start_date,
                end_date=end_date,
                include_subaccounts=include_subaccounts,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        category: Union["ThisMonthInstance.Category", object] = values.unset,
        start_date: Union[date, object] = values.unset,
        end_date: Union[date, object] = values.unset,
        include_subaccounts: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ThisMonthInstance]:
        """
        Asynchronously lists ThisMonthInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param &quot;ThisMonthInstance.Category&quot; category: The [usage category](https://www.twilio.com/docs/usage/api/usage-record#usage-categories) of the UsageRecord resources to read. Only UsageRecord resources in the specified category are retrieved.
        :param date start_date: Only include usage that has occurred on or after this date. Specify the date in GMT and format as `YYYY-MM-DD`. You can also specify offsets from the current date, such as: `-30days`, which will set the start date to be 30 days before the current date.
        :param date end_date: Only include usage that occurred on or before this date. Specify the date in GMT and format as `YYYY-MM-DD`.  You can also specify offsets from the current date, such as: `+30days`, which will set the end date to 30 days from the current date.
        :param bool include_subaccounts: Whether to include usage from the master account and all its subaccounts. Can be: `true` (the default) to include usage from the master account and all subaccounts or `false` to retrieve usage from only the specified account.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                category=category,
                start_date=start_date,
                end_date=end_date,
                include_subaccounts=include_subaccounts,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        category: Union["ThisMonthInstance.Category", object] = values.unset,
        start_date: Union[date, object] = values.unset,
        end_date: Union[date, object] = values.unset,
        include_subaccounts: Union[bool, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ThisMonthPage:
        """
        Retrieve a single page of ThisMonthInstance records from the API.
        Request is executed immediately

        :param category: The [usage category](https://www.twilio.com/docs/usage/api/usage-record#usage-categories) of the UsageRecord resources to read. Only UsageRecord resources in the specified category are retrieved.
        :param start_date: Only include usage that has occurred on or after this date. Specify the date in GMT and format as `YYYY-MM-DD`. You can also specify offsets from the current date, such as: `-30days`, which will set the start date to be 30 days before the current date.
        :param end_date: Only include usage that occurred on or before this date. Specify the date in GMT and format as `YYYY-MM-DD`.  You can also specify offsets from the current date, such as: `+30days`, which will set the end date to 30 days from the current date.
        :param include_subaccounts: Whether to include usage from the master account and all its subaccounts. Can be: `true` (the default) to include usage from the master account and all subaccounts or `false` to retrieve usage from only the specified account.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ThisMonthInstance
        """
        data = values.of(
            {
                "Category": category,
                "StartDate": serialize.iso8601_date(start_date),
                "EndDate": serialize.iso8601_date(end_date),
                "IncludeSubaccounts": serialize.boolean_to_string(include_subaccounts),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ThisMonthPage(self._version, response, self._solution)

    async def page_async(
        self,
        category: Union["ThisMonthInstance.Category", object] = values.unset,
        start_date: Union[date, object] = values.unset,
        end_date: Union[date, object] = values.unset,
        include_subaccounts: Union[bool, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ThisMonthPage:
        """
        Asynchronously retrieve a single page of ThisMonthInstance records from the API.
        Request is executed immediately

        :param category: The [usage category](https://www.twilio.com/docs/usage/api/usage-record#usage-categories) of the UsageRecord resources to read. Only UsageRecord resources in the specified category are retrieved.
        :param start_date: Only include usage that has occurred on or after this date. Specify the date in GMT and format as `YYYY-MM-DD`. You can also specify offsets from the current date, such as: `-30days`, which will set the start date to be 30 days before the current date.
        :param end_date: Only include usage that occurred on or before this date. Specify the date in GMT and format as `YYYY-MM-DD`.  You can also specify offsets from the current date, such as: `+30days`, which will set the end date to 30 days from the current date.
        :param include_subaccounts: Whether to include usage from the master account and all its subaccounts. Can be: `true` (the default) to include usage from the master account and all subaccounts or `false` to retrieve usage from only the specified account.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ThisMonthInstance
        """
        data = values.of(
            {
                "Category": category,
                "StartDate": serialize.iso8601_date(start_date),
                "EndDate": serialize.iso8601_date(end_date),
                "IncludeSubaccounts": serialize.boolean_to_string(include_subaccounts),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ThisMonthPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> ThisMonthPage:
        """
        Retrieve a specific page of ThisMonthInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ThisMonthInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ThisMonthPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> ThisMonthPage:
        """
        Asynchronously retrieve a specific page of ThisMonthInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ThisMonthInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ThisMonthPage(self._version, response, self._solution)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.ThisMonthList>"
