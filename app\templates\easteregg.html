{% extends "base.html" %}

{% block title %}Membership Card{% endblock %}

{% block extra_css %}
<style>
  .card-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f9f9f9;
  }

  .membership-card {
    background: url('/static/img/membership_background.png') center/cover no-repeat;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    max-width: 600px;
    width: 100%;
    position: relative;
    overflow: hidden;
  }

  .membership-card .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.4); /* Adjusted opacity to make it brighter */
    border-radius: 10px;
    z-index: 1;
  }

  .membership-card .content {
    position: relative;
    z-index: 2;
    text-align: center;
  }

  .membership-card img {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 50%;
    border: 2px solid #ddd;
    margin-bottom: 15px;
  }

  .membership-card h5 {
    color: #222;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  }

  .membership-card .details {
    text-align: left; /* Align details to the left-hand side */
    margin-left: 20px; /* Add some spacing from the left edge */
    font-weight: 600;
    color: #222;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    margin-bottom: 15px;
  }

  .membership-card .details div {
    margin-bottom: 5px;
  }

  .membership-card p {
    font-size: 12px;
    color: #222;
    text-align: center;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
  }

  .action-buttons button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-family: "Dosis", sans-serif;
    font-size: 16px;
    text-transform: uppercase;
    transition: background-color 0.3s ease;
  }

  .action-buttons .download-btn {
    background-color: #4CAF50;
    color: white;
  }

  .action-buttons .download-btn:hover {
    background-color: #388E3C;
  }

  .action-buttons .share-btn {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
  }

  .action-buttons .share-btn:hover {
    background-color: #e0e0e0;
  }

  #membership-form {
    margin-top: 20px;
  }

  #membership-form .form-label {
    font-weight: 600;
    color: #222;
  }

  #membership-form .form-control {
    border-radius: 5px;
    border: 1px solid #ddd;
    margin-bottom: 15px;
  }

  #disclaimer {
    font-size: 12px;
    color: #222;
    text-align: center;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  }

.containerc {
  position: relative;
}

.containerc img {
  position: absolute;
  width: 100px;
  height: 100px;
  animation-direction: reverse;
  animation: moveToTop 2s ease-out 3s;
}

.containerc img:nth-child(1) {
  left: 10%;
  transform: rotate(15deg);
  animation-delay: 0s;
}
.containerc img:nth-child(2) {
  left: 30%;
  transform: rotate(-10deg);
  animation-delay: 0.2s;
}
.containerc img:nth-child(3) {
  left: 50%;
  transform: rotate(20deg);
  animation-delay: 0.4s;
}
.containerc img:nth-child(4) {
  left: 70%;
  transform: rotate(-15deg);
  animation-delay: 0.6s;
}
.containerc img:nth-child(5) {
  left: 90%;
  transform: rotate(30deg);
  animation-delay: 0.8s;
}
.containerc img:nth-child(6) {
  left: 20%;
  transform: rotate(-25deg);
  animation-delay: 1s;
}
.containerc img:nth-child(7) {
  left: 40%;
  transform: rotate(35deg);
  animation-delay: 1.2s;
}
.containerc img:nth-child(8) {
  left: 60%;
  transform: rotate(-30deg);
  animation-delay: 1.4s;
}
.containerc img:nth-child(9) {
  left: 80%;
  transform: rotate(40deg);
  animation-delay: 1.6s;
}
.containerc img:nth-child(10) {
  left: 50%;
  transform: rotate(-20deg);
  animation-delay: 1.8s;
}
.containerc img:nth-child(11) {
  left: 80%;
  transform: rotate(40deg);
  animation-delay: 1.6s;
}
.containerc img:nth-child(12) {
  left: 50%;
  transform: rotate(-20deg);
  animation-delay: 1.8s;
}
@keyframes moveToTop {
  0% {
    bottom: 600px;
  }
  100% {
    bottom: 0;
  }
}

</style>
{% endblock %}

{% block content %}

<h1 class="project-name">HELLO CYRIL MEMBERSHIP CARD</h1>
<div class="row justify-content-center">
  <div class="containerc">
      <img src="/static/img/cupcake1.png" alt="Image 1">
      <img src="/static/img/cupcake2.png" alt="Image 2">
      <img src="/static/img/cupcake3.png" alt="Image 3">
      <img src="/static/img/cupcake4.png" alt="Image 4">
      <img src="/static/img/cupcake2.png" alt="Image 5">
      <img src="/static/img/cupcake3.png" alt="Image 6">
      <img src="/static/img/cupcake4.png" alt="Image 7">
      <img src="/static/img/cupcake1.png" alt="Image 8">
      <img src="/static/img/cupcake3.png" alt="Image 9">
      <img src="/static/img/cupcake2.png" alt="Image 10">
      <img src="/static/img/cupcake1.png" alt="Image 11">
      <img src="/static/img/cupcake4.png" alt="Image 12">
  </div>

    <div class="col-md-8">
        <div class="membership-card">
            <div class="overlay"></div>
            <div class="content">
            <img id="profile-image" src="/static/img/profile_picture.jpg" alt="profile_image">
            <h5>Honorable Member</h5>
            <div class="details">
                <div><h6><strong>Membership no:</strong></h6> [Enter Membership Number]</div>
                <div><h6><strong>Province:</strong></h6> [Enter Province]</div>
                <div><h6><strong>Title:</strong></h6> [Enter Title]</div>
                <div><h6><strong>Age:</strong></h6> [Enter Age]</div>
                <div><h6><strong>Gender:</strong></h6> [Enter Gender]</div>
            </div>
            <p class="disclaimer"><strong>
                Official Hello Cyril VIP Pass is the property of the Hello Cyril Foundation, hotter than a Durban curry, and must be returned quicker than you can say “load-shedding schedule” if we come knocking. Lose it, and you’ll be banished to the back of the tender queue or, worse, barred from the VIP section at the next amapiano bash. Flash it wisely, and keep the gees high!</strong></p>
            </div>
        </div>
    </div>
    <!--This is an official Hello Cyril ID card, designated for use in all formal business dealings and transactions. This card is the property of the Hello Cyril Foundation and must be returned upon request. -->
    <div class="col-md-4">
        <div class="card shadow-sm">

            <div class="card-body">
                <form id="membership-form">
            <p>
            Official Hello Cyril VIP Pass. Greetings, esteemed cardholder!<br>
            This is no ordinary piece of plastic it’s the Hello Cyril ID Card, your golden ticket to swagger through all formal South African shindigs, braais, and high-stakes spaza shop negotiations. Issued by the illustrious Hello Cyril Foundation, this card screams, “I know people who know people!”
            </p>

                    <div class="mb-3">
                        <label for="profile-upload" class="form-label">Upload Profile Picture</label>
                        <input type="file" class="form-control" id="profile-upload" accept="image/*">
                    </div>
                    <div class="mb-3">
                        <label for="membership-number" class="form-label">Membership Number</label>
                        <input type="text" class="form-control" id="membership-number" placeholder="Enter Membership Number">
                    </div>
                    <div class="mb-3">
                        <label for="province" class="form-label">Province</label>
                        <select class="form-control" id="province">
                            <option value="">Select Province</option>
                            <option value="Eastern Cape">Eastern Cape</option>
                            <option value="Free State">Free State</option>
                            <option value="Gauteng">Gauteng</option>
                            <option value="KwaZulu-Natal">KwaZulu-Natal</option>
                            <option value="Limpopo">Limpopo</option>
                            <option value="Mpumalanga">Mpumalanga</option>
                            <option value="Northern Cape">Northern Cape</option>
                            <option value="North West">North West</option>
                            <option value="Western Cape">Western Cape</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="title" class="form-label">Title</label>
                        <select class="form-control" id="title">
                <option value="">Select Title</option>
                <option value="Minister of Load-Shedding Solutions and Candle Distribution ~
                Tasked with creatively explaining power outages and ensuring every household gets a free candle during blackouts.">Minister of Load-Shedding Solutions and Candle Distribution</option>
                <option value="Director-General of Braai Diplomacy ~
                Responsible for promoting national unity through perfectly timed braais and ensuring the correct ratio of boerewors to chops at state functions.">Director-General of Braai Diplomacy</option>
                <option value="Chief Vuvuzela Coordinator ~
                Ensures the correct use of vuvuzelas at public events and maintains eardrum safety.">Chief Vuvuzela Coordinator</option>
                <option value="Minister of Minibus Taxi Logistics ~
                Responsible for decoding the unwritten rules of taxi hand signals and queue etiquette.">Minister of Minibus Taxi Logistics</option>
                <option value="Minister of Load-Shedding Excuses ~
                Crafts creative reasons for power cuts and distributes solar-powered torches.">Minister of Load-Shedding Excuses</option>
                <option value="Director-General of Braai Diplomacy ~
                Ensures national unity through perfectly executed braais, with strict oversight on pap and sous ratios.">Director-General of Braai Diplomacy</option>
                <option value="Chief Vuvuzela Coordinator ~
                Regulates the use of vuvuzelas at public events to maintain eardrum safety.">Chief Vuvuzela Coordinator</option>
                <option value="Minister of Minibus Taxi Logistics ~
                Attempts to decode the unwritten rules of taxi hand signals and queue etiquette.">Minister of Minibus Taxi Logistics</option>
                <option value="National Bunny Chow Ambassador ~
                Promotes the perfect balance of curry, bread, and atchar in every province.">National Bunny Chow Ambassador</option>
                <option value="Secretary of Eskom Apology Tweets ~
                Composes heartfelt social media apologies for unscheduled blackouts.">Secretary of Eskom Apology Tweets</option>
                <option value="Gautrain Timetable Optimist ~
                Convinces commuters that the Gautrain will always run on time, despite evidence.">Gautrain Timetable Optimist</option>
                <option value="Cape Town Weather Czar ~
                Predicts whether it’ll be sunny, rainy, or windy in the next 10 minutes.">Cape Town Weather Czar</option>
                <option value="Minister of Biltong Standards ~
                Enforces strict quality control on biltong texture and spice levels nationwide.">Minister of Biltong Standards</option>
                <option value="Chief Spaza Shop Regulator ~
                Ensures every spaza shop stocks Oros and loose cigarettes in equal measure.">Chief Spaza Shop Regulator</option>
                <option value="Director of N1 Roadblock Entertainment ~
                Organizes live performances to keep drivers amused during holiday traffic jams.">Director of N1 Roadblock Entertainment</option>
                <option value="National Amapiano Playlist Curator ~
                Selects the perfect amapiano tracks for government functions to keep morale high.">National Amapiano Playlist Curator</option>
                <option value="Minister of Two-Ocean Aquarium Parking ~
                Solves the eternal mystery of finding a parking spot at Cape Town’s waterfront.">Minister of Two-Ocean Aquarium Parking</option>
                <option value="Commissioner of Koeksister Quality Control ~
                Ensures every koeksister is syrupy enough to stick to your fingers for days.">Commissioner of Koeksister Quality Control</option>
                <option value="Head of Mzansi Meme Oversight ~
                Approves only the funniest South African memes for official government WhatsApp groups.">Head of Mzansi Meme Oversight</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="age" class="form-label">Age</label>
                        <input type="number" class="form-control" id="age" placeholder="Enter Age">
                    </div>
                    <div class="mb-3">
                        <label for="gender" class="form-label">Gender</label>
                        <select class="form-control" id="gender">
                            <option value="">Select Gender</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>

                    <div class="action-buttons">
                        <button type="button" class="btn btn-outline-danger w-100" id="update-card">Update</button>
                        <button type="button" class="btn btn-outline-danger w-100" id="download-card">Download</button>
                        <button type="button" class="btn btn-outline-danger w-100" id="share-card">Share</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<!-- html2canvas library for downloading the card as image -->
<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script> -->
<script src="/static/js/html2canvas.min.js"></script>
<script>
  // Randomize background image
  const randomBackgroundIndex = Math.floor(Math.random() * 6) + 1;
  document.querySelector(".membership-card").style.background = `url('/static/img/membership_background${randomBackgroundIndex}.png') center/cover no-repeat`;

  // Download membership card as PNG
  document.getElementById("download-card").addEventListener("click", function () {
    const downloadBtn = this;
    const originalText = downloadBtn.textContent;

    // Show loading state
    downloadBtn.textContent = "Generating...";
    downloadBtn.disabled = true;

    const card = document.querySelector(".membership-card");

    // Configure html2canvas options for better quality
    const options = {
      backgroundColor: null,
      scale: 2, // Higher resolution
      useCORS: true, // Allow cross-origin images
      allowTaint: true,
      width: card.offsetWidth,
      height: card.offsetHeight
    };

    html2canvas(card, options).then(canvas => {
        const link = document.createElement("a");
        link.download = "hello-cyril-membership-card.png";
        link.href = canvas.toDataURL("image/png");
        link.click();

        // Reset button state
        downloadBtn.textContent = originalText;
        downloadBtn.disabled = false;

        // Show success message
        const successMsg = document.createElement("div");
        successMsg.className = "alert alert-success mt-2";
        successMsg.textContent = "Card downloaded successfully!";
        downloadBtn.parentNode.appendChild(successMsg);
        setTimeout(() => successMsg.remove(), 3000);

    }).catch(error => {
        console.error("Error generating the card image:", error);

        // Reset button state
        downloadBtn.textContent = originalText;
        downloadBtn.disabled = false;

        // Show error message
        const errorMsg = document.createElement("div");
        errorMsg.className = "alert alert-danger mt-2";
        errorMsg.textContent = "Failed to download card. Please try again.";
        downloadBtn.parentNode.appendChild(errorMsg);
        setTimeout(() => errorMsg.remove(), 5000);
    });
  });

  // Share membership card with image and website URL
  document.getElementById("share-card").addEventListener("click", function () {
    const shareBtn = this;
    const originalText = shareBtn.textContent;

    // Show loading state
    shareBtn.textContent = "Preparing...";
    shareBtn.disabled = true;

    const card = document.querySelector(".membership-card");

    // Configure html2canvas options for sharing
    const options = {
      backgroundColor: null,
      scale: 2,
      useCORS: true,
      allowTaint: true,
      width: card.offsetWidth,
      height: card.offsetHeight
    };

    html2canvas(card, options).then(canvas => {
      // Reset button state
      shareBtn.textContent = originalText;
      shareBtn.disabled = false;

      // Convert canvas to blob for sharing
      canvas.toBlob(function(blob) {
        const websiteUrl = "https://hellocyril.co.za/easteregg";
        const shareText = `Check out my Hello Cyril Membership Card! 🎉\n\nGet your own membership card at: ${websiteUrl}`;

        // Check if Web Share API is supported and can share files
        if (navigator.share && navigator.canShare && navigator.canShare({ files: [new File([blob], 'membership-card.png', { type: 'image/png' })] })) {
          // Use native Web Share API (works on mobile devices)
          const file = new File([blob], 'hello-cyril-membership-card.png', { type: 'image/png' });
          navigator.share({
            title: 'Hello Cyril Membership Card',
            text: shareText,
            files: [file]
          }).catch(err => console.log('Error sharing:', err));
        } else {
          // Fallback: Show modal with download + manual share instructions
          showShareModal(canvas, blob, shareText, websiteUrl);
        }
      }, 'image/png');

    }).catch(error => {
      console.error("Error generating the card image:", error);

      // Reset button state
      shareBtn.textContent = originalText;
      shareBtn.disabled = false;

      // Show error message
      const errorMsg = document.createElement("div");
      errorMsg.className = "alert alert-danger mt-2";
      errorMsg.textContent = "Failed to prepare card for sharing. Please try again.";
      shareBtn.parentNode.appendChild(errorMsg);
      setTimeout(() => errorMsg.remove(), 5000);
    });
  });

  // Function to show share modal with image preview and instructions
  function showShareModal(canvas, blob, shareText, websiteUrl) {
    const imageUrl = canvas.toDataURL('image/png');

    // WhatsApp share (text only, user will need to attach image manually)
    const whatsappMessage = encodeURIComponent(shareText);
    const whatsappUrl = `https://wa.me/?text=${whatsappMessage}`;

    // Twitter share (text only, user will need to attach image manually)
    const twitterMessage = encodeURIComponent(`Check out my Hello Cyril Membership Card! 🎉 Get yours at: ${websiteUrl} #HelloCyril #MembershipCard`);
    const twitterUrl = `https://twitter.com/intent/tweet?text=${twitterMessage}`;

    // Create and show share modal
    const shareModal = document.createElement("div");
    shareModal.className = "modal fade";
    shareModal.id = "shareModal";
    shareModal.innerHTML = `
      <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Share Your Membership Card</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-md-6">
                <h6>Your Card Image:</h6>
                <img src="${imageUrl}" class="img-fluid border rounded mb-3" alt="Membership Card">
                <div class="d-grid">
                  <button type="button" class="btn btn-outline-primary" onclick="downloadCardImage('${imageUrl}')">
                    <i class="bi bi-download"></i> Download Image
                  </button>
                </div>
              </div>
              <div class="col-md-6">
                <h6>Share Instructions:</h6>
                <div class="alert alert-info">
                  <small>
                    <strong>Step 1:</strong> Download the card image<br>
                    <strong>Step 2:</strong> Copy the message below<br>
                    <strong>Step 3:</strong> Share on your preferred platform
                  </small>
                </div>

                <div class="mb-3">
                  <label class="form-label"><strong>Share Message:</strong></label>
                  <textarea class="form-control" rows="4" readonly id="shareMessage">${shareText}</textarea>
                  <button type="button" class="btn btn-sm btn-outline-secondary mt-1" onclick="copyToClipboard(document.getElementById('shareMessage').value)">
                    <i class="bi bi-clipboard"></i> Copy Message
                  </button>
                </div>

                <div class="d-grid gap-2">
                  <a href="${whatsappUrl}" target="_blank" class="btn">
                    <i class="bi bi-whatsapp"></i> Open WhatsApp
                  </a>
                  <a href="${twitterUrl}" target="_blank" class="btn">
                    <i class="bi bi-twitter"></i> Open Twitter
                  </a>
                  <button type="button" class="btn" onclick="openFacebook('${websiteUrl}')">
                    <i class="bi bi-facebook"></i> Open Facebook
                  </button>
                  <button type="button" class="btn" onclick="openInstagram()">
                    <i class="bi bi-instagram"></i> Open Instagram
                  </button>
                </div>

                <hr>
                <small class="text-muted">Website: ${websiteUrl}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(shareModal);
    const modal = new bootstrap.Modal(shareModal);
    modal.show();

    // Clean up modal when hidden
    shareModal.addEventListener('hidden.bs.modal', function () {
      document.body.removeChild(shareModal);
    });
  }

  // Function to download card image
  function downloadCardImage(imageUrl) {
    const link = document.createElement("a");
    link.download = "hello-cyril-membership-card.png";
    link.href = imageUrl;
    link.click();
  }

  // Function to open Facebook
  function openFacebook(websiteUrl) {
    window.open(`https://www.facebook.com/`, '_blank');
  }

  // Function to open Instagram
  function openInstagram() {
    window.open(`https://www.instagram.com/`, '_blank');
  }

  // Function to copy text to clipboard
  function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
      // Show success message
      const successMsg = document.createElement("div");
      successMsg.className = "alert alert-success position-fixed top-0 start-50 translate-middle-x mt-3";
      successMsg.style.zIndex = "9999";
      successMsg.textContent = "Message copied to clipboard!";
      document.body.appendChild(successMsg);
      setTimeout(() => successMsg.remove(), 2000);
    }).catch(function(err) {
      console.error('Could not copy text: ', err);
      alert('Could not copy to clipboard. Please copy manually.');
    });
  }

  document.getElementById("profile-upload").addEventListener("change", function (event) {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function (e) {
        document.getElementById("profile-image").src = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  });

  document.getElementById("update-card").addEventListener("click", function () {
    const updateBtn = this;
    const originalText = updateBtn.textContent;

    try {
      // Get form values
      const membershipNumber = document.getElementById("membership-number").value;
      const province = document.getElementById("province").value;
      const title = document.getElementById("title").value;
      const age = document.getElementById("age").value;
      const gender = document.getElementById("gender").value;

      // Validate required fields
      if (!membershipNumber || !province || !title || !age || !gender) {
        // Show validation error
        const errorMsg = document.createElement("div");
        errorMsg.className = "alert alert-warning mt-2";
        errorMsg.textContent = "Please fill in all fields before updating the card.";
        updateBtn.parentNode.appendChild(errorMsg);
        setTimeout(() => errorMsg.remove(), 3000);
        return;
      }

      // Show loading state
      updateBtn.textContent = "Updating...";
      updateBtn.disabled = true;

      // Update card details with error handling
      const detailsContainer = document.querySelector(".details");
      if (detailsContainer) {
        const detailDivs = detailsContainer.querySelectorAll("div");

        if (detailDivs.length >= 5) {
          detailDivs[0].innerHTML = `<strong>Membership no:</strong> ${membershipNumber}`;
          detailDivs[1].innerHTML = `<strong>Province:</strong> ${province}`;
          detailDivs[2].innerHTML = `<strong>Title:</strong> ${title}`;
          detailDivs[3].innerHTML = `<strong>Age:</strong> ${age}`;
          detailDivs[4].innerHTML = `<strong>Gender:</strong> ${gender}`;

          // Reset button state
          updateBtn.textContent = originalText;
          updateBtn.disabled = false;

          // Show success message
          const successMsg = document.createElement("div");
          successMsg.className = "alert alert-success mt-2";
          successMsg.textContent = "Card updated successfully!";
          updateBtn.parentNode.appendChild(successMsg);
          setTimeout(() => successMsg.remove(), 3000);
        } else {
          throw new Error("Card details structure not found");
        }
      } else {
        throw new Error("Details container not found");
      }

    } catch (error) {
      console.error("Error updating card:", error);

      // Reset button state
      updateBtn.textContent = originalText;
      updateBtn.disabled = false;

      // Show error message
      const errorMsg = document.createElement("div");
      errorMsg.className = "alert alert-danger mt-2";
      errorMsg.textContent = "Failed to update card. Please try again.";
      updateBtn.parentNode.appendChild(errorMsg);
      setTimeout(() => errorMsg.remove(), 5000);
    }
  });

  // Generate a random 10-digit membership number
  const randomMembershipNumber = Math.floor(1000000000 + Math.random() * 9000000000);
  const membershipNumberInput = document.getElementById("membership-number");
  membershipNumberInput.value = randomMembershipNumber;
  membershipNumberInput.setAttribute("readonly", true); // Disable editing
</script>
{% endblock %}
