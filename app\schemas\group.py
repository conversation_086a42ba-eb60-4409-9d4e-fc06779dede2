from pydantic import BaseModel, Field
from typing import List, Optional, Tuple
from datetime import datetime
from uuid import UUID

from app.models.group import NotificationFrequency
from app.models.report import ReportCategory

class GroupBase(BaseModel):
    name: str
    description: Optional[str] = None
    admin_contact: str
    admin_phone: Optional[str] = None
    whatsapp_group_id: Optional[str] = None
    notification_frequency: NotificationFrequency = NotificationFrequency.REAL_TIME
    categories: List[ReportCategory] = []

class GroupCreate(GroupBase):
    # List of [longitude, latitude] coordinates defining the polygon boundary
    area_coordinates: List[Tuple[float, float]]

class GroupUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    admin_contact: Optional[str] = None
    admin_phone: Optional[str] = None
    whatsapp_group_id: Optional[str] = None
    notification_frequency: Optional[NotificationFrequency] = None
    categories: Optional[List[ReportCategory]] = None
    area_coordinates: Optional[List[Tuple[float, float]]] = None
    active: Optional[bool] = None

class GroupResponse(GroupBase):
    id: UUID
    created_at: datetime
    active: bool

    class Config:
        from_attributes = True

class GroupListResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str] = None
    notification_frequency: NotificationFrequency
    categories: List[ReportCategory]
    area_coordinates: List[Tuple[float, float]]

    class Config:
        from_attributes = True
