from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from typing import Dict, Any
from datetime import datetime, timezone

from app.models.database import get_db
from app.services.stats_service import StatsService

router = APIRouter()

@router.get("/")
def get_statistics(
    period: str = Query("overall", description="Period: 'overall' or 'monthly'"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Get comprehensive platform statistics"""

    try:
        stats_service = StatsService(db)
        # Pass the period to the service layer
        stats = stats_service.get_all_stats(period=period)

        # Metadata is already added by the service or can be ensured here
        if "period" not in stats:
            stats["period"] = period
        if "last_updated" not in stats:
            stats["last_updated"] = datetime.now(timezone.utc).isoformat()

        return stats

    except Exception as e:
        # Return basic fallback stats if there's an error
        print(f"Error getting stats: {e}")
        return {
            "total_reports": 0,
            "unique_users": 0,
            "community_groups": 0,
            "crime_reports": 0,
            "ems_reports": 0,
            "infrastructure_reports": 0,
            "messages_sent": 0,
            "messages_failed": 0,
            "total_cost": 0.0,
            "cost_per_message": 0.50,
            "success_rate": 100.0,
            "active_areas": 0,
            "avg_response_time": 15,
            "resolution_rate": 85,
            "peak_hours": "14:00",
            "time_labels": ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
            "time_data": [0, 0, 0, 0, 0, 0],
            "period": period,
            "last_updated": datetime.now(timezone.utc).isoformat(),
            "error": str(e)
        }


