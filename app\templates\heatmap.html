{% extends "base.html" %}

{% block title %}Map View - Hello Cyril 2.0{% endblock %}

{% block extra_css %}
<style>
    /* Basic styling for the map container */
    #map {
        height: 600px !important;
        width: 100% !important;
        border-radius: 5px;
        z-index: 1;
        border: 2px solid #ddd;
    }

    /* Styling for the container */
    .map-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    /* Legend styling */
    .legend-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .legend-item {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .legend-color {
        display: inline-block;
        width: 20px;
        height: 20px;
        margin-right: 10px;
        border-radius: 50%;
    }

    /* Time filter card styling */
    .time-filter-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .time-filter-container label {
        margin-bottom: 10px;
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<h1 class="project-name">INCIDENT MAP</h1>

<div class="container">
    <!-- Map Container -->
    <div class="row mb-3">

        <div class="col-md-10">
            <div class="map-container">
                <div id="map"></div>
            </div>
        </div>

        <div class="col-md-2">
            <div class="legend-container">
                <h5>Legend:</h5>
                <div class="legend-item">
                    <span class="legend-color" style="background-color: #dc3545;"></span>
                    <span>Crime</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color" style="background-color: #28a745;"></span>
                    <span>EMS</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color" style="background-color: #ffc107;"></span>
                    <span>Infrastructure</span>
                </div>
            </div>

            <!-- Time Filter Card -->
            <div class="time-filter-container">
                <h5>Filter:</h5>
                <label for="time-period">Select Time Period:</label>
                <select id="time-period" class="form-control">
                    <option value="1">Last 1 Hour</option>
                    <option value="6" selected>Last 6 Hour</option>
                    <option value="12">Last 12 Hour</option>
                    <option value="24">Last 24 Hours</option>
                    <option value="168">Last 7 Days</option>
                    <option value="720">Last 30 Days</option>
                    <option value="8760">Last 365 Days</option>
                </select>
            </div>
        </div>
        
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>

async function loadMessages() {
    try {
        fetch('/api/reports')
        .then(response => response.json())
        .then(data => {
            const reports = data; // Added 'const' to define 'reports'
            addReportMarkers(reports); // Renamed from addReportMarkers1
        })
        .catch(error => {
            console.error('Error fetching messages:', error); // Fixed error message
        });
    } catch (error) {
        console.error('Error fetching messages:', error);
    }
}

// Add report markers
function addReportMarkers(reports) {
    // Initialize counters for each category
    const categoryCounts = {
        crime: 0,
        EMS: 0,
        infrastructure: 0
    };

    for (const report of reports) {
        const latitude = report.location?.latitude ?? report.latitude;
        const longitude = report.location?.longitude ?? report.longitude;


        console.table(report);

        if (latitude && longitude) { // Ensure valid coordinates
            // Determine the color based on the category
            let color;
            switch (report.category) {
                case 'crime':
                    color = '#dc3545'; // Red
                    categoryCounts.crime++;
                    break;
                case 'ems':
                    color = '#28a745'; // Green
                    categoryCounts.EMS++;
                    break;
                case 'infrastructure':
                    color = '#ffc107'; // Yellow
                    categoryCounts.infrastructure++;
                    break;
                default:
                    color = '#007bff'; // Default (Blue)
            }

            // Format the timestamp
            const formattedTimestamp = report.timestamp 
                ? new Date(report.timestamp).toLocaleString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                }) 
                : 'Unknown';

            // Add the circle marker with the determined color
            L.circleMarker([latitude, longitude], {
                color: color,
                radius: 8 // Adjust the radius as needed
            }).addTo(map)
                .bindPopup(`
                    <strong>ID:</strong> #${report.short_id || report.id.substring(0, 8)}<br>
                    <strong>Report:</strong> ${report.category || 'Unknown'}<br>
                    <strong>Description:</strong> ${report.description || 'Unknown'}<br>
                    <strong>Timestamp:</strong> ${formattedTimestamp}<br>
                    <a href="https://www.google.com/maps?q=${latitude},${longitude}" target="_blank">Google Maps</a>
                `);
        } else {
            console.warn('Invalid report coordinates:', report);
        }
    }

    // Update the legend with the counts
    document.querySelector('.legend-container').innerHTML = `
        <h5>Legend:</h5>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #dc3545;"></span>
            <span>Crime (${categoryCounts.crime})</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #28a745;"></span>
            <span>EMS (${categoryCounts.EMS})</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #ffc107;"></span>
            <span>Infrastructure (${categoryCounts.infrastructure})</span>
        </div>
    `;
}

// Function to fetch and filter reports based on the selected time period
async function filterReportsByTime() {
    const timePeriod = document.getElementById('time-period').value;
    const now = new Date();
    const startDate = new Date(now.getTime() - timePeriod * 60 * 60 * 1000).toISOString();

    try {
        // Clear existing markers from the map
        if (typeof mapMarkers !== 'undefined') {
            mapMarkers.forEach(marker => map.removeLayer(marker));
        }
        mapMarkers = [];

        const response = await fetch(`/api/reports?start_date=${startDate}`);
        const reports = await response.json();
        addReportMarkers(reports);
    } catch (error) {
        console.error('Error fetching filtered reports:', error);
    }
}

// Initialize an array to store map markers
let mapMarkers = [];

// Add event listener to the time period dropdown
const timePeriodDropdown = document.getElementById('time-period');
timePeriodDropdown.addEventListener('change', filterReportsByTime);

// Initialize the map when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Check if Leaflet is available
    if (typeof L === 'undefined') {
        console.error("Leaflet is not loaded!");
        return;
    }

    // Check if map element exists
    const mapElement = document.getElementById('map');
    if (!mapElement) {
        console.error("Map element not found!");
        return;
    }

    try {
        // Create the map centered on South Africa
        map = L.map('map').setView([-29.5595, 22.9375], 5);

        // Add the tile layer (OpenStreetMap)
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Add a test marker to verify the map is working
        //L.circleMarker([-25.7461, 28.1881]).addTo(map)
        //    .bindPopup('Test marker 1 - Map is working!')
        //    .openPopup();
        //console.log("Test marker added");

        // Force map to resize after a short delay
        setTimeout(() => {
            map.invalidateSize();
        }, 500);

        // Load current reports
        loadMessages(); // Corrected function call

    } catch (error) {
        console.error("Error creating map:", error);
    }
});

</script>
{% endblock %}
