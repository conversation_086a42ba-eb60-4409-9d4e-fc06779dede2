{% extends "base.html" %}

{% block title %}Safety Heatmap - Hello <PERSON>{% endblock %}

{% block extra_css %}
<style>
    /* Basic styling for the map container */
    #map {
        height: 700px !important;
        width: 100% !important;
        border-radius: 8px;
        z-index: 1;
        border: 2px solid #ddd;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Styling for the container */
    .map-container {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        padding: 25px;
        margin-bottom: 25px;
    }

    /* Legend styling */
    .legend-container {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .legend-item {
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        font-weight: 500;
    }

    .legend-color {
        display: inline-block;
        width: 24px;
        height: 24px;
        margin-right: 12px;
        border-radius: 6px;
        border: 2px solid rgba(0,0,0,0.1);
    }

    /* Controls styling */
    .controls-container {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .controls-container label {
        margin-bottom: 8px;
        display: block;
        font-weight: 600;
        color: #333;
    }

    .controls-container select {
        margin-bottom: 15px;
    }

    /* Statistics styling */
    .stats-container {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }

    .stat-item:last-child {
        border-bottom: none;
    }

    .stat-label {
        font-weight: 500;
        color: #555;
    }

    .stat-value {
        font-weight: 700;
        font-size: 1.1em;
    }

    .stat-danger { color: #dc3545; }
    .stat-warning { color: #ffc107; }
    .stat-safe { color: #28a745; }

    /* Loading indicator */
    .loading {
        text-align: center;
        padding: 20px;
        color: #666;
    }

    .loading-spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #007bff;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Refresh button */
    .refresh-btn {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        width: 100%;
        margin-top: 10px;
    }

    .refresh-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    /* Zone popup styling */
    .zone-popup {
        max-width: 300px;
    }

    .zone-popup h6 {
        margin-bottom: 10px;
        color: #333;
        border-bottom: 2px solid #eee;
        padding-bottom: 5px;
    }

    .zone-popup .report-item {
        background: #f8f9fa;
        padding: 8px;
        margin: 5px 0;
        border-radius: 4px;
        border-left: 3px solid #007bff;
    }
</style>
{% endblock %}

{% block content %}
<h1 class="project-name">SOUTH AFRICA SAFETY HEATMAP</h1>

<div class="container-fluid">
    <div class="row">
        <!-- Map Container -->
        <div class="col-lg-9">
            <div class="map-container">
                <div id="loading" class="loading">
                    <div class="loading-spinner"></div>
                    <p>Loading safety data...</p>
                </div>
                <div id="map" style="display: none;"></div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Legend -->
            <div class="legend-container">
                <h5><i class="fas fa-map-marked-alt"></i> Safety Levels</h5>
                <div class="legend-item">
                    <span class="legend-color" style="background-color: #dc3545;"></span>
                    <span><strong>Danger Zone</strong><br><small>High crime activity</small></span>
                </div>
                <div class="legend-item">
                    <span class="legend-color" style="background-color: #ffc107;"></span>
                    <span><strong>Warning Zone</strong><br><small>Moderate activity</small></span>
                </div>
                <div class="legend-item">
                    <span class="legend-color" style="background-color: #28a745;"></span>
                    <span><strong>Safe Zone</strong><br><small>Low risk area</small></span>
                </div>
            </div>

            <!-- Controls -->
            <div class="controls-container">
                <h5><i class="fas fa-sliders-h"></i> Controls</h5>

                <label for="time-period">Time Period:</label>
                <select id="time-period" class="form-control">
                    <option value="1">Last 1 Hour</option>
                    <option value="6">Last 6 Hours</option>
                    <option value="12">Last 12 Hours</option>
                    <option value="24" selected>Last 24 Hours</option>
                    <option value="72">Last 3 Days</option>
                    <option value="168">Last 7 Days</option>
                    <option value="720">Last 30 Days</option>
                </select>

                <label for="grid-size">Detail Level:</label>
                <select id="grid-size" class="form-control">
                    <option value="0.05">Very High Detail</option>
                    <option value="0.1" selected>High Detail</option>
                    <option value="0.2">Medium Detail</option>
                    <option value="0.5">Low Detail</option>
                </select>

                <button id="refresh-btn" class="refresh-btn">
                    <i class="fas fa-sync-alt"></i> Refresh Data
                </button>
            </div>

            <!-- Statistics -->
            <div class="stats-container">
                <h5><i class="fas fa-chart-bar"></i> Statistics</h5>
                <div id="stats-content">
                    <div class="stat-item">
                        <span class="stat-label">Total Reports:</span>
                        <span class="stat-value" id="total-reports">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Danger Zones:</span>
                        <span class="stat-value stat-danger" id="danger-zones">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Warning Zones:</span>
                        <span class="stat-value stat-warning" id="warning-zones">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Safe Zones:</span>
                        <span class="stat-value stat-safe" id="safe-zones">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Crime Reports:</span>
                        <span class="stat-value stat-danger" id="crime-reports">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">EMS Reports:</span>
                        <span class="stat-value stat-safe" id="ems-reports">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Infrastructure:</span>
                        <span class="stat-value stat-warning" id="infrastructure-reports">-</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Global variables
let map;
let heatmapLayers = [];
let currentHeatmapData = null;

// Initialize the heatmap
async function initializeHeatmap() {
    console.log("Initializing heatmap...");

    try {
        // Check if Leaflet is available
        if (typeof L === 'undefined') {
            throw new Error("Leaflet is not loaded!");
        }

        // Create the map centered on South Africa
        map = L.map('map').setView([-29.0, 24.0], 6);

        // Add the tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 18
        }).addTo(map);

        // Hide loading indicator and show map
        document.getElementById('loading').style.display = 'none';
        document.getElementById('map').style.display = 'block';

        // Force map to resize
        setTimeout(() => {
            map.invalidateSize();
        }, 100);

        // Load initial heatmap data
        await loadHeatmapData();

        console.log("Heatmap initialized successfully");

    } catch (error) {
        console.error("Error initializing heatmap:", error);
        document.getElementById('loading').innerHTML = `
            <div class="alert alert-danger">
                <strong>Error:</strong> Failed to load heatmap. ${error.message}
            </div>
        `;
    }
}

// Load heatmap data from API
async function loadHeatmapData() {
    const timePeriod = document.getElementById('time-period').value;
    const gridSize = document.getElementById('grid-size').value;

    console.log(`Loading heatmap data: ${timePeriod} hours, grid size ${gridSize}`);

    try {
        // Show loading state
        showLoadingState();

        const response = await fetch(`/api/reports/heatmap?hours=${timePeriod}&grid_size=${gridSize}`);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        currentHeatmapData = data;

        console.log("Heatmap data loaded:", data);

        // Clear existing layers
        clearHeatmapLayers();

        // Add heatmap zones to map
        addHeatmapZones(data.zones);

        // Update statistics
        updateStatistics(data.statistics);

        hideLoadingState();

    } catch (error) {
        console.error("Error loading heatmap data:", error);
        hideLoadingState();

        // Show error message
        const errorMsg = document.createElement('div');
        errorMsg.className = 'alert alert-danger mt-2';
        errorMsg.innerHTML = `<strong>Error:</strong> ${error.message}`;
        document.getElementById('stats-content').prepend(errorMsg);
        setTimeout(() => errorMsg.remove(), 5000);
    }
}

// Add heatmap zones to the map
function addHeatmapZones(zones) {
    console.log(`Adding ${zones.length} heatmap zones to map`);

    zones.forEach(zone => {
        // Calculate zone size based on grid size
        const gridSize = parseFloat(document.getElementById('grid-size').value);
        const zoneSize = gridSize * 111000; // Convert degrees to meters (approximate)

        // Create circle for the zone
        const circle = L.circle([zone.lat, zone.lng], {
            color: zone.color,
            fillColor: zone.color,
            fillOpacity: 0.6,
            radius: zoneSize / 2,
            weight: 2
        });

        // Create popup content
        const popupContent = createZonePopup(zone);
        circle.bindPopup(popupContent);

        // Add to map and track
        circle.addTo(map);
        heatmapLayers.push(circle);
    });
}

// Create popup content for a zone
function createZonePopup(zone) {
    const dangerLevelText = {
        'danger': 'Danger Zone',
        'warning': 'Warning Zone',
        'safe': 'Safe Zone'
    };

    const dangerLevelClass = {
        'danger': 'stat-danger',
        'warning': 'stat-warning',
        'safe': 'stat-safe'
    };

    let recentReportsHtml = '';
    if (zone.recent_reports && zone.recent_reports.length > 0) {
        recentReportsHtml = '<h6>Recent Reports:</h6>';
        zone.recent_reports.forEach(report => {
            const reportDate = new Date(report.timestamp).toLocaleDateString();
            recentReportsHtml += `
                <div class="report-item">
                    <strong>${report.category}</strong><br>
                    <small>${report.description.substring(0, 50)}...</small><br>
                    <small class="text-muted">${reportDate}</small>
                </div>
            `;
        });
    }

    return `
        <div class="zone-popup">
            <h6 class="${dangerLevelClass[zone.danger_level]}">
                ${dangerLevelText[zone.danger_level]}
            </h6>
            <p><strong>Total Reports:</strong> ${zone.total_reports}</p>
            <p>
                <strong>Crime:</strong> ${zone.crime_reports} |
                <strong>EMS:</strong> ${zone.ems_reports} |
                <strong>Infrastructure:</strong> ${zone.infrastructure_reports}
            </p>
            ${recentReportsHtml}
        </div>
    `;
}

// Clear existing heatmap layers
function clearHeatmapLayers() {
    heatmapLayers.forEach(layer => {
        map.removeLayer(layer);
    });
    heatmapLayers = [];
}

// Update statistics display
function updateStatistics(stats) {
    document.getElementById('total-reports').textContent = stats.total_reports;
    document.getElementById('danger-zones').textContent = stats.danger_zones;
    document.getElementById('warning-zones').textContent = stats.warning_zones;
    document.getElementById('safe-zones').textContent = stats.safe_zones;
    document.getElementById('crime-reports').textContent = stats.crime_reports;
    document.getElementById('ems-reports').textContent = stats.ems_reports;
    document.getElementById('infrastructure-reports').textContent = stats.infrastructure_reports;
}

// Show loading state
function showLoadingState() {
    const refreshBtn = document.getElementById('refresh-btn');
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    refreshBtn.disabled = true;
}

// Hide loading state
function hideLoadingState() {
    const refreshBtn = document.getElementById('refresh-btn');
    refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Data';
    refreshBtn.disabled = false;
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Initialize heatmap
    initializeHeatmap();

    // Add event listeners for controls
    document.getElementById('time-period').addEventListener('change', loadHeatmapData);
    document.getElementById('grid-size').addEventListener('change', loadHeatmapData);
    document.getElementById('refresh-btn').addEventListener('click', loadHeatmapData);
});

</script>
{% endblock %}
