/* Base styles from example_html */
@import "https://fonts.googleapis.com/css?family=Dosis:300,400,500,600,700";

#timeline .timeline-item:after, header:after, #timeline .timeline-item:before, header:before {
  content: "";
  display: block;
  width: 100%;
  clear: both;
}

*, *:before, *:after {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

body, html {
  height: 100%;
}

body {
  background: #f9f9f9;
  background-size: cover;
  margin: 0;
  padding: 0;
  font-family: helvetica, arial, tahoma, verdana;
  line-height: 20px;
  font-size: 14px;
  color: #726f77;
}

img {
  max-width: 100%;
}

a {
  text-decoration: none;
}

.container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 0 20px;
}

h1, h2, h3, h4 {
  font-family: "Dosis", arial, tahoma, verdana;
  font-weight: 500;
}

.project-name {
  text-align: center;
  padding: 10px 0;
}

header {
  background: #2b2e48;
  padding: 10px;
  -webkit-box-shadow: 0 3px 3px rgba(0, 0, 0, 0.05);
  -moz-box-shadow: 0 3px 3px rgba(0, 0, 0, 0.05);
  -ms-box-shadow: 0 3px 3px rgba(0, 0, 0, 0.05);
  box-shadow: 0 3px 3px rgba(0, 0, 0, 0.05);
}

header .logo {
  color: #ee4d4d;
  float: left;
  font-family: "Dosis", arial, tahoma, verdana;
  font-size: 22px;
  font-weight: 500;
  margin: 10px 0;
}

header .logo > span {
  color: #f7aaaa;
  font-weight: 300;
}

header .social {
  float: right;
}

header .social .btn {
  font-family: "Dosis";
  font-size: 14px;
  margin: 10px 5px;
}

/* Timeline styles from example_html */
#timeline {
  width: 100%;
  margin: 30px auto;
  position: relative;
  padding: 0 10px;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

#timeline:before {
  content: "";
  width: 3px;
  height: 100%;
  background: #ee4d4d;
  left: 50%;
  top: 0;
  position: absolute;
}

#timeline:after {
  content: "";
  clear: both;
  display: table;
  width: 100%;
}

#timeline .timeline-item {
  margin-bottom: 50px;
  position: relative;
}

#timeline .timeline-item .timeline-icon {
  background: #ee4d4d;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 0;
  left: 50%;
  overflow: hidden;
  margin-left: -23px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}

#timeline .timeline-item .timeline-icon svg {
  position: relative;
  top: 14px;
  left: 14px;
}

#timeline .timeline-item .timeline-content {
  width: 45%;
  background: #fff;
  padding: 20px;
  -webkit-box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
  -ms-box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  border-radius: 5px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

#timeline .timeline-item .timeline-content h2 {
  padding: 15px;
  background: #ee4d4d;
  color: #fff;
  margin: -20px -20px 0 -20px;
  font-weight: 300;
  -webkit-border-radius: 3px 3px 0 0;
  -moz-border-radius: 3px 3px 0 0;
  -ms-border-radius: 3px 3px 0 0;
  border-radius: 3px 3px 0 0;
}

#timeline .timeline-item .timeline-content:before {
  content: "";
  position: absolute;
  left: 45%;
  top: 20px;
  width: 0;
  height: 0;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
  border-left: 7px solid #ee4d4d;
}

#timeline .timeline-item .timeline-content.right {
  float: right;
}

#timeline .timeline-item .timeline-content.right:before {
  content: "";
  right: 45%;
  left: inherit;
  border-left: 0;
  border-right: 7px solid #ee4d4d;
}

/* Button styles from example_html */
.btn {
  padding: 5px 15px;
  text-decoration: none;
  background: transparent;
  border: 2px solid #f27c7c;
  color: #f27c7c;
  display: inline-block;
  position: relative;
  text-transform: uppercase;
  font-size: 12px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  border-radius: 5px;
  -webkit-transition: background 0.3s ease;
  -moz-transition: background 0.3s ease;
  -ms-transition: background 0.3s ease;
  transition: background 0.3s ease;
  -webkit-box-shadow: 2px 2px 0 #f27c7c;
  -moz-box-shadow: 2px 2px 0 #f27c7c;
  -ms-box-shadow: 2px 2px 0 #f27c7c;
  box-shadow: 2px 2px 0 #f27c7c;
}

.btn:hover {
  box-shadow: none;
  top: 2px;
  left: 2px;
  -webkit-box-shadow: 2px 2px 0 transparent;
  -moz-box-shadow: 2px 2px 0 transparent;
  -ms-box-shadow: 2px 2px 0 transparent;
  box-shadow: 2px 2px 0 transparent;
}

/* Map styles */
#map-section {
  margin: 50px 0;
}

#map {
  height: 500px;
  width: 100%;
  border-radius: 5px;
  box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
}

.filter-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.filter-group {
  display: flex;
  align-items: center;
}

.filter-group label {
  margin-right: 10px;
  font-weight: bold;
}

.filter-group select {
  padding: 5px 10px;
  border-radius: 5px;
  border: 1px solid #ddd;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
  #timeline {
    margin: 30px;
    padding: 0px;
    width: 90%;
  }
  
  #timeline:before {
    left: 0;
  }
  
  #timeline .timeline-item .timeline-content {
    width: 90%;
    float: right;
  }
  
  #timeline .timeline-item .timeline-content:before, 
  #timeline .timeline-item .timeline-content.right:before {
    left: 10%;
    margin-left: -6px;
    border-left: 0;
    border-right: 7px solid #ee4d4d;
  }
  
  #timeline .timeline-item .timeline-icon {
    left: 0;
  }
  
  .filter-controls {
    flex-direction: column;
  }
  
  .filter-group {
    margin-bottom: 10px;
  }
}
