r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Marketplace
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, List, Optional, Union
from twilio.base import values

from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class ModuleDataInstance(InstanceResource):
    """
    :ivar url: URL to query the subresource.
    :ivar sid: ModuleSid that identifies this Listing.
    :ivar description: A JSON object describing the module and is displayed under the Description tab of the Module detail page. You can define the main body of the description, highlight key features or aspects of the module and if applicable, provide code samples for developers
    :ivar support: A JSON object containing information on how customers can obtain support for the module. Use this parameter to provide details such as contact information and support description.
    :ivar policies: A JSON object describing the module's privacy and legal policies and is displayed under the Policies tab of the Module detail page. The maximum file size for Policies is 5MB
    :ivar module_info: A JSON object containing essential attributes that define a module. This information is presented on the Module detail page in the Twilio Marketplace Catalog. You can pass the following attributes in the JSON object
    :ivar documentation: A JSON object for providing comprehensive information, instructions, and resources related to the module
    :ivar configuration: A JSON object for providing listing specific configuration. Contains button setup, notification url, among others.
    :ivar pricing: A JSON object for providing Listing specific pricing information.
    :ivar listings:
    """

    def __init__(self, version: Version, payload: Dict[str, Any]):
        super().__init__(version)

        self.url: Optional[str] = payload.get("url")
        self.sid: Optional[str] = payload.get("sid")
        self.description: Optional[Dict[str, object]] = payload.get("description")
        self.support: Optional[Dict[str, object]] = payload.get("support")
        self.policies: Optional[Dict[str, object]] = payload.get("policies")
        self.module_info: Optional[Dict[str, object]] = payload.get("module_info")
        self.documentation: Optional[Dict[str, object]] = payload.get("documentation")
        self.configuration: Optional[Dict[str, object]] = payload.get("configuration")
        self.pricing: Optional[Dict[str, object]] = payload.get("pricing")
        self.listings: Optional[List[Dict[str, object]]] = payload.get("listings")

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Marketplace.V1.ModuleDataInstance>"


class ModuleDataList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the ModuleDataList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Listings"

    def create(
        self,
        module_info: Union[str, object] = values.unset,
        configuration: Union[str, object] = values.unset,
    ) -> ModuleDataInstance:
        """
        Create the ModuleDataInstance

        :param module_info: A JSON object containing essential attributes that define a Listing.
        :param configuration: A JSON object for providing Listing-specific configuration. Contains button setup, notification URL, and more.

        :returns: The created ModuleDataInstance
        """

        data = values.of(
            {
                "ModuleInfo": module_info,
                "Configuration": configuration,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ModuleDataInstance(self._version, payload)

    async def create_async(
        self,
        module_info: Union[str, object] = values.unset,
        configuration: Union[str, object] = values.unset,
    ) -> ModuleDataInstance:
        """
        Asynchronously create the ModuleDataInstance

        :param module_info: A JSON object containing essential attributes that define a Listing.
        :param configuration: A JSON object for providing Listing-specific configuration. Contains button setup, notification URL, and more.

        :returns: The created ModuleDataInstance
        """

        data = values.of(
            {
                "ModuleInfo": module_info,
                "Configuration": configuration,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ModuleDataInstance(self._version, payload)

    def fetch(self) -> ModuleDataInstance:
        """
        Asynchronously fetch the ModuleDataInstance


        :returns: The fetched ModuleDataInstance
        """
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ModuleDataInstance(self._version, payload)

    async def fetch_async(self) -> ModuleDataInstance:
        """
        Asynchronously fetch the ModuleDataInstance


        :returns: The fetched ModuleDataInstance
        """
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ModuleDataInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Marketplace.V1.ModuleDataList>"
