from sqlalchemy import Column, String, Text, Boolean, DateTime, Enum
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, timezone
import enum
import json

from app.models.database import Base

class NotificationFrequency(str, enum.Enum):
    REAL_TIME = "real_time"
    HOURLY = "hourly"
    DAILY = "daily"

class CommunityGroup(Base):
    __tablename__ = "community_groups"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    admin_contact = Column(String, nullable=False)
    admin_phone = Column(String, nullable=True)  # New field for admin phone number
    whatsapp_group_id = Column(String, nullable=True)
    # Store area boundary as a JSON string of coordinates
    area_json = Column(Text, nullable=False)
    # Store the WKT representation as a string
    area_wkt = Column(String, nullable=True)
    # Notification preferences
    notification_frequency = Column(Enum(NotificationFrequency), default=NotificationFrequency.REAL_TIME)
    # Store categories as a JSON string
    categories_json = Column(Text, nullable=True)
    # Additional fields
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    active = Column(Boolean, default=True)

    @property
    def area_coordinates(self):
        """Get the area coordinates as a list of [lon, lat] pairs"""
        if self.area_json:
            return json.loads(self.area_json)
        return []

    @area_coordinates.setter
    def area_coordinates(self, coordinates):
        """Set the area coordinates from a list of [lon, lat] pairs"""
        self.area_json = json.dumps(coordinates)

        # Also update the WKT representation
        if coordinates:
            # Make sure the polygon is closed
            if coordinates[0] != coordinates[-1]:
                coordinates.append(coordinates[0])

            # Create WKT string
            points_str = ", ".join([f"{point[0]} {point[1]}" for point in coordinates])
            self.area_wkt = f"POLYGON(({points_str}))"

    @property
    def categories(self):
        """Get the categories as a list of category names"""
        if self.categories_json:
            return json.loads(self.categories_json)
        return []

    @categories.setter
    def categories(self, categories):
        """Set the categories from a list of category names or enum values"""
        # Handle both strings and enum values
        category_names = []
        for category in categories:
            if hasattr(category, 'value'):
                category_names.append(category.value)
            else:
                category_names.append(str(category))
        self.categories_json = json.dumps(category_names)

    def __repr__(self):
        return f"<CommunityGroup(id={self.id}, name={self.name})>"
