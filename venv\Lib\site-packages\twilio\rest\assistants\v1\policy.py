r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Assistants
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values

from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class PolicyInstance(InstanceResource):
    """
    :ivar id: The Policy ID.
    :ivar name: The name of the policy.
    :ivar description: The description of the policy.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Policy resource.
    :ivar user_sid: The SID of the User that created the Policy resource.
    :ivar type: The type of the policy.
    :ivar policy_details: The details of the policy based on the type.
    :ivar date_created: The date and time in GMT when the Policy was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the Policy was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    """

    def __init__(self, version: Version, payload: Dict[str, Any]):
        super().__init__(version)

        self.id: Optional[str] = payload.get("id")
        self.name: Optional[str] = payload.get("name")
        self.description: Optional[str] = payload.get("description")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.user_sid: Optional[str] = payload.get("user_sid")
        self.type: Optional[str] = payload.get("type")
        self.policy_details: Optional[Dict[str, object]] = payload.get("policy_details")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Assistants.V1.PolicyInstance>"


class PolicyPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> PolicyInstance:
        """
        Build an instance of PolicyInstance

        :param payload: Payload response from the API
        """
        return PolicyInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Assistants.V1.PolicyPage>"


class PolicyList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the PolicyList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Policies"

    def stream(
        self,
        tool_id: Union[str, object] = values.unset,
        knowledge_id: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[PolicyInstance]:
        """
        Streams PolicyInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str tool_id: The tool ID.
        :param str knowledge_id: The knowledge ID.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            tool_id=tool_id, knowledge_id=knowledge_id, page_size=limits["page_size"]
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        tool_id: Union[str, object] = values.unset,
        knowledge_id: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[PolicyInstance]:
        """
        Asynchronously streams PolicyInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str tool_id: The tool ID.
        :param str knowledge_id: The knowledge ID.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            tool_id=tool_id, knowledge_id=knowledge_id, page_size=limits["page_size"]
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        tool_id: Union[str, object] = values.unset,
        knowledge_id: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[PolicyInstance]:
        """
        Lists PolicyInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str tool_id: The tool ID.
        :param str knowledge_id: The knowledge ID.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                tool_id=tool_id,
                knowledge_id=knowledge_id,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        tool_id: Union[str, object] = values.unset,
        knowledge_id: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[PolicyInstance]:
        """
        Asynchronously lists PolicyInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str tool_id: The tool ID.
        :param str knowledge_id: The knowledge ID.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                tool_id=tool_id,
                knowledge_id=knowledge_id,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        tool_id: Union[str, object] = values.unset,
        knowledge_id: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> PolicyPage:
        """
        Retrieve a single page of PolicyInstance records from the API.
        Request is executed immediately

        :param tool_id: The tool ID.
        :param knowledge_id: The knowledge ID.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of PolicyInstance
        """
        data = values.of(
            {
                "ToolId": tool_id,
                "KnowledgeId": knowledge_id,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return PolicyPage(self._version, response)

    async def page_async(
        self,
        tool_id: Union[str, object] = values.unset,
        knowledge_id: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> PolicyPage:
        """
        Asynchronously retrieve a single page of PolicyInstance records from the API.
        Request is executed immediately

        :param tool_id: The tool ID.
        :param knowledge_id: The knowledge ID.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of PolicyInstance
        """
        data = values.of(
            {
                "ToolId": tool_id,
                "KnowledgeId": knowledge_id,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return PolicyPage(self._version, response)

    def get_page(self, target_url: str) -> PolicyPage:
        """
        Retrieve a specific page of PolicyInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of PolicyInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return PolicyPage(self._version, response)

    async def get_page_async(self, target_url: str) -> PolicyPage:
        """
        Asynchronously retrieve a specific page of PolicyInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of PolicyInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return PolicyPage(self._version, response)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Assistants.V1.PolicyList>"
