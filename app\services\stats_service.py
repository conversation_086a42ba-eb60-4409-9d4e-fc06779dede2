from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import datetime, timezone
from typing import Dict, Any

from app.models.stats import PlatformStats
from app.models.report import Report, ReportCategory
from app.models.group import CommunityGroup

class StatsService:
    def __init__(self, db: Session):
        self.db = db

    def increment_stat(self, stat_name: str, amount: int = 1) -> None:
        """Increment an integer statistic"""
        stat = self.db.query(PlatformStats).filter(
            PlatformStats.stat_name == stat_name
        ).first()

        if not stat:
            stat = PlatformStats(
                stat_name=stat_name,
                int_value=amount
            )
            self.db.add(stat)
        else:
            stat.int_value += amount
            stat.last_updated = datetime.now(timezone.utc)

        self.db.commit()

    def add_cost(self, stat_name: str, cost: float) -> None:
        """Add to a cost statistic"""
        stat = self.db.query(PlatformStats).filter(
            PlatformStats.stat_name == stat_name
        ).first()

        if not stat:
            stat = PlatformStats(
                stat_name=stat_name,
                float_value=cost
            )
            self.db.add(stat)
        else:
            stat.float_value += cost
            stat.last_updated = datetime.now(timezone.utc)

        self.db.commit()

    def get_stat_value(self, stat_name: str, default_int: int = 0, default_float: float = 0.0) -> Dict[str, Any]:
        """Get a statistic value"""
        stat = self.db.query(PlatformStats).filter(
            PlatformStats.stat_name == stat_name
        ).first()

        if not stat:
            return {
                "int_value": default_int,
                "float_value": default_float,
                "string_value": None
            }

        return {
            "int_value": stat.int_value or default_int,
            "float_value": stat.float_value or default_float,
            "string_value": stat.string_value
        }

    def record_message_sent(self, cost: float = 0.50) -> None:
        """Record that a message was sent"""
        self.increment_stat("total_messages_sent")
        self.increment_stat("monthly_messages_sent")
        self.add_cost("total_message_cost", cost)
        self.add_cost("monthly_message_cost", cost)

    def record_message_failed(self) -> None:
        """Record that a message failed"""
        self.increment_stat("total_messages_failed")
        self.increment_stat("monthly_messages_failed")

    def get_all_stats(self, period: str = "overall") -> Dict[str, Any]:
        """Get comprehensive statistics based on the period"""

        # Get basic counts from database (these are typically overall)
        total_reports = self.db.query(func.count(Report.id)).scalar() or 0
        unique_users = self.db.query(func.count(func.distinct(Report.user_hash))).scalar() or 0
        community_groups = self.db.query(CommunityGroup.id).filter(
            CommunityGroup.active == True
        ).count() or 0 # Use .count() for SQLAlchemy 2.0+ style, or keep scalar() if that's the intended pattern

        # Get category counts (typically overall, unless period filtering is added here too)
        crime_reports = self.db.query(func.count(Report.id)).filter(
            Report.category == ReportCategory.CRIME
        ).scalar() or 0

        ems_reports = self.db.query(func.count(Report.id)).filter(
            Report.category == ReportCategory.EMS
        ).scalar() or 0

        infrastructure_reports = self.db.query(func.count(Report.id)).filter(
            Report.category == ReportCategory.INFRASTRUCTURE
        ).scalar() or 0

        # Determine which stat prefixes to use based on the period
        if period == "monthly":
            messages_sent_stat_name = "monthly_messages_sent"
            messages_failed_stat_name = "monthly_messages_failed"
            message_cost_stat_name = "monthly_message_cost"
        else: # Default to overall
            messages_sent_stat_name = "total_messages_sent"
            messages_failed_stat_name = "total_messages_failed"
            message_cost_stat_name = "total_message_cost"

        messages_sent = self.get_stat_value(messages_sent_stat_name)["int_value"]
        messages_failed = self.get_stat_value(messages_failed_stat_name)["int_value"]
        current_period_cost = self.get_stat_value(message_cost_stat_name)["float_value"]

        # Calculate derived metrics for the current period
        total_messages_current_period = messages_sent + messages_failed
        success_rate_current_period = (messages_sent / total_messages_current_period * 100) if total_messages_current_period > 0 else 100

        cost_per_message = 0.50 # This is a fixed rate

        # For "overall" period, we can still do the sanity check for total_cost
        if period == "overall":
            expected_total_cost = messages_sent * cost_per_message
            if abs(current_period_cost - expected_total_cost) > 0.01:
                print(f"Warning: Total cost mismatch. Expected: R{expected_total_cost:.2f}, Actual: R{current_period_cost:.2f}")
                # current_period_cost = expected_total_cost # Decide if override is desired

        # Active areas (simplified calculation - typically overall)
        try:
            active_areas_count = self.db.query(
                func.count(func.distinct(Report.latitude, Report.longitude)) # Consider distinct pairs
            ).scalar() or 0
        except Exception:
            active_areas_count = max(1, total_reports // 10)

        # Placeholder for chart data - this would also need period-specific logic
        time_labels_data = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"] # Static for now
        time_series_data = [45, 67, 89, 123, 156, 178] # Static for now
        if period == "monthly":
            # TODO: Implement logic to fetch/generate monthly chart data
            # For now, let's return empty or last month's data as placeholder
            time_series_data = [10,12,5,20,15,3]


        return {
            # Core metrics (mostly overall, but could be adapted for period if needed)
            "total_reports": total_reports, # This remains overall
            "unique_users": unique_users, # This remains overall
            "community_groups": community_groups, # This remains overall

            # Category breakdown (overall, unless period filtering is added to their queries)
            "crime_reports": crime_reports,
            "ems_reports": ems_reports,
            "infrastructure_reports": infrastructure_reports,

            # Message metrics for the selected period
            "messages_sent": messages_sent,
            "messages_failed": messages_failed,
            "total_cost": current_period_cost, # Renamed from total_cost to reflect period
            "cost_per_message": cost_per_message, # Fixed rate
            "success_rate": round(success_rate_current_period, 1),

            # Additional metrics (some are estimates/overall)
            "active_areas": active_areas_count, # Overall
            "avg_response_time": 15,  # Estimated
            "resolution_rate": 85,    # Estimated
            "peak_hours": "14:00",    # Estimated

            # Chart data
            "time_labels": time_labels_data,
            "time_data": time_series_data,
            
            "period": period,
            "last_updated": datetime.now(timezone.utc).isoformat()
        }

    def reset_monthly_stats(self) -> None:
        """Reset monthly statistics (call this at the start of each month)"""
        monthly_stats = [
            "monthly_messages_sent",
            "monthly_messages_failed",
            "monthly_message_cost"
        ]

        for stat_name in monthly_stats:
            stat = self.db.query(PlatformStats).filter(
                PlatformStats.stat_name == stat_name
            ).first()

            if stat:
                stat.int_value = 0
                stat.float_value = 0.0
                stat.last_updated = datetime.now(timezone.utc)
            else:
                stat = PlatformStats(
                    stat_name=stat_name,
                    int_value=0,
                    float_value=0.0
                )
                self.db.add(stat)

        self.db.commit()
