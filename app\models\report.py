from sqlalchemy import Column, String, Text, Boolean, DateTime, Enum, Float
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, timezone
import enum
import hashlib

from app.models.database import Base

class ReportCategory(str, enum.Enum):
    CRIME = "crime"
    EMS = "ems"
    INFRASTRUCTURE = "infrastructure"

class Report(Base):
    __tablename__ = "reports"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    short_id = Column(String(8), nullable=False, index=True, unique=True)
    category = Column(Enum(ReportCategory), nullable=False)
    description = Column(Text, nullable=False)
    image_url = Column(Text, nullable=True)
    # Keep latitude and longitude for easy access
    latitude = Column(Float, nullable=False)
    longitude = Column(Float, nullable=False)
    # Store the WKT representation as a string instead of using PostGIS
    location_wkt = Column(String, nullable=True)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    user_hash = Column(String, nullable=False)
    verified = Column(Boolean, default=False)

    @staticmethod
    def generate_short_id():
        """Generate a short ID for user-friendly reference"""
        return uuid.uuid4().hex[:8]

    @staticmethod
    def hash_phone_number(phone_number):
        """Hash a phone number for anonymity"""
        return hashlib.sha256(phone_number.encode()).hexdigest()[:8]

    def __repr__(self):
        return f"<Report(id={self.id}, category={self.category})>"
