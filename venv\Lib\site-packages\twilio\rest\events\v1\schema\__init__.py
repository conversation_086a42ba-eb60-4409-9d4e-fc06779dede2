r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Events
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, Optional
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version

from twilio.rest.events.v1.schema.schema_version import SchemaVersionList


class SchemaInstance(InstanceResource):
    """
    :ivar id: The unique identifier of the schema. Each schema can have multiple versions, that share the same id.
    :ivar url: The URL of this resource.
    :ivar links: Contains a dictionary of URL links to nested resources of this schema.
    :ivar latest_version_date_created: The date that the latest schema version was created, given in ISO 8601 format.
    :ivar latest_version: The latest version published of this schema.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], id: Optional[str] = None
    ):
        super().__init__(version)

        self.id: Optional[str] = payload.get("id")
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")
        self.latest_version_date_created: Optional[datetime] = (
            deserialize.iso8601_datetime(payload.get("latest_version_date_created"))
        )
        self.latest_version: Optional[int] = deserialize.integer(
            payload.get("latest_version")
        )

        self._solution = {
            "id": id or self.id,
        }
        self._context: Optional[SchemaContext] = None

    @property
    def _proxy(self) -> "SchemaContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: SchemaContext for this SchemaInstance
        """
        if self._context is None:
            self._context = SchemaContext(
                self._version,
                id=self._solution["id"],
            )
        return self._context

    def fetch(self) -> "SchemaInstance":
        """
        Fetch the SchemaInstance


        :returns: The fetched SchemaInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "SchemaInstance":
        """
        Asynchronous coroutine to fetch the SchemaInstance


        :returns: The fetched SchemaInstance
        """
        return await self._proxy.fetch_async()

    @property
    def versions(self) -> SchemaVersionList:
        """
        Access the versions
        """
        return self._proxy.versions

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Events.V1.SchemaInstance {}>".format(context)


class SchemaContext(InstanceContext):

    def __init__(self, version: Version, id: str):
        """
        Initialize the SchemaContext

        :param version: Version that contains the resource
        :param id: The unique identifier of the schema. Each schema can have multiple versions, that share the same id.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "id": id,
        }
        self._uri = "/Schemas/{id}".format(**self._solution)

        self._versions: Optional[SchemaVersionList] = None

    def fetch(self) -> SchemaInstance:
        """
        Fetch the SchemaInstance


        :returns: The fetched SchemaInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return SchemaInstance(
            self._version,
            payload,
            id=self._solution["id"],
        )

    async def fetch_async(self) -> SchemaInstance:
        """
        Asynchronous coroutine to fetch the SchemaInstance


        :returns: The fetched SchemaInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return SchemaInstance(
            self._version,
            payload,
            id=self._solution["id"],
        )

    @property
    def versions(self) -> SchemaVersionList:
        """
        Access the versions
        """
        if self._versions is None:
            self._versions = SchemaVersionList(
                self._version,
                self._solution["id"],
            )
        return self._versions

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Events.V1.SchemaContext {}>".format(context)


class SchemaList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the SchemaList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self, id: str) -> SchemaContext:
        """
        Constructs a SchemaContext

        :param id: The unique identifier of the schema. Each schema can have multiple versions, that share the same id.
        """
        return SchemaContext(self._version, id=id)

    def __call__(self, id: str) -> SchemaContext:
        """
        Constructs a SchemaContext

        :param id: The unique identifier of the schema. Each schema can have multiple versions, that share the same id.
        """
        return SchemaContext(self._version, id=id)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Events.V1.SchemaList>"
