r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Accounts
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, Optional
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class AuthTokenPromotionInstance(InstanceResource):
    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that the secondary Auth Token was created for.
    :ivar auth_token: The promoted Auth Token that must be used to authenticate future API requests.
    :ivar date_created: The date and time in UTC when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The URI for this resource, relative to `https://accounts.twilio.com`
    """

    def __init__(self, version: Version, payload: Dict[str, Any]):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.auth_token: Optional[str] = payload.get("auth_token")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.url: Optional[str] = payload.get("url")

        self._context: Optional[AuthTokenPromotionContext] = None

    @property
    def _proxy(self) -> "AuthTokenPromotionContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: AuthTokenPromotionContext for this AuthTokenPromotionInstance
        """
        if self._context is None:
            self._context = AuthTokenPromotionContext(
                self._version,
            )
        return self._context

    def update(self) -> "AuthTokenPromotionInstance":
        """
        Update the AuthTokenPromotionInstance


        :returns: The updated AuthTokenPromotionInstance
        """
        return self._proxy.update()

    async def update_async(self) -> "AuthTokenPromotionInstance":
        """
        Asynchronous coroutine to update the AuthTokenPromotionInstance


        :returns: The updated AuthTokenPromotionInstance
        """
        return await self._proxy.update_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Accounts.V1.AuthTokenPromotionInstance>"


class AuthTokenPromotionContext(InstanceContext):

    def __init__(self, version: Version):
        """
        Initialize the AuthTokenPromotionContext

        :param version: Version that contains the resource
        """
        super().__init__(version)

        self._uri = "/AuthTokens/Promote"

    def update(self) -> AuthTokenPromotionInstance:
        """
        Update the AuthTokenPromotionInstance


        :returns: The updated AuthTokenPromotionInstance
        """

        data = values.of({})
        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return AuthTokenPromotionInstance(self._version, payload)

    async def update_async(self) -> AuthTokenPromotionInstance:
        """
        Asynchronous coroutine to update the AuthTokenPromotionInstance


        :returns: The updated AuthTokenPromotionInstance
        """

        data = values.of({})
        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return AuthTokenPromotionInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Accounts.V1.AuthTokenPromotionContext>"


class AuthTokenPromotionList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the AuthTokenPromotionList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self) -> AuthTokenPromotionContext:
        """
        Constructs a AuthTokenPromotionContext

        """
        return AuthTokenPromotionContext(self._version)

    def __call__(self) -> AuthTokenPromotionContext:
        """
        Constructs a AuthTokenPromotionContext

        """
        return AuthTokenPromotionContext(self._version)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Accounts.V1.AuthTokenPromotionList>"
