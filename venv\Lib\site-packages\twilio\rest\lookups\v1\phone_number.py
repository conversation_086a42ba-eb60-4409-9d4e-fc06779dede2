r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Lookups
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, List, Optional, Union
from twilio.base import serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class PhoneNumberInstance(InstanceResource):
    """
    :ivar caller_name: The name of the phone number's owner. If `null`, that information was not available.
    :ivar country_code: The [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) for the phone number.
    :ivar phone_number: The phone number in [E.164](https://www.twilio.com/docs/glossary/what-e164) format, which consists of a + followed by the country code and subscriber number.
    :ivar national_format: The phone number, in national format.
    :ivar carrier: The telecom company that provides the phone number.
    :ivar add_ons: A JSON string with the results of the Add-ons you specified in the `add_ons` parameters. For the format of the object, see [Using Add-ons](https://www.twilio.com/docs/add-ons).
    :ivar url: The absolute URL of the resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        phone_number: Optional[str] = None,
    ):
        super().__init__(version)

        self.caller_name: Optional[Dict[str, object]] = payload.get("caller_name")
        self.country_code: Optional[str] = payload.get("country_code")
        self.phone_number: Optional[str] = payload.get("phone_number")
        self.national_format: Optional[str] = payload.get("national_format")
        self.carrier: Optional[Dict[str, object]] = payload.get("carrier")
        self.add_ons: Optional[Dict[str, object]] = payload.get("add_ons")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "phone_number": phone_number or self.phone_number,
        }
        self._context: Optional[PhoneNumberContext] = None

    @property
    def _proxy(self) -> "PhoneNumberContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: PhoneNumberContext for this PhoneNumberInstance
        """
        if self._context is None:
            self._context = PhoneNumberContext(
                self._version,
                phone_number=self._solution["phone_number"],
            )
        return self._context

    def fetch(
        self,
        country_code: Union[str, object] = values.unset,
        type: Union[List[str], object] = values.unset,
        add_ons: Union[List[str], object] = values.unset,
        add_ons_data: Union[Dict[str, object], object] = values.unset,
    ) -> "PhoneNumberInstance":
        """
        Fetch the PhoneNumberInstance

        :param country_code: The [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the phone number to fetch. This is used to specify the country when the phone number is provided in a national format.
        :param type: The type of information to return. Can be: `carrier` or `caller-name`. The default is null. To retrieve both types of information, specify this parameter twice; once with `carrier` and once with `caller-name` as the value.
        :param add_ons: The `unique_name` of an Add-on you would like to invoke. Can be the `unique_name` of an Add-on that is installed on your account. You can specify multiple instances of this parameter to invoke multiple Add-ons. For more information about  Add-ons, see the [Add-ons documentation](https://www.twilio.com/docs/add-ons).
        :param add_ons_data: Data specific to the add-on you would like to invoke. The content and format of this value depends on the add-on.

        :returns: The fetched PhoneNumberInstance
        """
        return self._proxy.fetch(
            country_code=country_code,
            type=type,
            add_ons=add_ons,
            add_ons_data=add_ons_data,
        )

    async def fetch_async(
        self,
        country_code: Union[str, object] = values.unset,
        type: Union[List[str], object] = values.unset,
        add_ons: Union[List[str], object] = values.unset,
        add_ons_data: Union[Dict[str, object], object] = values.unset,
    ) -> "PhoneNumberInstance":
        """
        Asynchronous coroutine to fetch the PhoneNumberInstance

        :param country_code: The [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the phone number to fetch. This is used to specify the country when the phone number is provided in a national format.
        :param type: The type of information to return. Can be: `carrier` or `caller-name`. The default is null. To retrieve both types of information, specify this parameter twice; once with `carrier` and once with `caller-name` as the value.
        :param add_ons: The `unique_name` of an Add-on you would like to invoke. Can be the `unique_name` of an Add-on that is installed on your account. You can specify multiple instances of this parameter to invoke multiple Add-ons. For more information about  Add-ons, see the [Add-ons documentation](https://www.twilio.com/docs/add-ons).
        :param add_ons_data: Data specific to the add-on you would like to invoke. The content and format of this value depends on the add-on.

        :returns: The fetched PhoneNumberInstance
        """
        return await self._proxy.fetch_async(
            country_code=country_code,
            type=type,
            add_ons=add_ons,
            add_ons_data=add_ons_data,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Lookups.V1.PhoneNumberInstance {}>".format(context)


class PhoneNumberContext(InstanceContext):

    def __init__(self, version: Version, phone_number: str):
        """
        Initialize the PhoneNumberContext

        :param version: Version that contains the resource
        :param phone_number: The phone number to lookup in [E.164](https://www.twilio.com/docs/glossary/what-e164) format, which consists of a + followed by the country code and subscriber number.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "phone_number": phone_number,
        }
        self._uri = "/PhoneNumbers/{phone_number}".format(**self._solution)

    def fetch(
        self,
        country_code: Union[str, object] = values.unset,
        type: Union[List[str], object] = values.unset,
        add_ons: Union[List[str], object] = values.unset,
        add_ons_data: Union[Dict[str, object], object] = values.unset,
    ) -> PhoneNumberInstance:
        """
        Fetch the PhoneNumberInstance

        :param country_code: The [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the phone number to fetch. This is used to specify the country when the phone number is provided in a national format.
        :param type: The type of information to return. Can be: `carrier` or `caller-name`. The default is null. To retrieve both types of information, specify this parameter twice; once with `carrier` and once with `caller-name` as the value.
        :param add_ons: The `unique_name` of an Add-on you would like to invoke. Can be the `unique_name` of an Add-on that is installed on your account. You can specify multiple instances of this parameter to invoke multiple Add-ons. For more information about  Add-ons, see the [Add-ons documentation](https://www.twilio.com/docs/add-ons).
        :param add_ons_data: Data specific to the add-on you would like to invoke. The content and format of this value depends on the add-on.

        :returns: The fetched PhoneNumberInstance
        """

        data = values.of(
            {
                "CountryCode": country_code,
                "Type": serialize.map(type, lambda e: e),
                "AddOns": serialize.map(add_ons, lambda e: e),
            }
        )

        data.update(serialize.prefixed_collapsible_map(add_ons_data, "AddOns"))

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return PhoneNumberInstance(
            self._version,
            payload,
            phone_number=self._solution["phone_number"],
        )

    async def fetch_async(
        self,
        country_code: Union[str, object] = values.unset,
        type: Union[List[str], object] = values.unset,
        add_ons: Union[List[str], object] = values.unset,
        add_ons_data: Union[Dict[str, object], object] = values.unset,
    ) -> PhoneNumberInstance:
        """
        Asynchronous coroutine to fetch the PhoneNumberInstance

        :param country_code: The [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the phone number to fetch. This is used to specify the country when the phone number is provided in a national format.
        :param type: The type of information to return. Can be: `carrier` or `caller-name`. The default is null. To retrieve both types of information, specify this parameter twice; once with `carrier` and once with `caller-name` as the value.
        :param add_ons: The `unique_name` of an Add-on you would like to invoke. Can be the `unique_name` of an Add-on that is installed on your account. You can specify multiple instances of this parameter to invoke multiple Add-ons. For more information about  Add-ons, see the [Add-ons documentation](https://www.twilio.com/docs/add-ons).
        :param add_ons_data: Data specific to the add-on you would like to invoke. The content and format of this value depends on the add-on.

        :returns: The fetched PhoneNumberInstance
        """

        data = values.of(
            {
                "CountryCode": country_code,
                "Type": serialize.map(type, lambda e: e),
                "AddOns": serialize.map(add_ons, lambda e: e),
            }
        )

        data.update(serialize.prefixed_collapsible_map(add_ons_data, "AddOns"))

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return PhoneNumberInstance(
            self._version,
            payload,
            phone_number=self._solution["phone_number"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Lookups.V1.PhoneNumberContext {}>".format(context)


class PhoneNumberList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the PhoneNumberList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self, phone_number: str) -> PhoneNumberContext:
        """
        Constructs a PhoneNumberContext

        :param phone_number: The phone number to lookup in [E.164](https://www.twilio.com/docs/glossary/what-e164) format, which consists of a + followed by the country code and subscriber number.
        """
        return PhoneNumberContext(self._version, phone_number=phone_number)

    def __call__(self, phone_number: str) -> PhoneNumberContext:
        """
        Constructs a PhoneNumberContext

        :param phone_number: The phone number to lookup in [E.164](https://www.twilio.com/docs/glossary/what-e164) format, which consists of a + followed by the country code and subscriber number.
        """
        return PhoneNumberContext(self._version, phone_number=phone_number)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Lookups.V1.PhoneNumberList>"
