r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Api
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class CredentialInstance(InstanceResource):
    """
    :ivar sid: A 34 character string that uniquely identifies this resource.
    :ivar account_sid: The unique id of the Account that is responsible for this resource.
    :ivar credential_list_sid: The unique id that identifies the credential list that includes this credential.
    :ivar username: The username for this credential.
    :ivar date_created: The date that this resource was created, given as GMT in [RFC 2822](https://www.php.net/manual/en/class.datetime.php#datetime.constants.rfc2822) format.
    :ivar date_updated: The date that this resource was last updated, given as GMT in [RFC 2822](https://www.php.net/manual/en/class.datetime.php#datetime.constants.rfc2822) format.
    :ivar uri: The URI for this resource, relative to `https://api.twilio.com`
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        account_sid: str,
        credential_list_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.credential_list_sid: Optional[str] = payload.get("credential_list_sid")
        self.username: Optional[str] = payload.get("username")
        self.date_created: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_updated")
        )
        self.uri: Optional[str] = payload.get("uri")

        self._solution = {
            "account_sid": account_sid,
            "credential_list_sid": credential_list_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[CredentialContext] = None

    @property
    def _proxy(self) -> "CredentialContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: CredentialContext for this CredentialInstance
        """
        if self._context is None:
            self._context = CredentialContext(
                self._version,
                account_sid=self._solution["account_sid"],
                credential_list_sid=self._solution["credential_list_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the CredentialInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the CredentialInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "CredentialInstance":
        """
        Fetch the CredentialInstance


        :returns: The fetched CredentialInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "CredentialInstance":
        """
        Asynchronous coroutine to fetch the CredentialInstance


        :returns: The fetched CredentialInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self, password: Union[str, object] = values.unset
    ) -> "CredentialInstance":
        """
        Update the CredentialInstance

        :param password: The password that the username will use when authenticating SIP requests. The password must be a minimum of 12 characters, contain at least 1 digit, and have mixed case. (eg `IWasAtSignal2018`)

        :returns: The updated CredentialInstance
        """
        return self._proxy.update(
            password=password,
        )

    async def update_async(
        self, password: Union[str, object] = values.unset
    ) -> "CredentialInstance":
        """
        Asynchronous coroutine to update the CredentialInstance

        :param password: The password that the username will use when authenticating SIP requests. The password must be a minimum of 12 characters, contain at least 1 digit, and have mixed case. (eg `IWasAtSignal2018`)

        :returns: The updated CredentialInstance
        """
        return await self._proxy.update_async(
            password=password,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.CredentialInstance {}>".format(context)


class CredentialContext(InstanceContext):

    def __init__(
        self, version: Version, account_sid: str, credential_list_sid: str, sid: str
    ):
        """
        Initialize the CredentialContext

        :param version: Version that contains the resource
        :param account_sid: The unique id of the Account that is responsible for this resource.
        :param credential_list_sid: The unique id that identifies the credential list that includes this credential.
        :param sid: The unique id that identifies the resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "credential_list_sid": credential_list_sid,
            "sid": sid,
        }
        self._uri = "/Accounts/{account_sid}/SIP/CredentialLists/{credential_list_sid}/Credentials/{sid}.json".format(
            **self._solution
        )

    def delete(self) -> bool:
        """
        Deletes the CredentialInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the CredentialInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> CredentialInstance:
        """
        Fetch the CredentialInstance


        :returns: The fetched CredentialInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return CredentialInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            credential_list_sid=self._solution["credential_list_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> CredentialInstance:
        """
        Asynchronous coroutine to fetch the CredentialInstance


        :returns: The fetched CredentialInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return CredentialInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            credential_list_sid=self._solution["credential_list_sid"],
            sid=self._solution["sid"],
        )

    def update(self, password: Union[str, object] = values.unset) -> CredentialInstance:
        """
        Update the CredentialInstance

        :param password: The password that the username will use when authenticating SIP requests. The password must be a minimum of 12 characters, contain at least 1 digit, and have mixed case. (eg `IWasAtSignal2018`)

        :returns: The updated CredentialInstance
        """

        data = values.of(
            {
                "Password": password,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return CredentialInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            credential_list_sid=self._solution["credential_list_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self, password: Union[str, object] = values.unset
    ) -> CredentialInstance:
        """
        Asynchronous coroutine to update the CredentialInstance

        :param password: The password that the username will use when authenticating SIP requests. The password must be a minimum of 12 characters, contain at least 1 digit, and have mixed case. (eg `IWasAtSignal2018`)

        :returns: The updated CredentialInstance
        """

        data = values.of(
            {
                "Password": password,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return CredentialInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            credential_list_sid=self._solution["credential_list_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.CredentialContext {}>".format(context)


class CredentialPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> CredentialInstance:
        """
        Build an instance of CredentialInstance

        :param payload: Payload response from the API
        """
        return CredentialInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            credential_list_sid=self._solution["credential_list_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.CredentialPage>"


class CredentialList(ListResource):

    def __init__(self, version: Version, account_sid: str, credential_list_sid: str):
        """
        Initialize the CredentialList

        :param version: Version that contains the resource
        :param account_sid: The unique id of the Account that is responsible for this resource.
        :param credential_list_sid: The unique id that identifies the credential list that contains the desired credentials.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "credential_list_sid": credential_list_sid,
        }
        self._uri = "/Accounts/{account_sid}/SIP/CredentialLists/{credential_list_sid}/Credentials.json".format(
            **self._solution
        )

    def create(self, username: str, password: str) -> CredentialInstance:
        """
        Create the CredentialInstance

        :param username: The username that will be passed when authenticating SIP requests. The username should be sent in response to Twilio's challenge of the initial INVITE. It can be up to 32 characters long.
        :param password: The password that the username will use when authenticating SIP requests. The password must be a minimum of 12 characters, contain at least 1 digit, and have mixed case. (eg `IWasAtSignal2018`)

        :returns: The created CredentialInstance
        """

        data = values.of(
            {
                "Username": username,
                "Password": password,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return CredentialInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            credential_list_sid=self._solution["credential_list_sid"],
        )

    async def create_async(self, username: str, password: str) -> CredentialInstance:
        """
        Asynchronously create the CredentialInstance

        :param username: The username that will be passed when authenticating SIP requests. The username should be sent in response to Twilio's challenge of the initial INVITE. It can be up to 32 characters long.
        :param password: The password that the username will use when authenticating SIP requests. The password must be a minimum of 12 characters, contain at least 1 digit, and have mixed case. (eg `IWasAtSignal2018`)

        :returns: The created CredentialInstance
        """

        data = values.of(
            {
                "Username": username,
                "Password": password,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return CredentialInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            credential_list_sid=self._solution["credential_list_sid"],
        )

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[CredentialInstance]:
        """
        Streams CredentialInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[CredentialInstance]:
        """
        Asynchronously streams CredentialInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[CredentialInstance]:
        """
        Lists CredentialInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[CredentialInstance]:
        """
        Asynchronously lists CredentialInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> CredentialPage:
        """
        Retrieve a single page of CredentialInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of CredentialInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return CredentialPage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> CredentialPage:
        """
        Asynchronously retrieve a single page of CredentialInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of CredentialInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return CredentialPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> CredentialPage:
        """
        Retrieve a specific page of CredentialInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of CredentialInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return CredentialPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> CredentialPage:
        """
        Asynchronously retrieve a specific page of CredentialInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of CredentialInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return CredentialPage(self._version, response, self._solution)

    def get(self, sid: str) -> CredentialContext:
        """
        Constructs a CredentialContext

        :param sid: The unique id that identifies the resource to update.
        """
        return CredentialContext(
            self._version,
            account_sid=self._solution["account_sid"],
            credential_list_sid=self._solution["credential_list_sid"],
            sid=sid,
        )

    def __call__(self, sid: str) -> CredentialContext:
        """
        Constructs a CredentialContext

        :param sid: The unique id that identifies the resource to update.
        """
        return CredentialContext(
            self._version,
            account_sid=self._solution["account_sid"],
            credential_list_sid=self._solution["credential_list_sid"],
            sid=sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.CredentialList>"
