{% extends "base.html" %}

{% block title %}Simple Safety Heatmap{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Map Column -->
        <div class="col-lg-9 col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-map-marked-alt me-2"></i>
                        SIMPLE SAFETY HEATMAP
                    </h4>
                </div>
                <div class="card-body p-0">
                    <!-- Loading State -->
                    <div id="loading-state" class="text-center p-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3 text-muted">Loading safety data...</p>
                    </div>
                    
                    <!-- Map Container -->
                    <div id="map" style="height: 600px; display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4">
            <!-- Statistics -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="stat-number" id="total-reports">-</div>
                        <div class="stat-label">Total Reports</div>
                    </div>
                    
                    <button id="refresh-btn" class="btn btn-primary w-100">
                        <i class="fas fa-sync-alt"></i> Refresh Data
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #dc3545;
}

.stat-label {
    font-size: 1rem;
    color: #6c757d;
}
</style>

<script>
// Global variables
let map;
let circleLayer;

// Initialize map
function initializeMap() {
    // Create map centered on South Africa
    map = L.map('map').setView([-26.2041, 28.0473], 6);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 18
    }).addTo(map);

    // Create layer group for circles
    circleLayer = L.layerGroup().addTo(map);

    console.log("Map initialized");
    
    // Load data
    loadHeatmapData();
}

// Show loading state
function showLoading() {
    document.getElementById('loading-state').style.display = 'block';
    document.getElementById('map').style.display = 'none';
}

// Hide loading state
function hideLoading() {
    document.getElementById('loading-state').style.display = 'none';
    document.getElementById('map').style.display = 'block';
}

// Clear existing circles
function clearCircles() {
    if (circleLayer) {
        circleLayer.clearLayers();
    }
}

// Add red circles to map
function addRedCircles(coordinates) {
    console.log(`Adding ${coordinates.length} red circles to map`);

    coordinates.forEach(coord => {
        // Create red circle
        const circle = L.circle([coord.lat, coord.lng], {
            color: '#dc3545',
            fillColor: '#dc3545',
            fillOpacity: 0.6,
            radius: 500 // 500 meter radius
        });

        // Add to layer
        circleLayer.addLayer(circle);
    });

    console.log(`Added ${coordinates.length} circles to map`);
}

// Update statistics
function updateStats(totalReports) {
    document.getElementById('total-reports').textContent = totalReports;
}

// Load heatmap data from API
async function loadHeatmapData() {
    try {
        showLoading();
        
        const response = await fetch('/api/reports/heatmap');
        const data = await response.json();
        
        console.log('Heatmap data received:', data);
        
        // Clear existing circles
        clearCircles();
        
        // Add red circles for each coordinate
        if (data.coordinates && data.coordinates.length > 0) {
            addRedCircles(data.coordinates);
            updateStats(data.total_reports);
        } else {
            console.log('No coordinates data received');
            updateStats(0);
        }
        
        hideLoading();
        
    } catch (error) {
        console.error('Error loading heatmap data:', error);
        hideLoading();
        updateStats(0);
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Initialize map
    initializeMap();
    
    // Add event listener for refresh button
    document.getElementById('refresh-btn').addEventListener('click', function() {
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        this.disabled = true;
        
        loadHeatmapData().finally(() => {
            this.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Data';
            this.disabled = false;
        });
    });
});

</script>
{% endblock %}
