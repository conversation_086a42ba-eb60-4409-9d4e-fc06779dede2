{% extends "base.html" %}

{% block title %}Simple Safety Heatmap{% endblock %}

{% block extra_css %}
<style>
    /* Basic styling for the map container */
    #map {
        height: 600px !important;
        width: 100% !important;
        border-radius: 5px;
        z-index: 1;
        border: 2px solid #ddd;
    }

    /* Styling for the container */
    .map-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    /* Legend styling */
    .legend-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .legend-item {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .legend-color {
        display: inline-block;
        width: 20px;
        height: 20px;
        margin-right: 10px;
        border-radius: 50%;
    }

    /* Time filter card styling */
    .time-filter-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .time-filter-container label {
        margin-bottom: 10px;
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<h1 class="project-name">HEAT MAP</h1>

<div class="container-fluid">
    <div class="row">
        <!-- Map Column -->
        <div class="col-lg-9 col-md-8">
            <div class="card">
                <div class="card-body p-0">
                    <!-- Loading State -->
                    <div id="loading-state" class="text-center p-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3 text-muted">Loading safety data...</p>
                    </div>

                    <!-- Map Container -->
                    <div id="map" style="height: 600px; display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4">
            <!-- Statistics -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="stat-number" id="total-reports">-</div>
                        <div class="stat-label">Total Reports</div>
                    </div>

                    <button id="refresh-btn" class="btn w-100">
                        <i class="fas fa-sync-alt"></i> Refresh Data
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #dc3545;
}

.stat-label {
    font-size: 1rem;
    color: #6c757d;
}
</style>

<script>
// Global variables
let map;
let circleLayer;

// Initialize map
function initializeMap() {
    // Show map immediately
    hideLoading();

    // Create map centered on South Africa with faster settings
    map = L.map('map', {
        preferCanvas: true,  // Use canvas for better performance
        zoomControl: true,
        attributionControl: false  // Remove attribution for faster loading
    }).setView([-29.5595, 22.9375], 5);

    // Add faster tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap',
        maxZoom: 16,  // Reduced max zoom for faster loading
        minZoom: 5,   // Set min zoom
        updateWhenIdle: true,  // Only update when map stops moving
        updateWhenZooming: false,  // Don't update while zooming
        keepBuffer: 2  // Keep fewer tiles in memory
    }).addTo(map);

    // Create layer group for circles
    circleLayer = L.layerGroup().addTo(map);

    console.log("Map initialized");

    // Load data immediately without waiting
    loadHeatmapData();
}

// Show loading state
function showLoading() {
    document.getElementById('loading-state').style.display = 'block';
    document.getElementById('map').style.display = 'none';
}

// Hide loading state
function hideLoading() {
    document.getElementById('loading-state').style.display = 'none';
    document.getElementById('map').style.display = 'block';
}

// Clear existing circles
function clearCircles() {
    if (circleLayer) {
        circleLayer.clearLayers();
    }
}

// Add red circles to map (optimized)
function addRedCircles(coordinates) {
    console.log(`Adding ${coordinates.length} red circles to map`);

    // Batch add circles for better performance
    const circles = coordinates.map(coord => {
        return L.circle([coord.lat, coord.lng], {
            color: '#dc3545',
            fillColor: '#dc3545',
            fillOpacity: 0.6,
            radius: 500, // 500 meter radius
            weight: 1,   // Thinner border for faster rendering
            interactive: false  // Disable interactions for faster rendering
        });
    });

    // Add all circles at once
    circles.forEach(circle => circleLayer.addLayer(circle));

    console.log(`Added ${coordinates.length} circles to map`);
}

// Update statistics
function updateStats(totalReports) {
    document.getElementById('total-reports').textContent = totalReports;
}

// Load heatmap data from API (optimized)
async function loadHeatmapData() {
    try {
        // Don't show loading for data - map is already visible
        const response = await fetch('/api/reports/heatmap');
        const data = await response.json();

        console.log('Heatmap data received:', data);

        // Clear existing circles
        clearCircles();

        // Add red circles for each coordinate
        if (data.coordinates && data.coordinates.length > 0) {
            // Use requestAnimationFrame for smooth rendering
            requestAnimationFrame(() => {
                addRedCircles(data.coordinates);
                updateStats(data.total_reports);
            });
        } else {
            console.log('No coordinates data received');
            updateStats(0);
        }

    } catch (error) {
        console.error('Error loading heatmap data:', error);
        updateStats(0);
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Initialize map immediately
    initializeMap();

    // Add event listener for refresh button
    document.getElementById('refresh-btn').addEventListener('click', function() {
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        this.disabled = true;

        loadHeatmapData().finally(() => {
            this.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Data';
            this.disabled = false;
        });
    });
});

// Preload map tiles for faster loading
window.addEventListener('load', function() {
    // Preload some common tile URLs
    const tileUrls = [
        'https://a.tile.openstreetmap.org/6/32/21.png',
        'https://b.tile.openstreetmap.org/6/33/21.png',
        'https://c.tile.openstreetmap.org/6/32/22.png'
    ];

    tileUrls.forEach(url => {
        const img = new Image();
        img.src = url;
    });
});

</script>
{% endblock %}
